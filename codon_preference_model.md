# 密码子偏好性预测模型设计

> 基于ESM2和Transformer的微生物密码子偏好性预测模型

## 1. 整体架构

### 1.1 模型概述
```
蛋白质序列 → ESM2编码器 → 上下文特征 → 密码子预测头 → 密码子概率分布
```

### 1.2 核心组件
- **ESM2特征提取器**: 获取氨基酸的上下文表示
- **密码子约束层**: 确保预测遵循遗传密码表
- **偏好性预测头**: 预测每个合法密码子的概率

## 2. 详细模型设计

### 2.1 密码子对应法则构建
```python
class CodonPreferenceModel(nn.Module):
    def __init__(self, esm2_model, codon_vocab_size=64):
        super().__init__()
        self.esm2 = esm2_model  # 冻结或微调
        self.hidden_size = esm2_model.config.hidden_size
        
        # 构建完整的遗传密码表
        self.codon_table = self._build_genetic_code()
        self.aa_to_codons = self._build_aa_to_codon_mapping()
        self.codon_to_idx = self._build_codon_vocab()
        
        # 预测头 - 注意：这里输出维度必须是64（所有可能密码子）
        self.codon_predictor = nn.Sequential(
            nn.Linear(self.hidden_size, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 64)  # 固定64个密码子
        )
    
    def _build_genetic_code(self):
        """构建标准遗传密码表"""
        genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
            'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        return genetic_code
    
    def _build_aa_to_codon_mapping(self):
        """构建氨基酸到密码子的映射"""
        aa_to_codons = {}
        for codon, aa in self.codon_table.items():
            if aa != '*':  # 排除终止密码子
                if aa not in aa_to_codons:
                    aa_to_codons[aa] = []
                aa_to_codons[aa].append(codon)
        return aa_to_codons
    
    def _build_codon_vocab(self):
        """构建密码子词汇表"""
        all_codons = [codon for codon, aa in self.codon_table.items() if aa != '*']
        return {codon: idx for idx, codon in enumerate(sorted(all_codons))}
```

### 2.2 硬约束密码子预测
```python
def apply_codon_constraints(self, logits, amino_acid_sequence):
    """
    硬约束：每个位置只能预测该氨基酸对应的合法密码子
    这是确保生物学正确性的关键步骤
    """
    batch_size, seq_len, vocab_size = logits.shape
    constrained_logits = torch.full_like(logits, float('-inf'))
    
    for b in range(batch_size):
        for pos in range(seq_len):
            aa = amino_acid_sequence[b][pos]
            
            # 获取该氨基酸对应的所有合法密码子
            if aa in self.aa_to_codons:
                valid_codons = self.aa_to_codons[aa]
                
                # 只保留合法密码子的logits，其他设为-inf
                for codon in valid_codons:
                    codon_idx = self.codon_to_idx[codon]
                    constrained_logits[b, pos, codon_idx] = logits[b, pos, codon_idx]
            
            # 如果遇到未知氨基酸，保持原logits
            else:
                constrained_logits[b, pos, :] = logits[b, pos, :]
    
    return constrained_logits

def get_valid_codon_mask(self, amino_acid):
    """
    为特定氨基酸生成合法密码子的mask
    返回一个长度为64的布尔tensor
    """
    mask = torch.zeros(64, dtype=torch.bool)
    if amino_acid in self.aa_to_codons:
        for codon in self.aa_to_codons[amino_acid]:
            codon_idx = self.codon_to_idx[codon]
            mask[codon_idx] = True
    return mask

def decode_to_codons(self, codon_probs, amino_acid_sequence):
    """
    将概率分布解码为实际的密码子序列
    """
    batch_size, seq_len = codon_probs.shape[:2]
    predicted_codons = []
    
    for b in range(batch_size):
        sequence_codons = []
        for pos in range(seq_len):
            aa = amino_acid_sequence[b][pos]
            
            # 只考虑该氨基酸的合法密码子
            if aa in self.aa_to_codons:
                valid_codons = self.aa_to_codons[aa]
                valid_probs = []
                
                for codon in valid_codons:
                    codon_idx = self.codon_to_idx[codon]
                    valid_probs.append((codon, codon_probs[b, pos, codon_idx].item()))
                
                # 选择概率最高的密码子
                best_codon = max(valid_probs, key=lambda x: x[1])[0]
                sequence_codons.append(best_codon)
            else:
                sequence_codons.append('NNN')  # 未知氨基酸
                
        predicted_codons.append(sequence_codons)
    
    return predicted_codons
```

### 2.3 完整前向传播（带硬约束）
```python
def forward(self, protein_sequence, return_codons=False):
    """
    前向传播，确保输出严格遵循遗传密码表
    """
    # 1. ESM2特征提取 - 获取每个氨基酸的上下文表示
    esm2_output = self.esm2(protein_sequence)
    contextualized_features = esm2_output.last_hidden_state
    
    # 2. 预测所有64个密码子的原始logits
    raw_codon_logits = self.codon_predictor(contextualized_features)
    
    # 3. 应用硬约束 - 这是关键步骤！
    # 只有对应氨基酸的合法密码子才能有非零概率
    constrained_logits = self.apply_codon_constraints(
        raw_codon_logits, protein_sequence
    )
    
    # 4. 计算约束后的概率分布
    codon_probs = F.softmax(constrained_logits, dim=-1)
    
    # 5. 可选：直接解码为密码子序列
    if return_codons:
        predicted_codons = self.decode_to_codons(codon_probs, protein_sequence)
        return codon_probs, constrained_logits, predicted_codons
    
    return codon_probs, constrained_logits

def predict_codon_sequence(self, protein_sequence):
    """
    直接预测蛋白质序列对应的密码子序列
    """
    with torch.no_grad():
        codon_probs, _, predicted_codons = self.forward(
            protein_sequence, return_codons=True
        )
    
    # 将密码子序列连接成DNA序列
    dna_sequences = []
    for seq_codons in predicted_codons:
        dna_seq = ''.join(seq_codons)
        dna_sequences.append(dna_seq)
    
    return dna_sequences, codon_probs
```

## 3. 训练策略

### 3.1 损失函数设计
```python
class CodonPreferenceLoss(nn.Module):
    def __init__(self, alpha=0.8):
        super().__init__()
        self.alpha = alpha
        self.ce_loss = nn.CrossEntropyLoss()
        
    def forward(self, predicted_logits, true_codons, protein_seq):
        # 主要损失：交叉熵
        ce_loss = self.ce_loss(
            predicted_logits.view(-1, 64), 
            true_codons.view(-1)
        )
        
        # 正则化损失：鼓励多样性，避免总是预测最常见密码子
        diversity_loss = self.compute_diversity_loss(predicted_logits)
        
        return self.alpha * ce_loss + (1 - self.alpha) * diversity_loss
```

### 3.2 验证约束正确性
```python
def validate_constraints():
    """验证模型是否严格遵循遗传密码表"""
    model = CodonPreferenceModel(esm2_model)
    
    # 测试序列
    test_protein = "MFVFLVLLPLVSSQCVNLTTRTQLPPA"  # 示例蛋白质序列
    
    # 获取预测
    dna_sequences, codon_probs = model.predict_codon_sequence([test_protein])
    predicted_dna = dna_sequences[0]
    
    # 验证：将预测的DNA翻译回蛋白质
    predicted_codons = [predicted_dna[i:i+3] for i in range(0, len(predicted_dna), 3)]
    back_translated = ''.join([model.codon_table.get(codon, 'X') for codon in predicted_codons])
    
    print(f"原始蛋白质: {test_protein}")
    print(f"预测DNA:   {predicted_dna}")
    print(f"回译蛋白质: {back_translated}")
    print(f"约束正确性: {test_protein == back_translated}")
    
    # 检查每个位置的约束
    for i, (original_aa, predicted_codon) in enumerate(zip(test_protein, predicted_codons)):
        expected_aa = model.codon_table[predicted_codon]
        assert original_aa == expected_aa, f"位置{i}: {original_aa} != {expected_aa}"
    
    print("✓ 所有约束验证通过！")

# 使用示例
validate_constraints()
```
```python
class CodonDataset(Dataset):
    def __init__(self, protein_sequences, dna_sequences):
        self.data = []
        for prot_seq, dna_seq in zip(protein_sequences, dna_sequences):
            # 确保DNA序列长度是3的倍数
            assert len(dna_seq) % 3 == 0
            assert len(prot_seq) * 3 == len(dna_seq)
            
            # 提取密码子
            codons = [dna_seq[i:i+3] for i in range(0, len(dna_seq), 3)]
            codon_indices = [self.codon_to_idx[codon] for codon in codons]
            
            self.data.append({
                'protein': prot_seq,
                'codons': codon_indices
            })
```

## 4. 模型改进方向

### 4.1 多层次特征融合
```python
class MultiLevelCodonPredictor(nn.Module):
    def __init__(self, esm2_model):
        super().__init__()
        self.esm2 = esm2_model
        
        # 使用多层ESM2特征
        self.layer_weights = nn.Parameter(torch.ones(33))  # ESM2有33层
        
        # 多尺度上下文窗口
        self.local_conv = nn.Conv1d(hidden_size, 256, kernel_size=3, padding=1)
        self.global_attention = nn.MultiheadAttention(hidden_size, 8)
        
    def forward(self, x):
        # 获取所有层的特征
        all_layers = self.esm2(x, output_hidden_states=True).hidden_states
        
        # 加权融合多层特征
        weighted_features = sum(w * layer for w, layer in 
                              zip(self.layer_weights, all_layers))
        
        # 局部和全局特征融合
        local_features = self.local_conv(weighted_features.transpose(1, 2))
        global_features, _ = self.global_attention(weighted_features, 
                                                 weighted_features, 
                                                 weighted_features)
        
        return local_features + global_features
```

### 4.2 物种特异性建模
```python
class SpeciesAwareCodonModel(nn.Module):
    def __init__(self, esm2_model, num_species=100):
        super().__init__()
        self.esm2 = esm2_model
        
        # 物种嵌入
        self.species_embedding = nn.Embedding(num_species, 128)
        
        # 物种特异性预测头
        self.species_specific_predictor = nn.ModuleDict({
            f'species_{i}': nn.Linear(hidden_size + 128, 64)
            for i in range(num_species)
        })
        
    def forward(self, protein_seq, species_id):
        esm_features = self.esm2(protein_seq).last_hidden_state
        species_emb = self.species_embedding(species_id)
        
        # 特征融合
        combined_features = torch.cat([
            esm_features, 
            species_emb.unsqueeze(1).expand(-1, esm_features.size(1), -1)
        ], dim=-1)
        
        # 物种特异性预测
        predictor = self.species_specific_predictor[f'species_{species_id}']
        return predictor(combined_features)
```

## 5. 评估指标

### 5.1 准确性指标
- **Top-1准确率**: 预测的最高概率密码子是否正确
- **Top-K准确率**: 真实密码子是否在前K个预测中
- **困惑度**: 模型对真实密码子序列的困惑度

### 5.2 生物学意义指标
- **密码子使用偏差**: 预测的密码子分布与真实分布的KL散度
- **GC含量保持**: 预测序列的GC含量与真实序列的差异
- **翻译效率**: 基于预测密码子的理论翻译效率

## 6. 实现建议

### 6.1 模型训练
1. **预训练ESM2**: 使用冻结的ESM2作为特征提取器
2. **渐进式训练**: 先训练基础版本，再加入复杂特征
3. **数据增强**: 使用同义突变生成更多训练样本

### 6.2 优化技巧
1. **学习率调度**: 使用余弦退火或warm-up策略
2. **梯度裁剪**: 防止梯度爆炸
3. **早停策略**: 基于验证集性能避免过拟合

### 6.3 部署考虑
1. **模型压缩**: 使用知识蒸馏减小模型大小
2. **推理优化**: 批处理和GPU加速
3. **可解释性**: 提供注意力权重分析工具