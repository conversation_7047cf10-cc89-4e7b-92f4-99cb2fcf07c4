#!/usr/bin/env python3
"""
XGBoost Comparison Experiment
Using the fixed data processing to compare XGBoost vs Deep Learning
"""

import numpy as np
import pandas as pd
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.ensemble import RandomForestClassifier
import json
import time
import warnings
warnings.filterwarnings('ignore')

def run_xgboost_experiment():
    """Run XGBoost experiment using pre-processed features"""
    print("🔬 XGBoost vs Deep Learning Comparison Experiment")
    print("=" * 60)
    
    # Check if features exist
    try:
        X = np.load('xgboost_features_fixed.npy')
        y = np.load('xgboost_labels_fixed.npy')
        
        with open('xgboost_codon_mapping_fixed.json', 'r') as f:
            codon_mapping = json.load(f)
        
        print(f"✅ Loaded pre-processed features:")
        print(f"   Feature matrix: {X.shape}")
        print(f"   Labels: {len(y)}")
        print(f"   Classes: {len(np.unique(y))}")
        
    except FileNotFoundError:
        print("❌ Pre-processed features not found. Running fixed data processor...")
        
        # Run the fixed data processor to create features
        from fixed_data_processor import create_xgboost_features_fixed
        X, y, codon_mapping = create_xgboost_features_fixed()
        
        if X is None or len(X) == 0:
            print("❌ Failed to create features")
            return
    
    print(f"\n📊 Dataset Statistics:")
    print(f"   Total samples: {len(X):,}")
    print(f"   Features per sample: {X.shape[1]}")
    print(f"   Unique codons: {len(np.unique(y))}")
    print(f"   Class distribution: {np.bincount(y)[:10]}... (showing first 10)")
    
    # Data split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"\n🔄 Data Split:")
    print(f"   Training: {len(X_train):,} samples")
    print(f"   Testing: {len(X_test):,} samples")
    
    # Feature scaling
    print(f"\n⚙️ Feature Scaling...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # XGBoost Experiment
    print(f"\n🚀 Training XGBoost Model...")
    
    xgb_params = {
        'objective': 'multi:softmax',
        'num_class': len(np.unique(y)),
        'max_depth': 8,
        'learning_rate': 0.1,
        'n_estimators': 300,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'random_state': 42,
        'n_jobs': -1,
        'eval_metric': 'mlogloss'
    }
    
    start_time = time.time()
    xgb_model = xgb.XGBClassifier(**xgb_params)
    xgb_model.fit(X_train_scaled, y_train, verbose=False)
    xgb_train_time = time.time() - start_time
    
    # XGBoost Predictions
    y_pred_xgb = xgb_model.predict(X_test_scaled)
    xgb_accuracy = accuracy_score(y_test, y_pred_xgb)
    
    print(f"✅ XGBoost Results:")
    print(f"   Training time: {xgb_train_time:.2f} seconds")
    print(f"   Test accuracy: {xgb_accuracy:.4f} ({xgb_accuracy*100:.1f}%)")
    
    # Cross-validation
    print(f"\n🔄 Cross-Validation (5-fold)...")
    cv_scores = cross_val_score(xgb_model, X_train_scaled, y_train, cv=5, scoring='accuracy')
    print(f"   CV accuracy: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    print(f"   CV scores: {[f'{score:.3f}' for score in cv_scores]}")
    
    # Random Forest Comparison
    print(f"\n🌲 Training Random Forest for comparison...")
    
    rf_params = {
        'n_estimators': 200,
        'max_depth': 12,
        'min_samples_split': 5,
        'min_samples_leaf': 2,
        'random_state': 42,
        'n_jobs': -1
    }
    
    start_time = time.time()
    rf_model = RandomForestClassifier(**rf_params)
    rf_model.fit(X_train_scaled, y_train)
    rf_train_time = time.time() - start_time
    
    y_pred_rf = rf_model.predict(X_test_scaled)
    rf_accuracy = accuracy_score(y_test, y_pred_rf)
    
    print(f"✅ Random Forest Results:")
    print(f"   Training time: {rf_train_time:.2f} seconds")
    print(f"   Test accuracy: {rf_accuracy:.4f} ({rf_accuracy*100:.1f}%)")
    
    # Feature Importance Analysis
    print(f"\n📈 Feature Importance Analysis (Top 10):")
    feature_names = [
        'AA_hydrophobicity', 'AA_polarity', 'AA_positive', 'AA_negative', 'AA_aromatic',
        'rel_position', 'seq_length',
        'prev_hydrophobicity', 'prev_polarity', 'prev_positive', 'prev_negative', 'prev_aromatic',
        'next_hydrophobicity', 'next_polarity', 'next_positive', 'next_negative', 'next_aromatic'
    ]
    
    importances = xgb_model.feature_importances_
    feature_importance = list(zip(feature_names, importances))
    feature_importance.sort(key=lambda x: x[1], reverse=True)
    
    for i, (name, importance) in enumerate(feature_importance[:10]):
        print(f"   {i+1:2d}. {name:<20} {importance:.4f}")
    
    # Performance Comparison
    print(f"\n🏆 Model Performance Comparison:")
    print(f"{'Model':<20} {'Accuracy':<12} {'Training Time':<15} {'Advantages'}")
    print("-" * 80)
    print(f"{'XGBoost':<20} {xgb_accuracy*100:<11.1f}% {xgb_train_time:<14.1f}s {'Fast, interpretable'}")
    print(f"{'Random Forest':<20} {rf_accuracy*100:<11.1f}% {rf_train_time:<14.1f}s {'Robust, ensemble'}")
    print(f"{'Deep Learning (Best)':<20} {'54.4':<11}% {'Hours':<14} {'End-to-end learning'}")
    
    # Analysis and Recommendations
    print(f"\n🔍 Analysis and Recommendations:")
    
    best_traditional = max(xgb_accuracy, rf_accuracy)
    best_traditional_name = "XGBoost" if xgb_accuracy > rf_accuracy else "Random Forest"
    
    if best_traditional > 0.7:
        print(f"🎯 EXCELLENT: {best_traditional_name} achieves {best_traditional*100:.1f}% accuracy!")
        print(f"   Recommendation: Use {best_traditional_name} as primary model")
        print(f"   Advantages: Fast training, good interpretability, no overfitting")
        
    elif best_traditional > 0.6:
        print(f"✅ GOOD: {best_traditional_name} achieves {best_traditional*100:.1f}% accuracy")
        print(f"   Recommendation: Consider ensemble of {best_traditional_name} + Deep Learning")
        print(f"   Strategy: Use traditional ML for baseline, DL for complex patterns")
        
    elif best_traditional > 0.544:  # Better than current DL
        print(f"📈 IMPROVEMENT: {best_traditional_name} beats Deep Learning!")
        print(f"   Current DL: 54.4% vs {best_traditional_name}: {best_traditional*100:.1f}%")
        print(f"   Recommendation: Switch to {best_traditional_name} or create ensemble")
        
    else:
        print(f"📊 BASELINE: Traditional ML provides good baseline")
        print(f"   {best_traditional_name}: {best_traditional*100:.1f}% vs Deep Learning: 54.4%")
        print(f"   Recommendation: Continue improving Deep Learning approach")
    
    # Detailed Analysis
    print(f"\n💡 Detailed Insights:")
    
    if xgb_accuracy > 0.6:
        print(f"   • XGBoost shows strong performance - feature engineering is effective")
        print(f"   • Traditional ML may be sufficient for this task")
        print(f"   • Consider XGBoost as production model for speed and interpretability")
    
    if abs(xgb_accuracy - rf_accuracy) < 0.02:
        print(f"   • XGBoost and Random Forest perform similarly")
        print(f"   • Ensemble of both could provide additional improvement")
    
    if best_traditional > 0.544:
        print(f"   • Traditional ML outperforms current Deep Learning approach")
        print(f"   • Deep Learning may be overly complex for this feature space")
        print(f"   • Consider hybrid approach: traditional ML + DL ensemble")
    
    # Save results
    results = {
        'xgboost_accuracy': float(xgb_accuracy),
        'xgboost_train_time': float(xgb_train_time),
        'random_forest_accuracy': float(rf_accuracy),
        'random_forest_train_time': float(rf_train_time),
        'cv_mean': float(cv_scores.mean()),
        'cv_std': float(cv_scores.std()),
        'feature_importance': feature_importance,
        'best_traditional_ml': float(best_traditional),
        'recommendation': best_traditional_name if best_traditional > 0.544 else "Continue Deep Learning"
    }
    
    with open('xgboost_experiment_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: xgboost_experiment_results.json")
    
    return xgb_accuracy, rf_accuracy, best_traditional

if __name__ == "__main__":
    try:
        xgb_acc, rf_acc, best_acc = run_xgboost_experiment()
        
        print(f"\n🎯 Final Summary:")
        print(f"   XGBoost: {xgb_acc*100:.1f}%")
        print(f"   Random Forest: {rf_acc*100:.1f}%")
        print(f"   Best Traditional ML: {best_acc*100:.1f}%")
        print(f"   Current Deep Learning: 54.4%")
        
        if best_acc > 0.7:
            print(f"\n🏆 SUCCESS: Traditional ML achieves excellent performance!")
        elif best_acc > 0.544:
            print(f"\n📈 IMPROVEMENT: Traditional ML beats Deep Learning!")
        else:
            print(f"\n📊 BASELINE: Traditional ML provides good comparison baseline")
            
    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        import traceback
        traceback.print_exc()
