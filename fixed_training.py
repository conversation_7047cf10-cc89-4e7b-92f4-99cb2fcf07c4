#!/usr/bin/env python3
"""
修复版密码子偏好性预测模型训练脚本
解决数值不稳定和准确度下降问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import os
import warnings
warnings.filterwarnings('ignore')

from codon_preference_model import CodonPreferenceModel

class FixedCodonPreferenceModel(nn.Module):
    """修复版密码子偏好性预测模型"""
    
    def __init__(self, esm_model_path="facebook/esm2_t33_650M_UR50D"):
        super().__init__()
        
        # 加载ESM2模型
        from transformers import EsmModel, EsmTokenizer
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            self.tokenizer = EsmTokenizer.from_pretrained(local_path)
            self.esm2 = EsmModel.from_pretrained(local_path)
        except:
            self.tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
            self.esm2 = EsmModel.from_pretrained(esm_model_path)
        
        # 冻结ESM2参数
        for param in self.esm2.parameters():
            param.requires_grad = False
            
        self.hidden_size = self.esm2.config.hidden_size
        
        # 构建遗传密码表
        self.genetic_code = self._build_genetic_code()
        self.aa_to_codons = self._build_aa_to_codon_mapping()
        self.codon_to_idx = self._build_codon_vocab()
        
        # 改进的预测头 - 添加归一化和更保守的架构
        self.feature_norm = nn.LayerNorm(self.hidden_size)
        self.dropout = nn.Dropout(0.1)
        
        self.codon_predictor = nn.Sequential(
            nn.Linear(self.hidden_size, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, len(self.codon_to_idx))
        )
        
        # 初始化权重
        self._init_weights()
        
    def _init_weights(self):
        """保守的权重初始化"""
        for module in self.codon_predictor:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)  # 小的初始化
                nn.init.zeros_(module.bias)
    
    def _build_genetic_code(self):
        return {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
            'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def _build_aa_to_codon_mapping(self):
        aa_to_codons = {}
        for codon, aa in self.genetic_code.items():
            if aa != '*':
                if aa not in aa_to_codons:
                    aa_to_codons[aa] = []
                aa_to_codons[aa].append(codon)
        return aa_to_codons
    
    def _build_codon_vocab(self):
        all_codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        return {codon: idx for idx, codon in enumerate(sorted(all_codons))}
    
    def apply_soft_constraints(self, logits, protein_sequences):
        """温和的约束机制 - 使用mask而不是-inf"""
        batch_size, seq_len, vocab_size = logits.shape
        
        # 创建mask
        mask = torch.zeros_like(logits, dtype=torch.bool)
        
        for b in range(batch_size):
            protein_seq = protein_sequences[b]
            for pos in range(min(len(protein_seq), seq_len - 2)):
                if pos + 1 < seq_len:
                    aa = protein_seq[pos]
                    if aa in self.aa_to_codons:
                        valid_codons = self.aa_to_codons[aa]
                        for codon in valid_codons:
                            if codon in self.codon_to_idx:
                                codon_idx = self.codon_to_idx[codon]
                                mask[b, pos + 1, codon_idx] = True
        
        # 应用mask - 使用大负数而不是-inf
        MASK_VALUE = -1e4  # 足够大但不会导致数值问题
        constrained_logits = torch.where(mask, logits, torch.full_like(logits, MASK_VALUE))
        
        return constrained_logits
    
    def forward(self, protein_sequences):
        # Tokenize
        formatted_sequences = [" ".join(list(seq)) for seq in protein_sequences]
        encoded = self.tokenizer(
            formatted_sequences,
            padding=True,
            truncation=True,
            max_length=1024,
            return_tensors="pt"
        )
        
        input_ids = encoded['input_ids'].to(next(self.parameters()).device)
        attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
        
        # ESM2特征提取
        with torch.no_grad():
            esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
        
        # 特征归一化
        features = self.feature_norm(esm_output.last_hidden_state)
        features = self.dropout(features)
        
        # 预测logits
        raw_logits = self.codon_predictor(features)
        
        # 数值稳定性检查
        raw_logits = torch.clamp(raw_logits, min=-50, max=50)
        
        # 应用约束
        constrained_logits = self.apply_soft_constraints(raw_logits, protein_sequences)
        
        return constrained_logits

class StableCodonLoss(nn.Module):
    """数值稳定的损失函数"""
    
    def __init__(self, ignore_index=-100):
        super().__init__()
        self.ignore_index = ignore_index
        
    def forward(self, logits, targets):
        # 数值稳定性检查
        if torch.isnan(logits).any() or torch.isinf(logits).any():
            print("警告: logits包含NaN或Inf")
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        # 使用数值稳定的交叉熵 - 修复张量内存布局问题
        logits_flat = logits.contiguous().view(-1, logits.size(-1))
        targets_flat = targets.contiguous().view(-1)

        loss = F.cross_entropy(
            logits_flat,
            targets_flat,
            ignore_index=self.ignore_index,
            reduction='mean'
        )
        
        # 最终检查
        if torch.isnan(loss) or torch.isinf(loss):
            print("警告: 损失为NaN或Inf")
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        return loss

def calculate_accuracy(logits, targets, ignore_index=-100):
    """计算准确度"""
    with torch.no_grad():
        predictions = torch.argmax(logits, dim=-1)
        mask = (targets != ignore_index)
        correct = (predictions == targets) & mask
        accuracy = correct.sum().float() / mask.sum().float()
        return accuracy.item()

def train_fixed_model():
    """训练修复版模型"""
    print("开始训练修复版密码子偏好性预测模型")
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]  # 进一步限制长度
    print(f"使用 {len(df)} 条数据进行训练")
    
    # 创建修复版模型
    model = FixedCodonPreferenceModel()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 创建简化的数据集
    from improved_training import ImprovedCodonDataset, collate_fn
    dataset = ImprovedCodonDataset(
        df['protein_sequence'].tolist(),
        df['nucleotide_sequence'].tolist(),
        model.codon_to_idx
    )
    
    # 划分数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 数据加载器 - 使用更小的批次
    train_loader = DataLoader(train_dataset, batch_size=2, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=2, shuffle=False, collate_fn=collate_fn)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=1e-6, weight_decay=0.01)  # 更小的学习率
    criterion = StableCodonLoss()
    
    # 学习率调度器 - 使用warmup
    from torch.optim.lr_scheduler import LinearLR, CosineAnnealingLR, SequentialLR
    warmup_scheduler = LinearLR(optimizer, start_factor=0.1, total_iters=10)
    main_scheduler = CosineAnnealingLR(optimizer, T_max=40)
    scheduler = SequentialLR(optimizer, [warmup_scheduler, main_scheduler], milestones=[10])
    
    best_val_loss = float('inf')
    patience = 5
    patience_counter = 0
    
    for epoch in range(50):
        # 训练
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        valid_batches = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/50 [Train]')
        for batch in train_pbar:
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                # 前向传播
                logits = model(proteins)
                
                # 调整维度
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                # 计算损失
                loss = criterion(logits, targets)
                
                # 检查损失值
                if loss.item() > 1000:  # 异常大的损失
                    print(f"跳过异常批次，损失值: {loss.item()}")
                    continue
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                
                if torch.isnan(grad_norm):
                    print("跳过NaN梯度批次")
                    continue
                
                optimizer.step()
                
                # 统计
                train_loss += loss.item()
                train_acc += calculate_accuracy(logits, targets)
                valid_batches += 1
                
                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.2f}',
                    'Acc': f'{calculate_accuracy(logits, targets):.4f}',
                    'GradNorm': f'{grad_norm:.4f}'
                })
                
            except Exception as e:
                print(f"训练批次异常: {e}")
                continue
        
        # 验证
        model.eval()
        val_loss = 0.0
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f'Epoch {epoch+1}/50 [Val]'):
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    loss = criterion(logits, targets)
                    
                    val_loss += loss.item()
                    val_acc += calculate_accuracy(logits, targets)
                    val_batches += 1
                    
                except Exception as e:
                    print(f"验证批次异常: {e}")
                    continue
        
        # 计算平均值
        if valid_batches > 0:
            avg_train_loss = train_loss / valid_batches
            avg_train_acc = train_acc / valid_batches
        else:
            avg_train_loss = float('inf')
            avg_train_acc = 0.0
            
        if val_batches > 0:
            avg_val_loss = val_loss / val_batches
            avg_val_acc = val_acc / val_batches
        else:
            avg_val_loss = float('inf')
            avg_val_acc = 0.0
        
        print(f'Epoch {epoch+1}/50:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_acc:.4f}')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Acc: {avg_val_acc:.4f}')
        print(f'  Valid Batches: Train={valid_batches}, Val={val_batches}')
        
        # 保存检查点
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'val_loss': avg_val_loss,
            'val_acc': avg_val_acc
        }, 'fixed_model_checkpoint.pth')
        
        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': avg_val_loss,
                'val_acc': avg_val_acc
            }, 'fixed_best_model.pth')
            print(f'  保存最佳模型 (Val Loss: {best_val_loss:.4f})')
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            print(f'早停：验证损失连续{patience}轮未改善')
            break
            
        scheduler.step()
        print(f'  Learning Rate: {scheduler.get_last_lr()[0]:.8f}')
        print()

if __name__ == "__main__":
    train_fixed_model()
