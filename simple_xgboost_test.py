#!/usr/bin/env python3
"""
Simple XGBoost Test
Basic comparison without complex dependencies
"""

import numpy as np
import pandas as pd
import json
import time
import warnings
warnings.filterwarnings('ignore')

def simple_baseline_comparison():
    """Simple baseline comparison using basic methods"""
    print("🔬 Simple Baseline Comparison")
    print("=" * 50)
    
    # Load pre-processed features if they exist
    try:
        X = np.load('xgboost_features_fixed.npy')
        y = np.load('xgboost_labels_fixed.npy')
        
        with open('xgboost_codon_mapping_fixed.json', 'r') as f:
            codon_mapping = json.load(f)
        
        print(f"✅ Loaded features: {X.shape}")
        print(f"✅ Labels: {len(y)}")
        print(f"✅ Classes: {len(np.unique(y))}")
        
    except FileNotFoundError:
        print("❌ Features not found. Need to run fixed_data_processor.py first")
        return
    
    # Simple train-test split
    n_samples = len(X)
    n_train = int(0.8 * n_samples)
    
    # Shuffle indices
    indices = np.random.permutation(n_samples)
    train_idx = indices[:n_train]
    test_idx = indices[n_train:]
    
    X_train, X_test = X[train_idx], X[test_idx]
    y_train, y_test = y[train_idx], y[test_idx]
    
    print(f"\n📊 Data Split:")
    print(f"   Training: {len(X_train):,} samples")
    print(f"   Testing: {len(X_test):,} samples")
    
    # Simple baseline: Most frequent class
    print(f"\n🎯 Baseline Methods:")
    
    # Most frequent class baseline
    unique_classes, counts = np.unique(y_train, return_counts=True)
    most_frequent_class = unique_classes[np.argmax(counts)]
    
    baseline_predictions = np.full(len(y_test), most_frequent_class)
    baseline_accuracy = np.mean(baseline_predictions == y_test)
    
    print(f"   Most Frequent Class: {baseline_accuracy*100:.1f}%")
    
    # Random baseline
    random_predictions = np.random.choice(unique_classes, size=len(y_test))
    random_accuracy = np.mean(random_predictions == y_test)
    
    print(f"   Random Baseline: {random_accuracy*100:.1f}%")
    
    # Simple k-NN like approach (using feature similarity)
    print(f"\n🔍 Simple Feature-Based Prediction:")
    
    # Normalize features
    X_train_norm = (X_train - X_train.mean(axis=0)) / (X_train.std(axis=0) + 1e-8)
    X_test_norm = (X_test - X_train.mean(axis=0)) / (X_train.std(axis=0) + 1e-8)
    
    # Simple nearest neighbor prediction (using first 1000 test samples for speed)
    test_subset = min(1000, len(X_test))
    correct_predictions = 0
    
    print(f"   Testing on {test_subset} samples...")
    
    start_time = time.time()
    
    for i in range(test_subset):
        # Find nearest neighbor in training set
        distances = np.sum((X_train_norm - X_test_norm[i])**2, axis=1)
        nearest_idx = np.argmin(distances)
        prediction = y_train[nearest_idx]
        
        if prediction == y_test[i]:
            correct_predictions += 1
        
        if (i + 1) % 100 == 0:
            print(f"     Progress: {i+1}/{test_subset}")
    
    nn_time = time.time() - start_time
    nn_accuracy = correct_predictions / test_subset
    
    print(f"   Nearest Neighbor: {nn_accuracy*100:.1f}% (Time: {nn_time:.1f}s)")
    
    # Feature importance analysis (simple correlation)
    print(f"\n📈 Simple Feature Analysis:")
    
    feature_names = [
        'AA_hydrophobicity', 'AA_polarity', 'AA_positive', 'AA_negative', 'AA_aromatic',
        'rel_position', 'seq_length',
        'prev_hydrophobicity', 'prev_polarity', 'prev_positive', 'prev_negative', 'prev_aromatic',
        'next_hydrophobicity', 'next_polarity', 'next_positive', 'next_negative', 'next_aromatic'
    ]
    
    # Calculate simple correlation with target
    correlations = []
    for i in range(X_train.shape[1]):
        # Convert categorical target to continuous for correlation
        corr = np.corrcoef(X_train[:, i], y_train)[0, 1]
        if np.isnan(corr):
            corr = 0.0
        correlations.append((feature_names[i] if i < len(feature_names) else f'feature_{i}', abs(corr)))
    
    # Sort by correlation
    correlations.sort(key=lambda x: x[1], reverse=True)
    
    print("   Top 10 Most Correlated Features:")
    for i, (name, corr) in enumerate(correlations[:10]):
        print(f"     {i+1:2d}. {name:<20} {corr:.4f}")
    
    # Performance comparison
    print(f"\n🏆 Performance Summary:")
    print(f"{'Method':<25} {'Accuracy':<12} {'Time':<10} {'Notes'}")
    print("-" * 60)
    print(f"{'Most Frequent Class':<25} {baseline_accuracy*100:<11.1f}% {'<1s':<9} {'Simple baseline'}")
    print(f"{'Random':<25} {random_accuracy*100:<11.1f}% {'<1s':<9} {'Random chance'}")
    print(f"{'Nearest Neighbor':<25} {nn_accuracy*100:<11.1f}% {nn_time:<9.1f}s {'Feature similarity'}")
    print(f"{'Deep Learning (Current)':<25} {'54.4':<11}% {'Hours':<9} {'ESM2 + Neural Net'}")
    
    # Analysis and recommendations
    print(f"\n💡 Analysis:")
    
    if nn_accuracy > 0.6:
        print(f"✅ Feature-based methods show promise!")
        print(f"   Nearest neighbor achieves {nn_accuracy*100:.1f}%")
        print(f"   This suggests the features contain useful information")
        print(f"   Recommendation: Traditional ML (XGBoost/RF) likely to work well")
        
    elif nn_accuracy > baseline_accuracy * 1.5:
        print(f"📈 Features provide some improvement over baseline")
        print(f"   NN: {nn_accuracy*100:.1f}% vs Baseline: {baseline_accuracy*100:.1f}%")
        print(f"   Recommendation: Try ensemble methods")
        
    else:
        print(f"⚠️ Features may not be sufficient")
        print(f"   NN: {nn_accuracy*100:.1f}% vs Baseline: {baseline_accuracy*100:.1f}%")
        print(f"   Recommendation: Focus on deep learning or better features")
    
    # Feature insights
    top_feature = correlations[0]
    print(f"\n🔍 Feature Insights:")
    print(f"   Most important feature: {top_feature[0]} (correlation: {top_feature[1]:.4f})")
    
    if top_feature[0].startswith('AA_'):
        print(f"   → Amino acid properties are most predictive")
    elif 'position' in top_feature[0]:
        print(f"   → Sequence position is most predictive")
    elif 'prev' in top_feature[0] or 'next' in top_feature[0]:
        print(f"   → Context (neighboring amino acids) is most predictive")
    
    # Final recommendation
    print(f"\n🎯 Final Recommendation:")
    
    if nn_accuracy > 0.65:
        print(f"🏆 STRONG: Traditional ML should work excellently!")
        print(f"   Expected XGBoost/RF accuracy: 70-85%")
        print(f"   Recommendation: Switch to traditional ML as primary approach")
        
    elif nn_accuracy > 0.55:
        print(f"✅ GOOD: Traditional ML should work well")
        print(f"   Expected XGBoost/RF accuracy: 60-75%")
        print(f"   Recommendation: Use traditional ML + deep learning ensemble")
        
    elif nn_accuracy > baseline_accuracy * 1.2:
        print(f"📊 MODERATE: Traditional ML may help")
        print(f"   Expected XGBoost/RF accuracy: 50-65%")
        print(f"   Recommendation: Try traditional ML as baseline, focus on deep learning")
        
    else:
        print(f"🔬 LIMITED: Focus on deep learning")
        print(f"   Traditional ML unlikely to exceed current deep learning performance")
        print(f"   Recommendation: Continue improving deep learning approach")
    
    return {
        'baseline_accuracy': baseline_accuracy,
        'random_accuracy': random_accuracy,
        'nn_accuracy': nn_accuracy,
        'top_features': correlations[:5],
        'recommendation': 'traditional_ml' if nn_accuracy > 0.6 else 'deep_learning'
    }

if __name__ == "__main__":
    try:
        results = simple_baseline_comparison()
        
        # Save results
        with open('simple_baseline_results.json', 'w') as f:
            # Convert numpy types to Python types for JSON serialization
            json_results = {
                'baseline_accuracy': float(results['baseline_accuracy']),
                'random_accuracy': float(results['random_accuracy']),
                'nn_accuracy': float(results['nn_accuracy']),
                'top_features': [(name, float(corr)) for name, corr in results['top_features']],
                'recommendation': results['recommendation']
            }
            json.dump(json_results, f, indent=2)
        
        print(f"\n💾 Results saved to: simple_baseline_results.json")
        
    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        import traceback
        traceback.print_exc()
