#!/usr/bin/env python3
"""
抗过拟合训练脚本
解决训练集60.6%，验证集49.1%的过拟合问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import matplotlib.pyplot as plt
import gc
import os
import time
warnings.filterwarnings('ignore')

from fixed_training import FixedCodonPreferenceModel, calculate_accuracy
from improved_training import ImprovedCodonDataset, collate_fn

class AntiOverfittingModel(nn.Module):
    """抗过拟合的密码子预测模型"""
    
    def __init__(self, esm_model_path="facebook/esm2_t33_650M_UR50D"):
        super().__init__()
        
        # 加载ESM2模型
        from transformers import EsmModel, EsmTokenizer
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            self.tokenizer = EsmTokenizer.from_pretrained(local_path)
            self.esm2 = EsmModel.from_pretrained(local_path)
        except:
            self.tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
            self.esm2 = EsmModel.from_pretrained(esm_model_path)
        
        # 冻结ESM2参数
        for param in self.esm2.parameters():
            param.requires_grad = False
            
        self.hidden_size = self.esm2.config.hidden_size
        
        # 构建遗传密码表
        self.genetic_code = self._build_genetic_code()
        self.aa_to_codons = self._build_aa_to_codon_mapping()
        self.codon_to_idx = self._build_codon_vocab()
        
        # 大幅增强正则化的预测头
        self.feature_norm = nn.LayerNorm(self.hidden_size)
        self.input_dropout = nn.Dropout(0.3)  # 增加输入dropout
        
        # 简化架构，减少过拟合
        self.codon_predictor = nn.Sequential(
            nn.Linear(self.hidden_size, 512),
            nn.LayerNorm(512),
            nn.ReLU(),
            nn.Dropout(0.4),  # 增加dropout
            
            nn.Linear(512, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Dropout(0.4),
            
            nn.Linear(256, len(self.codon_to_idx))
        )
        
        # 权重初始化
        self._init_weights()
        
    def _init_weights(self):
        """更保守的权重初始化"""
        for module in self.codon_predictor:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.05)  # 更小的初始化
                nn.init.zeros_(module.bias)
    
    def _build_genetic_code(self):
        return {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
            'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def _build_aa_to_codon_mapping(self):
        aa_to_codons = {}
        for codon, aa in self.genetic_code.items():
            if aa != '*':
                if aa not in aa_to_codons:
                    aa_to_codons[aa] = []
                aa_to_codons[aa].append(codon)
        return aa_to_codons
    
    def _build_codon_vocab(self):
        codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        return {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    def forward(self, protein_sequences):
        """前向传播"""
        # 编码蛋白质序列
        encoded = self.tokenizer(
            protein_sequences,
            padding=True,
            truncation=True,
            max_length=1024,
            return_tensors="pt"
        )
        
        input_ids = encoded['input_ids'].to(next(self.parameters()).device)
        attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
        
        # ESM2特征提取
        with torch.no_grad():
            esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
        
        # 特征处理 - 增强正则化
        features = self.feature_norm(esm_output.last_hidden_state)
        features = self.input_dropout(features)
        
        # 预测logits
        logits = self.codon_predictor(features)
        
        # 数值稳定性
        logits = torch.clamp(logits, min=-10, max=10)  # 更严格的裁剪
        
        return logits

class RegularizedLoss(nn.Module):
    """带正则化的损失函数"""
    
    def __init__(self, class_weights=None, ignore_index=-100, label_smoothing=0.1):
        super().__init__()
        self.ignore_index = ignore_index
        self.label_smoothing = label_smoothing
        if class_weights is not None:
            self.register_buffer('class_weights', class_weights)
        else:
            self.class_weights = None
    
    def forward(self, logits, targets):
        """计算正则化损失"""
        logits = logits.contiguous()
        targets = targets.contiguous()
        
        batch_size, seq_len, num_classes = logits.shape
        logits_flat = logits.view(-1, num_classes)
        targets_flat = targets.view(-1)
        
        # 标签平滑
        if self.label_smoothing > 0:
            # 创建平滑标签
            smooth_targets = torch.zeros_like(logits_flat)
            smooth_targets.fill_(self.label_smoothing / (num_classes - 1))
            
            # 有效目标mask
            valid_mask = (targets_flat != self.ignore_index)
            valid_targets = targets_flat[valid_mask]
            
            if len(valid_targets) > 0:
                smooth_targets[valid_mask, valid_targets] = 1.0 - self.label_smoothing
                
                # 计算KL散度损失
                log_probs = F.log_softmax(logits_flat[valid_mask], dim=-1)
                loss = F.kl_div(log_probs, smooth_targets[valid_mask], reduction='batchmean')
            else:
                loss = torch.tensor(0.0, device=logits.device, requires_grad=True)
        else:
            # 标准交叉熵
            if self.class_weights is not None:
                loss = F.cross_entropy(
                    logits_flat, targets_flat, 
                    weight=self.class_weights,
                    ignore_index=self.ignore_index,
                    reduction='mean'
                )
            else:
                loss = F.cross_entropy(
                    logits_flat, targets_flat, 
                    ignore_index=self.ignore_index,
                    reduction='mean'
                )
        
        return loss

def train_anti_overfitting_model():
    """抗过拟合训练"""
    print("🛡️ 开始抗过拟合训练")
    print("目标: 解决过拟合，提升验证集准确度到80%")
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]
    print(f"使用 {len(df)} 条数据")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # 创建抗过拟合模型
    model = AntiOverfittingModel()
    model = model.to(device)
    
    # 数据集
    dataset = ImprovedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    
    # 更严格的数据划分 - 增加验证集比例
    train_size = int(0.7 * len(dataset))  # 减少训练集
    val_size = len(dataset) - train_size   # 增加验证集
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    print(f"训练集: {len(train_dataset)}, 验证集: {len(val_dataset)}")
    
    # 数据加载器 - 减小批次大小
    batch_size = 8  # 更小的批次
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)
    
    # 加载权重
    try:
        with open('pure_codon_weights.json', 'r') as f:
            weight_info = json.load(f)
        codon_weights = weight_info['mixed_weights']
        weight_tensor = torch.ones(len(model.codon_to_idx))
        
        for codon, weight in codon_weights.items():
            if codon in model.codon_to_idx:
                idx = model.codon_to_idx[codon]
                weight_tensor[idx] = weight
        
        weight_tensor = weight_tensor / weight_tensor.mean()
        weight_tensor = weight_tensor.to(device)
    except:
        weight_tensor = None
    
    # 损失函数 - 增加标签平滑
    criterion = RegularizedLoss(class_weights=weight_tensor, label_smoothing=0.1)
    
    # 优化器 - 更小的学习率和更强的权重衰减
    optimizer = optim.AdamW(model.parameters(), lr=5e-6, weight_decay=0.1)
    
    # 学习率调度器 - 更激进的衰减
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=10, verbose=True
    )
    
    # 训练历史
    train_losses, val_losses = [], []
    train_accuracies, val_accuracies = [], []
    
    best_val_acc = 0.0
    patience = 50
    patience_counter = 0
    
    print("\n🎯 开始训练...")
    
    for epoch in range(500):  # 更多轮次
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc=f'Epoch {epoch+1:3d}/500 [Train]', leave=False):
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                logits = model(proteins)
                
                # 对齐维度
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                loss = criterion(logits, targets)
                
                if torch.isnan(loss) or torch.isinf(loss):
                    continue
                
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
                
                optimizer.step()
                
                train_loss += loss.item()
                train_acc += calculate_accuracy(logits, targets)
                train_batches += 1
                
            except Exception as e:
                print(f"训练异常: {e}")
                continue
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f'Epoch {epoch+1:3d}/500 [Val]', leave=False):
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    loss = criterion(logits, targets)
                    acc = calculate_accuracy(logits, targets)
                    
                    val_loss += loss.item()
                    val_acc += acc
                    val_batches += 1
                    
                except Exception as e:
                    continue
        
        # 计算平均值
        avg_train_loss = train_loss / max(train_batches, 1)
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_loss = val_loss / max(val_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        
        # 记录历史
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        train_accuracies.append(avg_train_acc)
        val_accuracies.append(avg_val_acc)
        
        # 学习率调度
        scheduler.step(avg_val_acc)
        
        # 计算过拟合程度
        overfitting_gap = avg_train_acc - avg_val_acc
        
        print(f'Epoch {epoch+1:3d}/500:')
        print(f'  Train: Loss={avg_train_loss:.4f}, Acc={avg_train_acc*100:.1f}%')
        print(f'  Val:   Loss={avg_val_loss:.4f}, Acc={avg_val_acc*100:.1f}%')
        print(f'  Gap:   {overfitting_gap*100:.1f}% (过拟合程度)')
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_accuracy': avg_val_acc,
                'train_accuracy': avg_train_acc,
                'overfitting_gap': overfitting_gap
            }, 'anti_overfitting_best_model.pth')
            print(f'  💾 保存最佳模型 (验证准确度: {best_val_acc*100:.1f}%)')
            
            if avg_val_acc >= 0.8:
                print("🎉 达到80%目标！")
                break
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= patience:
            print(f'早停: 验证准确度连续{patience}轮未改善')
            break
        
        # 内存清理
        if (epoch + 1) % 10 == 0:
            gc.collect()
            torch.cuda.empty_cache()
    
    print(f"\n🏁 训练完成！最佳验证准确度: {best_val_acc*100:.1f}%")
    return best_val_acc

if __name__ == "__main__":
    best_acc = train_anti_overfitting_model()
    
    if best_acc >= 0.8:
        print("🎉 成功达到80%目标准确度！")
    else:
        print(f"当前最佳准确度: {best_acc*100:.1f}%")
        print("建议尝试方案2: 数据增强和特征工程")
