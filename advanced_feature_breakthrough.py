#!/usr/bin/env python3
"""
高级特征工程突破模型
Advanced Feature Engineering Breakthrough Model

目标：通过高级特征工程将准确度从55%提升到70%+
策略：
1. 生物学相关特征（密码子使用偏好、GC含量等）
2. 扩展上下文特征
3. 集成学习方法
4. 高级机器学习算法
"""

import numpy as np
import pandas as pd
import json
import time
import warnings
from collections import Counter
warnings.filterwarnings('ignore')

# 导入机器学习库
try:
    import xgboost as xgb
    from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
    from sklearn.preprocessing import StandardScaler, RobustScaler
    from sklearn.metrics import accuracy_score, classification_report
    from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier, VotingClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.neural_network import MLPClassifier
    SKLEARN_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 机器学习库不可用: {e}")
    SKLEARN_AVAILABLE = False

class AdvancedFeatureExtractor:
    """高级特征提取器"""
    
    def __init__(self):
        # 密码子使用频率（基于大肠杆菌优化）
        self.codon_usage = {
            'TTT': 0.58, 'TTC': 0.42, 'TTA': 0.14, 'TTG': 0.13,
            'TCT': 0.17, 'TCC': 0.15, 'TCA': 0.14, 'TCG': 0.14,
            'TAT': 0.59, 'TAC': 0.41, 'TGT': 0.46, 'TGC': 0.54, 'TGG': 1.00,
            'CTT': 0.12, 'CTC': 0.10, 'CTA': 0.04, 'CTG': 0.47,
            'CCT': 0.18, 'CCC': 0.13, 'CCA': 0.20, 'CCG': 0.49,
            'CAT': 0.57, 'CAC': 0.43, 'CAA': 0.34, 'CAG': 0.66,
            'CGT': 0.36, 'CGC': 0.36, 'CGA': 0.07, 'CGG': 0.11,
            'ATT': 0.49, 'ATC': 0.39, 'ATA': 0.11, 'ATG': 1.00,
            'ACT': 0.19, 'ACC': 0.40, 'ACA': 0.17, 'ACG': 0.25,
            'AAT': 0.49, 'AAC': 0.51, 'AAA': 0.74, 'AAG': 0.26,
            'AGT': 0.16, 'AGC': 0.25, 'AGA': 0.07, 'AGG': 0.04,
            'GTT': 0.28, 'GTC': 0.20, 'GTA': 0.17, 'GTG': 0.35,
            'GCT': 0.18, 'GCC': 0.26, 'GCA': 0.23, 'GCG': 0.33,
            'GAT': 0.63, 'GAC': 0.37, 'GAA': 0.68, 'GAG': 0.32,
            'GGT': 0.35, 'GGC': 0.37, 'GGA': 0.13, 'GGG': 0.15
        }
        
        # 扩展氨基酸属性
        self.aa_properties = {
            'A': [1.8, 0, 0, 0, 0, 89.1, 6.0, 0.74, 1.28],    # 疏水性, 极性, 正电, 负电, 芳香性, 分子量, pKa, 体积, 可及性
            'R': [-4.5, 1, 1, 0, 0, 174.2, 12.5, 2.65, 2.34],
            'N': [-3.5, 1, 0, 0, 0, 132.1, 8.8, 1.60, 1.96],
            'D': [-3.5, 1, 0, 1, 0, 133.1, 3.9, 1.60, 1.90],
            'C': [2.5, 0, 0, 0, 0, 121.0, 8.3, 1.54, 1.48],
            'Q': [-3.5, 1, 0, 0, 0, 146.1, 9.1, 1.89, 2.07],
            'E': [-3.5, 1, 0, 1, 0, 147.1, 4.3, 1.56, 2.02],
            'G': [-0.4, 0, 0, 0, 0, 75.1, 6.0, 0.00, 1.00],
            'H': [-3.2, 1, 1, 0, 1, 155.2, 6.0, 2.99, 2.07],
            'I': [4.5, 0, 0, 0, 0, 131.2, 6.0, 4.19, 1.81],
            'L': [3.8, 0, 0, 0, 0, 131.2, 6.0, 2.52, 1.81],
            'K': [-3.9, 1, 1, 0, 0, 146.2, 10.5, 2.80, 2.25],
            'M': [1.9, 0, 0, 0, 0, 149.2, 5.7, 2.35, 1.94],
            'F': [2.8, 0, 0, 0, 1, 165.2, 5.5, 2.94, 2.27],
            'P': [-1.6, 0, 0, 0, 0, 115.1, 6.3, 1.66, 1.46],
            'S': [-0.8, 1, 0, 0, 0, 105.1, 5.7, 1.31, 1.31],
            'T': [-0.7, 1, 0, 0, 0, 119.1, 5.6, 1.21, 1.50],
            'W': [-0.9, 0, 0, 0, 1, 204.2, 5.9, 3.21, 2.66],
            'Y': [-1.3, 1, 0, 0, 1, 181.2, 10.1, 2.94, 2.35],
            'V': [4.2, 0, 0, 0, 0, 117.1, 6.0, 3.02, 1.58],
        }
        
        # 遗传密码
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        # 密码子稳定性评分
        self.codon_stability = {}
        for codon in self.genetic_code.keys():
            # 基于GC含量和wobble位置的稳定性
            gc_content = (codon.count('G') + codon.count('C')) / 3
            wobble_stable = 1 if codon[2] in 'GC' else 0
            self.codon_stability[codon] = gc_content * 0.7 + wobble_stable * 0.3
    
    def extract_comprehensive_features(self, protein_seq, nucleotide_seq, position):
        """提取综合特征"""
        features = []
        
        # 1. 基本氨基酸特征（9维）
        aa = protein_seq[position]
        aa_props = self.aa_properties.get(aa, [0] * 9)
        features.extend(aa_props)
        
        # 2. 位置特征（3维）
        rel_pos = position / len(protein_seq)
        abs_pos = position
        seq_length = len(protein_seq)
        features.extend([rel_pos, abs_pos, seq_length])
        
        # 3. 扩展上下文特征（±7位置，每个位置4个主要属性）
        context_size = 7
        for offset in range(-context_size, context_size + 1):
            if offset == 0:
                continue
            ctx_pos = position + offset
            if 0 <= ctx_pos < len(protein_seq):
                ctx_aa = protein_seq[ctx_pos]
                ctx_props = self.aa_properties.get(ctx_aa, [0] * 9)
                features.extend(ctx_props[:4])  # 疏水性, 极性, 正电, 负电
            else:
                features.extend([0, 0, 0, 0])
        
        # 4. 密码子相关特征（6维）
        codon_start = position * 3
        if codon_start + 3 <= len(nucleotide_seq):
            codon = nucleotide_seq[codon_start:codon_start + 3]
            
            # GC含量
            gc_content = (codon.count('G') + codon.count('C')) / 3
            features.append(gc_content)
            
            # 密码子使用偏好
            codon_usage = self.codon_usage.get(codon, 0.5)
            features.append(codon_usage)
            
            # 密码子稳定性
            stability = self.codon_stability.get(codon, 0.5)
            features.append(stability)
            
            # Wobble位置特征
            wobble_gc = 1 if codon[2] in 'GC' else 0
            wobble_at = 1 if codon[2] in 'AT' else 0
            features.extend([wobble_gc, wobble_at])
            
            # 密码子第一、二位特征
            first_gc = 1 if codon[0] in 'GC' else 0
            features.append(first_gc)
        else:
            features.extend([0.5, 0.5, 0.5, 0, 0, 0])
        
        # 5. 局部序列特征（4维）
        window_size = 15  # ±15bp窗口
        window_start = max(0, codon_start - window_size)
        window_end = min(len(nucleotide_seq), codon_start + window_size + 3)
        window_seq = nucleotide_seq[window_start:window_end]
        
        if len(window_seq) > 0:
            local_gc = (window_seq.count('G') + window_seq.count('C')) / len(window_seq)
            local_at = (window_seq.count('A') + window_seq.count('T')) / len(window_seq)
            features.extend([local_gc, local_at])
            
            # 序列复杂度
            base_counts = [window_seq.count(base) for base in 'ATGC']
            entropy = -sum([(c/len(window_seq)) * np.log2(c/len(window_seq) + 1e-10) for c in base_counts if c > 0])
            features.append(entropy)
            
            # 重复序列检测
            repeat_score = len(set(window_seq)) / len(window_seq) if len(window_seq) > 0 else 1
            features.append(repeat_score)
        else:
            features.extend([0.5, 0.5, 2.0, 1.0])
        
        # 6. 蛋白质全局特征（5维）
        # 整体疏水性
        global_hydrophobicity = np.mean([self.aa_properties.get(aa, [0]*9)[0] for aa in protein_seq])
        features.append(global_hydrophobicity)
        
        # 整体电荷
        positive_count = sum(1 for aa in protein_seq if self.aa_properties.get(aa, [0]*9)[2] > 0)
        negative_count = sum(1 for aa in protein_seq if self.aa_properties.get(aa, [0]*9)[3] > 0)
        net_charge = (positive_count - negative_count) / len(protein_seq)
        features.append(net_charge)
        
        # 芳香性氨基酸比例
        aromatic_ratio = sum(1 for aa in protein_seq if self.aa_properties.get(aa, [0]*9)[4] > 0) / len(protein_seq)
        features.append(aromatic_ratio)
        
        # 极性氨基酸比例
        polar_ratio = sum(1 for aa in protein_seq if self.aa_properties.get(aa, [0]*9)[1] > 0) / len(protein_seq)
        features.append(polar_ratio)
        
        # 分子量分布
        molecular_weights = [self.aa_properties.get(aa, [0]*9)[5] for aa in protein_seq]
        avg_mw = np.mean(molecular_weights)
        features.append(avg_mw)
        
        return features

def create_advanced_features():
    """创建高级特征"""
    print("🔧 创建高级特征工程数据")
    print("=" * 50)
    
    # 加载数据
    try:
        df = pd.read_csv("processed_BL21_data.csv")
        print(f"✅ 加载 {len(df)} 个样本")
    except FileNotFoundError:
        print("❌ processed_BL21_data.csv 未找到")
        return None, None, None
    
    # 过滤数据
    df = df[df['protein_length'] <= 512]
    print(f"✅ 使用 {len(df)} 个样本 (长度 <= 512)")
    
    # 创建特征提取器
    extractor = AdvancedFeatureExtractor()
    
    # 创建密码子映射
    codons = [codon for codon, aa in extractor.genetic_code.items() if aa != '*']
    codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    print(f"✅ 密码子类别数: {len(codon_to_idx)}")
    
    # 提取特征和标签
    features = []
    labels = []
    
    successful_samples = 0
    total_positions = 0
    
    print("🔄 提取特征中...")
    
    for idx, row in df.iterrows():
        try:
            protein_seq = row['protein_sequence']
            nucleotide_seq = row['nucleotide_sequence']
            
            # 基本验证
            if len(nucleotide_seq) % 3 != 0:
                continue
            
            # 提取密码子
            codons_in_seq = [nucleotide_seq[i:i+3] for i in range(0, len(nucleotide_seq), 3)]
            
            # 移除终止密码子
            if len(codons_in_seq) > len(protein_seq):
                codons_in_seq = codons_in_seq[:len(protein_seq)]
            
            if len(codons_in_seq) != len(protein_seq):
                continue
            
            # 处理每个位置
            sample_features = []
            sample_labels = []
            
            for pos, (aa, codon) in enumerate(zip(protein_seq, codons_in_seq)):
                if codon not in codon_to_idx:
                    continue
                if aa not in extractor.aa_properties:
                    continue
                
                # 验证遗传密码一致性
                expected_aa = extractor.genetic_code.get(codon, 'X')
                if expected_aa != aa:
                    continue
                
                # 提取综合特征
                feature_vector = extractor.extract_comprehensive_features(
                    protein_seq, nucleotide_seq, pos
                )
                
                sample_features.append(feature_vector)
                sample_labels.append(codon_to_idx[codon])
                total_positions += 1
            
            if len(sample_features) > 0:
                features.extend(sample_features)
                labels.extend(sample_labels)
                successful_samples += 1
                
                if successful_samples % 500 == 0:
                    print(f"   处理了 {successful_samples} 个样本...")
                    
        except Exception as e:
            continue
    
    print(f"✅ 成功处理 {successful_samples} 个样本")
    print(f"✅ 创建 {len(features)} 个特征向量")
    print(f"✅ 总位置数: {total_positions}")
    
    if len(features) == 0:
        return None, None, None
    
    X = np.array(features)
    y = np.array(labels)
    
    print(f"✅ 特征矩阵形状: {X.shape}")
    print(f"✅ 标签形状: {y.shape}")
    print(f"✅ 特征维度: {X.shape[1]}")
    
    return X, y, codon_to_idx

def train_advanced_breakthrough_model():
    """训练高级突破模型"""
    print("\n🚀 高级特征工程突破模型训练")
    print("目标：通过高级特征工程将准确度从55%提升到70%+")
    print("=" * 70)

    if not SKLEARN_AVAILABLE:
        print("❌ 机器学习库不可用，无法运行")
        return

    # 创建高级特征
    X, y, codon_mapping = create_advanced_features()

    if X is None:
        print("❌ 特征创建失败")
        return

    print(f"\n📊 数据集统计:")
    print(f"   总样本数: {len(X):,}")
    print(f"   特征维度: {X.shape[1]}")
    print(f"   类别数: {len(np.unique(y))}")
    print(f"   类别分布: {np.bincount(y)[:10]}... (显示前10个)")

    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )

    print(f"\n🔄 数据分割:")
    print(f"   训练集: {len(X_train):,} 样本")
    print(f"   测试集: {len(X_test):,} 样本")

    # 特征缩放
    print(f"\n⚙️ 特征缩放...")
    scaler = RobustScaler()  # 使用RobustScaler处理异常值
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # 创建多个高级模型
    print(f"\n🏗️ 创建高级模型集合...")

    models = {
        'XGBoost_Advanced': xgb.XGBClassifier(
            objective='multi:softmax',
            num_class=len(np.unique(y)),
            max_depth=10,
            learning_rate=0.05,
            n_estimators=500,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=0.1,
            random_state=42,
            n_jobs=-1,
            eval_metric='mlogloss'
        ),

        'RandomForest_Advanced': RandomForestClassifier(
            n_estimators=300,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            bootstrap=True,
            random_state=42,
            n_jobs=-1
        ),

        'ExtraTrees_Advanced': ExtraTreesClassifier(
            n_estimators=300,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            max_features='sqrt',
            bootstrap=True,
            random_state=42,
            n_jobs=-1
        ),

        'MLP_Advanced': MLPClassifier(
            hidden_layer_sizes=(512, 256, 128),
            activation='relu',
            solver='adam',
            alpha=0.001,
            learning_rate='adaptive',
            max_iter=300,
            random_state=42,
            early_stopping=True,
            validation_fraction=0.1
        )
    }

    # 训练和评估每个模型
    results = {}
    trained_models = {}

    for name, model in models.items():
        print(f"\n🚀 训练 {name}...")

        start_time = time.time()

        try:
            # 训练模型
            model.fit(X_train_scaled, y_train)
            train_time = time.time() - start_time

            # 预测
            y_pred = model.predict(X_test_scaled)
            accuracy = accuracy_score(y_test, y_pred)

            # 交叉验证
            cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='accuracy')

            results[name] = {
                'accuracy': accuracy,
                'train_time': train_time,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std()
            }

            trained_models[name] = model

            print(f"✅ {name} 结果:")
            print(f"   测试准确率: {accuracy:.4f} ({accuracy*100:.1f}%)")
            print(f"   训练时间: {train_time:.2f} 秒")
            print(f"   交叉验证: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

        except Exception as e:
            print(f"❌ {name} 训练失败: {e}")
            continue

    # 创建集成模型
    print(f"\n🔗 创建集成模型...")

    if len(trained_models) >= 2:
        # 选择最好的几个模型进行集成
        best_models = sorted(results.items(), key=lambda x: x[1]['accuracy'], reverse=True)[:3]
        ensemble_models = [(name, trained_models[name]) for name, _ in best_models]

        ensemble = VotingClassifier(
            estimators=ensemble_models,
            voting='soft'  # 使用软投票
        )

        print(f"集成模型包含: {[name for name, _ in ensemble_models]}")

        # 训练集成模型
        start_time = time.time()
        ensemble.fit(X_train_scaled, y_train)
        ensemble_train_time = time.time() - start_time

        # 评估集成模型
        ensemble_pred = ensemble.predict(X_test_scaled)
        ensemble_accuracy = accuracy_score(y_test, ensemble_pred)

        # 集成模型交叉验证
        ensemble_cv_scores = cross_val_score(ensemble, X_train_scaled, y_train, cv=5, scoring='accuracy')

        results['Ensemble'] = {
            'accuracy': ensemble_accuracy,
            'train_time': ensemble_train_time,
            'cv_mean': ensemble_cv_scores.mean(),
            'cv_std': ensemble_cv_scores.std()
        }

        print(f"✅ 集成模型结果:")
        print(f"   测试准确率: {ensemble_accuracy:.4f} ({ensemble_accuracy*100:.1f}%)")
        print(f"   训练时间: {ensemble_train_time:.2f} 秒")
        print(f"   交叉验证: {ensemble_cv_scores.mean():.4f} ± {ensemble_cv_scores.std():.4f}")

    # 性能比较
    print(f"\n🏆 模型性能比较:")
    print(f"{'模型':<20} {'准确率':<12} {'训练时间':<12} {'交叉验证':<15} {'状态'}")
    print("-" * 80)

    for name, result in results.items():
        print(f"{name:<20} {result['accuracy']*100:<11.1f}% {result['train_time']:<11.1f}s "
              f"{result['cv_mean']:.3f}±{result['cv_std']:.3f}   {'✅ 完成'}")

    # 与之前结果比较
    print(f"\n📈 与之前结果比较:")
    print(f"   之前最佳 (改进管道): 54.8%")
    print(f"   之前最佳 (XGBoost): 48.4%")

    best_result = max(results.items(), key=lambda x: x[1]['accuracy'])
    best_name, best_performance = best_result
    best_accuracy = best_performance['accuracy']

    print(f"   当前最佳 ({best_name}): {best_accuracy*100:.1f}%")

    # 分析改进效果
    previous_best = 0.548
    improvement = best_accuracy - previous_best

    if best_accuracy >= 0.7:
        print(f"\n🎉 突破成功！达到70%目标！")
        print(f"   改进幅度: +{improvement*100:.1f} 个百分点")
        print(f"   相对改进: +{(improvement/previous_best)*100:.1f}%")
    elif best_accuracy >= 0.65:
        print(f"\n🎯 显著突破！接近70%目标！")
        print(f"   改进幅度: +{improvement*100:.1f} 个百分点")
        print(f"   相对改进: +{(improvement/previous_best)*100:.1f}%")
    elif best_accuracy >= 0.6:
        print(f"\n📈 重要改进！准确率突破60%！")
        print(f"   改进幅度: +{improvement*100:.1f} 个百分点")
        print(f"   相对改进: +{(improvement/previous_best)*100:.1f}%")
    elif best_accuracy > previous_best:
        print(f"\n✅ 有所改进！")
        print(f"   改进幅度: +{improvement*100:.1f} 个百分点")
        print(f"   相对改进: +{(improvement/previous_best)*100:.1f}%")
    else:
        print(f"\n📊 需要进一步优化")
        print(f"   当前差距: {improvement*100:.1f} 个百分点")

    # 特征重要性分析
    if 'XGBoost_Advanced' in trained_models:
        print(f"\n📈 特征重要性分析 (XGBoost):")
        model = trained_models['XGBoost_Advanced']
        importances = model.feature_importances_

        # 特征名称（简化版）
        feature_names = [
            'AA_hydrophobicity', 'AA_polarity', 'AA_positive', 'AA_negative', 'AA_aromatic',
            'AA_molecular_weight', 'AA_pKa', 'AA_volume', 'AA_accessibility',
            'rel_position', 'abs_position', 'seq_length'
        ]

        # 添加上下文特征名称
        for i in range(-7, 8):
            if i != 0:
                feature_names.extend([f'ctx_{i}_hydro', f'ctx_{i}_polar', f'ctx_{i}_pos', f'ctx_{i}_neg'])

        # 添加密码子特征名称
        feature_names.extend([
            'gc_content', 'codon_usage', 'codon_stability', 'wobble_gc', 'wobble_at', 'first_gc',
            'local_gc', 'local_at', 'sequence_entropy', 'repeat_score',
            'global_hydrophobicity', 'net_charge', 'aromatic_ratio', 'polar_ratio', 'avg_molecular_weight'
        ])

        # 确保特征名称数量匹配
        if len(feature_names) > len(importances):
            feature_names = feature_names[:len(importances)]
        elif len(feature_names) < len(importances):
            feature_names.extend([f'feature_{i}' for i in range(len(feature_names), len(importances))])

        # 排序并显示前15个最重要的特征
        feature_importance = list(zip(feature_names, importances))
        feature_importance.sort(key=lambda x: x[1], reverse=True)

        print("   前15个最重要特征:")
        for i, (name, importance) in enumerate(feature_importance[:15]):
            print(f"   {i+1:2d}. {name:<25} {importance:.4f}")

    # 保存结果
    final_results = {
        'best_model': best_name,
        'best_accuracy': float(best_accuracy),
        'previous_best': previous_best,
        'improvement': float(improvement),
        'relative_improvement': float((improvement/previous_best)*100),
        'all_results': {name: {k: float(v) if isinstance(v, (int, float)) else v
                              for k, v in result.items()}
                       for name, result in results.items()},
        'feature_count': X.shape[1],
        'sample_count': len(X),
        'breakthrough_achieved': best_accuracy >= 0.65
    }

    with open('advanced_breakthrough_results.json', 'w') as f:
        json.dump(final_results, f, indent=2)

    print(f"\n💾 结果已保存到: advanced_breakthrough_results.json")

    return best_accuracy

if __name__ == "__main__":
    try:
        best_accuracy = train_advanced_breakthrough_model()

        print(f"\n🏁 高级特征工程突破训练完成！")

        if best_accuracy:
            print(f"最终最佳准确率: {best_accuracy*100:.1f}%")

            if best_accuracy >= 0.7:
                print("🎉 成功达到70%突破目标！")
            elif best_accuracy >= 0.65:
                print("🎯 显著突破！接近70%目标！")
            elif best_accuracy >= 0.6:
                print("📈 重要改进！突破60%关口！")
            elif best_accuracy > 0.55:
                print("✅ 有所改进！为进一步突破奠定基础！")
            else:
                print("📊 需要重新评估策略")
        else:
            print("❌ 训练过程中出现问题")

    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
