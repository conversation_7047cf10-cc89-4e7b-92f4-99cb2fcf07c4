#!/usr/bin/env python3
"""
超大批次训练脚本 - 2000轮训练
简化版本，避免混合精度问题，专注于高准确度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import matplotlib.pyplot as plt
import gc
import os
import time
warnings.filterwarnings('ignore')

from fixed_training import FixedCodonPreferenceModel, calculate_accuracy
from improved_training import ImprovedCodonDataset, collate_fn

class UltraLargeBatchLoss(nn.Module):
    """超大批次损失函数"""
    
    def __init__(self, class_weights=None, ignore_index=-100):
        super().__init__()
        self.ignore_index = ignore_index
        if class_weights is not None:
            self.register_buffer('class_weights', class_weights)
        else:
            self.class_weights = None
    
    def forward(self, logits, targets):
        """计算损失"""
        # 确保张量连续性
        logits = logits.contiguous()
        targets = targets.contiguous()
        
        # 重塑张量
        batch_size, seq_len, num_classes = logits.shape
        logits_flat = logits.view(-1, num_classes)
        targets_flat = targets.view(-1)
        
        # 使用加权交叉熵损失
        if self.class_weights is not None:
            loss = F.cross_entropy(
                logits_flat, 
                targets_flat, 
                weight=self.class_weights,
                ignore_index=self.ignore_index,
                reduction='mean'
            )
        else:
            loss = F.cross_entropy(
                logits_flat, 
                targets_flat, 
                ignore_index=self.ignore_index,
                reduction='mean'
            )
        
        return loss

def load_checkpoint(model, optimizer, scheduler, checkpoint_path):
    """加载检查点"""
    if os.path.exists(checkpoint_path):
        print(f"📂 发现检查点文件: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location='cpu')

        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        start_epoch = checkpoint['epoch'] + 1
        best_accuracy = checkpoint.get('best_accuracy', 0.0)
        train_losses = checkpoint.get('train_losses', [])
        val_losses = checkpoint.get('val_losses', [])
        train_accuracies = checkpoint.get('train_accuracies', [])
        val_accuracies = checkpoint.get('val_accuracies', [])
        achieved_milestones = set(checkpoint.get('achieved_milestones', []))
        patience_counter = checkpoint.get('patience_counter', 0)

        print(f"✅ 成功加载检查点:")
        print(f"   起始轮次: {start_epoch}")
        print(f"   最佳准确度: {best_accuracy*100:.1f}%")
        print(f"   已完成轮次: {len(train_losses)}")

        return start_epoch, best_accuracy, train_losses, val_losses, train_accuracies, val_accuracies, achieved_milestones, patience_counter
    else:
        print("📝 未发现检查点文件，从头开始训练")
        return 0, 0.0, [], [], [], [], set(), 0

def save_checkpoint(epoch, model, optimizer, scheduler, best_accuracy, train_losses, val_losses,
                   train_accuracies, val_accuracies, achieved_milestones, patience_counter,
                   checkpoint_path, is_best=False):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'best_accuracy': best_accuracy,
        'train_losses': train_losses,
        'val_losses': val_losses,
        'train_accuracies': train_accuracies,
        'val_accuracies': val_accuracies,
        'achieved_milestones': list(achieved_milestones),
        'patience_counter': patience_counter,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }

    # 保存常规检查点
    torch.save(checkpoint, checkpoint_path)

    # 如果是最佳模型，额外保存
    if is_best:
        best_path = checkpoint_path.replace('checkpoint', 'best')
        torch.save(checkpoint, best_path)
        print(f"💾 保存最佳模型: {best_path}")

def train_ultra_large_batch_model():
    """超大批次3000轮训练，支持断点续训"""
    print("🚀 开始超大批次3000轮训练 (batch_size=500)")
    print("目标: 达到80%准确度")
    print("✨ 支持断点续训功能")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]  # 限制序列长度
    print(f"使用 {len(df)} 条数据进行训练")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # 创建模型
    model = FixedCodonPreferenceModel()
    model = model.to(device)
    
    # 创建数据集
    dataset = ImprovedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    print(f"有效数据集大小: {len(dataset)}")
    
    # 划分数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 加载纯密码子权重
    try:
        with open('pure_codon_weights.json', 'r') as f:
            weight_info = json.load(f)
        
        print("✅ 使用纯密码子权重（不考虑基因组分布）")
        codon_weights = weight_info['mixed_weights']
        weight_tensor = torch.ones(len(model.codon_to_idx))
        
        for codon, weight in codon_weights.items():
            if codon in model.codon_to_idx:
                idx = model.codon_to_idx[codon]
                weight_tensor[idx] = weight
        
        weight_tensor = weight_tensor / weight_tensor.mean()
        weight_tensor = weight_tensor.to(device)
        
        print(f"权重统计: min={weight_tensor.min():.3f}, max={weight_tensor.max():.3f}")
        
    except FileNotFoundError:
        print("⚠️ 未找到权重文件，使用均匀权重")
        weight_tensor = None
    
    # 超大批次设置
    target_batch_size = 500
    physical_batch_size = 16  # 减小物理批次以节省内存
    accumulation_steps = target_batch_size // physical_batch_size
    
    print(f"📊 批次配置:")
    print(f"  目标批次大小: {target_batch_size}")
    print(f"  物理批次大小: {physical_batch_size}")
    print(f"  梯度累积步数: {accumulation_steps}")
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=physical_batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=physical_batch_size, shuffle=False, collate_fn=collate_fn)
    
    # 损失函数和优化器
    criterion = UltraLargeBatchLoss(class_weights=weight_tensor)
    
    # 学习率设置
    base_lr = 2e-5
    scaled_lr = base_lr * np.sqrt(target_batch_size / 32)
    
    optimizer = optim.AdamW(model.parameters(), lr=scaled_lr, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=3000)  # 3000轮

    # 检查点文件路径
    checkpoint_path = 'ultra_training_checkpoint.pth'

    # 尝试加载检查点
    start_epoch, best_accuracy, train_losses, val_losses, train_accuracies, val_accuracies, achieved_milestones, patience_counter = load_checkpoint(
        model, optimizer, scheduler, checkpoint_path
    )

    print(f"📈 训练配置:")
    print(f"  基础学习率: {base_lr:.2e}")
    print(f"  缩放学习率: {scaled_lr:.2e}")
    print(f"  训练轮数: 3000")
    print(f"  起始轮次: {start_epoch + 1}")
    print(f"  训练集大小: {len(train_dataset)}")
    print(f"  验证集大小: {len(val_dataset)}")
    print(f"  检查点保存: 每轮自动保存")

    # 里程碑记录
    milestones = [0.5, 0.6, 0.7, 0.8, 0.9]
    patience = 500  # 增加耐心值适应3000轮训练
    
    print(f"\n🎯 开始3000轮超大批次训练 (从第{start_epoch + 1}轮开始)...")
    print("=" * 60)

    for epoch in range(start_epoch, 3000):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        num_batches = 0
        
        optimizer.zero_grad()
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1:4d}/3000 [Train]', leave=False)
        for batch_idx, batch in enumerate(train_pbar):
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                # 前向传播
                logits = model(proteins)
                
                # 对齐维度
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                # 计算损失
                loss = criterion(logits, targets)
                loss = loss / accumulation_steps  # 缩放损失
                
                # 反向传播
                loss.backward()
                
                train_loss += loss.item() * accumulation_steps
                train_acc += calculate_accuracy(logits, targets)
                
                # 梯度累积
                if (batch_idx + 1) % accumulation_steps == 0:
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    optimizer.zero_grad()
                    num_batches += 1
                    
                    # 清理内存
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                
                train_pbar.set_postfix({
                    'Loss': f'{loss.item() * accumulation_steps:.3f}',
                    'Acc': f'{calculate_accuracy(logits, targets):.3f}',
                    'Batch': f'{num_batches}'
                })
                
            except Exception as e:
                print(f"训练批次 {batch_idx} 异常: {e}")
                continue
        
        # 处理剩余梯度
        if len(train_loader) % accumulation_steps != 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            optimizer.zero_grad()
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1:4d}/3000 [Val]', leave=False)
            for batch in val_pbar:
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    loss = criterion(logits, targets)
                    acc = calculate_accuracy(logits, targets)
                    
                    val_loss += loss.item()
                    val_acc += acc
                    val_batches += 1
                    
                    val_pbar.set_postfix({
                        'Loss': f'{loss.item():.3f}',
                        'Acc': f'{acc:.3f}'
                    })
                    
                except Exception as e:
                    print(f"验证批次异常: {e}")
                    continue
        
        # 计算平均值
        if len(train_loader) > 0:
            avg_train_loss = train_loss / len(train_loader)
            avg_train_acc = train_acc / len(train_loader)
        else:
            avg_train_loss = float('inf')
            avg_train_acc = 0.0
            
        if val_batches > 0:
            avg_val_loss = val_loss / val_batches
            avg_val_acc = val_acc / val_batches
        else:
            avg_val_loss = float('inf')
            avg_val_acc = 0.0
        
        # 记录历史
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        train_accuracies.append(avg_train_acc)
        val_accuracies.append(avg_val_acc)
        
        # 检查里程碑
        for milestone in milestones:
            if avg_val_acc >= milestone and milestone not in achieved_milestones:
                achieved_milestones.add(milestone)
                print(f"🎉 里程碑达成！验证准确度达到 {milestone*100:.0f}% (第{epoch+1}轮)")
        
        # 每轮保存检查点
        save_checkpoint(
            epoch, model, optimizer, scheduler, best_accuracy,
            train_losses, val_losses, train_accuracies, val_accuracies,
            achieved_milestones, patience_counter, checkpoint_path
        )

        # 每50轮或重要轮次显示详细信息
        if (epoch + 1) % 50 == 0 or avg_val_acc > best_accuracy:
            print(f'Epoch {epoch+1:4d}/3000: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%, Loss={avg_val_loss:.4f}')

        # 保存最佳模型
        is_best = False
        if avg_val_acc > best_accuracy:
            best_accuracy = avg_val_acc
            patience_counter = 0
            is_best = True

            # 保存最佳模型检查点
            save_checkpoint(
                epoch, model, optimizer, scheduler, best_accuracy,
                train_losses, val_losses, train_accuracies, val_accuracies,
                achieved_milestones, patience_counter, checkpoint_path, is_best=True
            )

            if avg_val_acc >= 0.8:
                print(f"🎊 目标达成！验证准确度: {best_accuracy*100:.1f}% (第{epoch+1}轮)")
                print("🎉 训练提前完成，已达到80%目标准确度！")
                break
        else:
            patience_counter += 1
        
        # 早停检查
        if patience_counter >= patience:
            print(f'早停：准确度连续{patience}轮未改善 (第{epoch+1}轮)')
            print(f"💾 最终检查点已保存: {checkpoint_path}")
            break
        
        scheduler.step()
        
        # 内存清理
        if (epoch + 1) % 10 == 0:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
    
    print("\n" + "=" * 60)
    print("🏁 超大批次训练完成！")
    print(f"🎯 最佳验证准确度: {best_accuracy:.4f} ({best_accuracy*100:.1f}%)")
    print(f"🏆 达成里程碑: {sorted([int(m*100) for m in achieved_milestones])}%")
    
    if best_accuracy >= 0.8:
        print("🎉🎉🎉 恭喜！成功达到80%目标准确度！")
    elif best_accuracy >= 0.7:
        print("🚀 准确度超过70%，非常接近目标！")
    elif best_accuracy >= 0.6:
        print("📈 准确度超过60%，有显著改进！")
    
    return best_accuracy

if __name__ == "__main__":
    best_acc = train_ultra_large_batch_model()
    
    print(f"\n📊 最终结果:")
    print(f"准确度: {best_acc*100:.1f}%")
    print(f"状态: {'✅ 达标' if best_acc >= 0.8 else '⚠️ 未达标'}")
    
    if best_acc < 0.8:
        print(f"\n💡 进一步优化建议:")
        print(f"1. 继续训练更多轮次")
        print(f"2. 尝试更大的批次大小 (1000+)")
        print(f"3. 调整学习率策略")
        print(f"4. 使用数据增强技术")
