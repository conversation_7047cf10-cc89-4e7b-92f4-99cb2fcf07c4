#!/usr/bin/env python3
"""
改进的密码子偏好性预测模型训练
解决数据量少、权重不合理、准确度低的问题
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import os
import warnings
import matplotlib.pyplot as plt
from sklearn.utils.class_weight import compute_class_weight
warnings.filterwarnings('ignore')

from fixed_training import FixedCodonPreferenceModel
from improved_training import ImprovedCodonDataset, collate_fn

class ImprovedWeightedLoss(nn.Module):
    """改进的加权损失函数"""
    
    def __init__(self, codon_weights, codon_to_idx, ignore_index=-100, 
                 focal_alpha=0.25, focal_gamma=2.0, label_smoothing=0.1):
        super().__init__()
        self.ignore_index = ignore_index
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma
        self.label_smoothing = label_smoothing
        
        # 构建温和的权重张量
        self.register_buffer('class_weights', self._build_balanced_weights(codon_weights, codon_to_idx))
        
    def _build_balanced_weights(self, codon_weights, codon_to_idx):
        """构建平衡的权重张量，避免极端权重"""
        num_classes = len(codon_to_idx)
        weights = torch.ones(num_classes)
        
        # 收集所有权重值
        weight_values = []
        for codon, weight in codon_weights.items():
            if codon in codon_to_idx:
                weight_values.append(weight)
        
        # 使用对数缩放减少极端值
        min_weight = min(weight_values)
        max_weight = max(weight_values)
        
        for codon, weight in codon_weights.items():
            if codon in codon_to_idx:
                idx = codon_to_idx[codon]
                # 对数缩放 + 限制范围
                log_weight = np.log(weight / min_weight + 1)
                max_log_weight = np.log(max_weight / min_weight + 1)
                # 将权重范围限制在 [0.5, 3.0]
                normalized_weight = 0.5 + 2.5 * (log_weight / max_log_weight)
                weights[idx] = normalized_weight
        
        # 归一化
        weights = weights / weights.mean()
        
        print(f"改进权重统计: min={weights.min():.3f}, max={weights.max():.3f}, mean={weights.mean():.3f}")
        return weights
    
    def forward(self, logits, targets):
        """计算改进的损失"""
        batch_size, seq_len, num_classes = logits.shape
        
        # 重塑张量
        logits_flat = logits.reshape(-1, num_classes)
        targets_flat = targets.reshape(-1)
        
        # 创建mask
        mask = (targets_flat != self.ignore_index)
        
        if mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        # 应用mask
        logits_masked = logits_flat[mask]
        targets_masked = targets_flat[mask]
        
        # 计算概率
        probs = F.softmax(logits_masked, dim=-1)
        
        # Label smoothing
        if self.label_smoothing > 0:
            smooth_targets = torch.zeros_like(probs)
            smooth_targets.fill_(self.label_smoothing / (num_classes - 1))
            smooth_targets.scatter_(1, targets_masked.unsqueeze(1), 1.0 - self.label_smoothing)
            targets_probs = smooth_targets
        else:
            targets_probs = F.one_hot(targets_masked, num_classes).float()
        
        # 加权交叉熵
        log_probs = F.log_softmax(logits_masked, dim=-1)
        weighted_nll = -(targets_probs * log_probs * self.class_weights.unsqueeze(0)).sum(dim=-1)
        
        # Focal loss调整
        if self.focal_gamma > 0:
            pt = (targets_probs * probs).sum(dim=-1)
            focal_weight = self.focal_alpha * (1 - pt) ** self.focal_gamma
            weighted_nll = focal_weight * weighted_nll
        
        return weighted_nll.mean()

class DataAugmentation:
    """数据增强类"""
    
    def __init__(self, codon_to_idx, aa_to_codons):
        self.codon_to_idx = codon_to_idx
        self.aa_to_codons = aa_to_codons
        
    def synonym_substitution(self, protein_seq, codon_indices, prob=0.1):
        """同义密码子替换"""
        if np.random.random() > prob:
            return codon_indices
        
        new_indices = codon_indices.copy()
        for i, (aa, codon_idx) in enumerate(zip(protein_seq, codon_indices)):
            if aa in self.aa_to_codons and np.random.random() < 0.3:
                # 随机选择该氨基酸的另一个密码子
                possible_codons = self.aa_to_codons[aa]
                if len(possible_codons) > 1:
                    new_codon = np.random.choice(possible_codons)
                    new_indices[i] = self.codon_to_idx[new_codon]
        
        return new_indices

def load_expanded_data(data_file="processed_BL21_data.csv", max_length=1024):
    """加载扩展的数据集"""
    print(f"正在加载数据: {data_file}")
    
    df = pd.read_csv(data_file)
    print(f"原始数据: {len(df)} 条")
    
    # 使用更宽松的长度限制
    df_filtered = df[df['protein_length'] <= max_length]
    print(f"长度过滤后: {len(df_filtered)} 条 (≤{max_length})")
    
    # 进一步按长度分层，优先使用较短序列
    df_short = df_filtered[df_filtered['protein_length'] <= 256]
    df_medium = df_filtered[(df_filtered['protein_length'] > 256) & (df_filtered['protein_length'] <= 512)]
    df_long = df_filtered[df_filtered['protein_length'] > 512]
    
    print(f"数据分布: 短序列(≤256): {len(df_short)}, 中等(256-512): {len(df_medium)}, 长序列(>512): {len(df_long)}")
    
    # 组合数据，确保有足够的训练样本
    if len(df_short) < 1000:
        # 如果短序列不够，添加中等长度序列
        combined_df = pd.concat([df_short, df_medium.head(2000)], ignore_index=True)
        print(f"组合数据集: {len(combined_df)} 条")
    else:
        combined_df = df_filtered
    
    return combined_df['protein_sequence'].tolist(), combined_df['nucleotide_sequence'].tolist()

def calculate_improved_accuracy(logits, targets, ignore_index=-100):
    """计算改进的准确度指标"""
    with torch.no_grad():
        predictions = torch.argmax(logits, dim=-1)
        mask = (targets != ignore_index)
        
        if mask.sum() == 0:
            return {'accuracy': 0.0, 'top3_accuracy': 0.0, 'top5_accuracy': 0.0}
        
        # Top-1准确度
        correct = (predictions == targets).float() * mask.float()
        accuracy = correct.sum() / mask.sum()
        
        # Top-3准确度
        _, top3_pred = torch.topk(logits, 3, dim=-1)
        top3_correct = (top3_pred == targets.unsqueeze(-1)).any(dim=-1).float() * mask.float()
        top3_accuracy = top3_correct.sum() / mask.sum()
        
        # Top-5准确度
        _, top5_pred = torch.topk(logits, 5, dim=-1)
        top5_correct = (top5_pred == targets.unsqueeze(-1)).any(dim=-1).float() * mask.float()
        top5_accuracy = top5_correct.sum() / mask.sum()
        
        return {
            'accuracy': accuracy.item(),
            'top3_accuracy': top3_accuracy.item(),
            'top5_accuracy': top5_accuracy.item()
        }

def train_improved_model():
    """训练改进的模型"""
    print("开始训练改进的密码子偏好性预测模型")

    # 加载改进的权重信息
    try:
        with open('improved_codon_weights.json', 'r') as f:
            weight_info = json.load(f)
        codon_weights = weight_info['mixed_weights']  # 使用混合权重策略
        print("使用改进的混合权重策略")
    except FileNotFoundError:
        with open('codon_weights.json', 'r') as f:
            weight_info = json.load(f)
        codon_weights = weight_info['codon_weights']
        print("使用原始权重策略")
    print(f"加载了 {len(codon_weights)} 个密码子的权重信息")

    # 加载扩展数据
    proteins, nucleotides = load_expanded_data(max_length=1024)
    print(f"使用 {len(proteins)} 条数据进行训练")

    # 创建模型
    model = FixedCodonPreferenceModel()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    # 创建数据集
    dataset = ImprovedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    print(f"有效数据集大小: {len(dataset)}")

    # 划分数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])

    # 数据加载器 - 增大批次大小
    batch_size = 8 if torch.cuda.is_available() else 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)

    # 改进的损失函数
    criterion = ImprovedWeightedLoss(codon_weights, model.codon_to_idx)
    criterion = criterion.to(device)

    # 优化器 - 使用更好的参数
    optimizer = optim.AdamW(model.parameters(), lr=1e-5, weight_decay=0.01, betas=(0.9, 0.999))

    # 学习率调度器 - 使用warmup
    total_steps = len(train_loader) * 50
    warmup_steps = total_steps // 10

    def lr_lambda(step):
        if step < warmup_steps:
            return step / warmup_steps
        else:
            return 0.5 * (1 + np.cos(np.pi * (step - warmup_steps) / (total_steps - warmup_steps)))

    scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

    # 训练历史
    train_losses = []
    val_losses = []
    train_accuracies = []
    val_accuracies = []

    best_accuracy = 0.0
    patience = 10
    patience_counter = 0

    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    print(f"批次大小: {batch_size}")
    print(f"总训练步数: {total_steps}")

    for epoch in range(50):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_metrics = {'accuracy': 0.0, 'top3_accuracy': 0.0, 'top5_accuracy': 0.0}
        valid_batches = 0

        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/50 [Train]')
        for batch_idx, batch in enumerate(train_pbar):
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)

                optimizer.zero_grad()

                logits = model(proteins)

                # 对齐维度
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]

                loss = criterion(logits, targets)

                # 检查损失是否有效
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"跳过无效损失: {loss.item()}")
                    continue

                loss.backward()

                # 梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                optimizer.step()
                scheduler.step()

                # 计算准确度
                metrics = calculate_improved_accuracy(logits, targets)

                train_loss += loss.item()
                for key in train_metrics:
                    train_metrics[key] += metrics[key]
                valid_batches += 1

                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.3f}',
                    'Acc': f'{metrics["accuracy"]:.3f}',
                    'Top3': f'{metrics["top3_accuracy"]:.3f}',
                    'LR': f'{scheduler.get_last_lr()[0]:.2e}'
                })

            except Exception as e:
                print(f"训练批次 {batch_idx} 异常: {e}")
                continue

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_metrics = {'accuracy': 0.0, 'top3_accuracy': 0.0, 'top5_accuracy': 0.0}
        val_batches = 0

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/50 [Val]')
            for batch in val_pbar:
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)

                    logits = model(proteins)

                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]

                    loss = criterion(logits, targets)
                    metrics = calculate_improved_accuracy(logits, targets)

                    val_loss += loss.item()
                    for key in val_metrics:
                        val_metrics[key] += metrics[key]
                    val_batches += 1

                    val_pbar.set_postfix({
                        'Loss': f'{loss.item():.3f}',
                        'Acc': f'{metrics["accuracy"]:.3f}',
                        'Top3': f'{metrics["top3_accuracy"]:.3f}'
                    })

                except Exception as e:
                    print(f"验证批次异常: {e}")
                    continue

        # 计算平均值
        if valid_batches > 0:
            avg_train_loss = train_loss / valid_batches
            for key in train_metrics:
                train_metrics[key] /= valid_batches
        else:
            avg_train_loss = float('inf')

        if val_batches > 0:
            avg_val_loss = val_loss / val_batches
            for key in val_metrics:
                val_metrics[key] /= val_batches
        else:
            avg_val_loss = float('inf')

        # 记录历史
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        train_accuracies.append(train_metrics['accuracy'])
        val_accuracies.append(val_metrics['accuracy'])

        print(f'Epoch {epoch+1}/50:')
        print(f'  Train - Loss: {avg_train_loss:.4f}, Acc: {train_metrics["accuracy"]:.4f}, Top3: {train_metrics["top3_accuracy"]:.4f}')
        print(f'  Val   - Loss: {avg_val_loss:.4f}, Acc: {val_metrics["accuracy"]:.4f}, Top3: {val_metrics["top3_accuracy"]:.4f}')
        print(f'  Valid Batches: Train={valid_batches}, Val={val_batches}')

        # 保存最佳模型
        current_accuracy = val_metrics['accuracy']
        if current_accuracy > best_accuracy:
            best_accuracy = current_accuracy
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': avg_val_loss,
                'val_accuracy': current_accuracy,
                'val_top3_accuracy': val_metrics['top3_accuracy'],
                'codon_weights': codon_weights
            }, 'improved_best_model.pth')
            print(f'  保存最佳模型 (准确度: {best_accuracy:.4f})')
        else:
            patience_counter += 1
            print(f'  准确度未改善 ({patience_counter}/{patience})')

        # 早停检查
        if patience_counter >= patience:
            print(f'  早停：准确度连续{patience}轮未改善')
            break

        # 保存检查点
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'val_loss': avg_val_loss,
            'val_accuracy': current_accuracy,
            'train_losses': train_losses,
            'val_losses': val_losses,
            'train_accuracies': train_accuracies,
            'val_accuracies': val_accuracies
        }, 'improved_checkpoint.pth')

        print()

    # 绘制训练曲线
    plt.figure(figsize=(15, 5))

    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 3, 2)
    plt.plot(train_accuracies, label='Train Accuracy')
    plt.plot(val_accuracies, label='Val Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.title('Training and Validation Accuracy')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 3, 3)
    plt.plot([acc * 100 for acc in val_accuracies], label='Val Accuracy (%)')
    plt.axhline(y=80, color='r', linestyle='--', label='Target 80%')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.title('Validation Accuracy Progress')
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig('improved_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("训练完成！")
    print(f"最佳验证准确度: {best_accuracy:.4f} ({best_accuracy*100:.1f}%)")

    return best_accuracy

if __name__ == "__main__":
    best_acc = train_improved_model()

    if best_acc >= 0.8:
        print("🎉 恭喜！模型达到了80%的目标准确度！")
    else:
        print(f"模型准确度为 {best_acc*100:.1f}%，距离80%目标还需要改进。")
        print("建议：")
        print("1. 增加训练轮数")
        print("2. 调整学习率")
        print("3. 尝试不同的权重策略")
        print("4. 增加数据增强")
