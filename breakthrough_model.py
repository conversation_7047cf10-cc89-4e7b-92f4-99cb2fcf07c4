#!/usr/bin/env python3
"""
突破性改进模型 - Breakthrough Improvement Model
目标：将准确度从55%提升到70%+

关键改进：
1. 高级特征工程（密码子使用偏好、GC含量等）
2. 焦点损失函数处理类别不平衡
3. 数据增强策略
4. 集成学习方法
5. 优化训练策略
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import time
import math
import random
from collections import Counter
warnings.filterwarnings('ignore')

class AdvancedFeatureExtractor:
    """高级特征提取器 - 添加生物学相关特征"""

    def __init__(self):
        # 密码子使用频率（基于大肠杆菌）
        self.codon_usage = {
            'TTT': 0.58, 'TTC': 0.42, 'TTA': 0.14, 'TTG': 0.13,
            'TCT': 0.17, 'TCC': 0.15, 'TCA': 0.14, 'TCG': 0.14,
            'TAT': 0.59, 'TAC': 0.41, 'TGT': 0.46, 'TGC': 0.54, 'TGG': 1.00,
            'CTT': 0.12, 'CTC': 0.10, 'CTA': 0.04, 'CTG': 0.47,
            'CCT': 0.18, 'CCC': 0.13, 'CCA': 0.20, 'CCG': 0.49,
            'CAT': 0.57, 'CAC': 0.43, 'CAA': 0.34, 'CAG': 0.66,
            'CGT': 0.36, 'CGC': 0.36, 'CGA': 0.07, 'CGG': 0.11,
            'ATT': 0.49, 'ATC': 0.39, 'ATA': 0.11, 'ATG': 1.00,
            'ACT': 0.19, 'ACC': 0.40, 'ACA': 0.17, 'ACG': 0.25,
            'AAT': 0.49, 'AAC': 0.51, 'AAA': 0.74, 'AAG': 0.26,
            'AGT': 0.16, 'AGC': 0.25, 'AGA': 0.07, 'AGG': 0.04,
            'GTT': 0.28, 'GTC': 0.20, 'GTA': 0.17, 'GTG': 0.35,
            'GCT': 0.18, 'GCC': 0.26, 'GCA': 0.23, 'GCG': 0.33,
            'GAT': 0.63, 'GAC': 0.37, 'GAA': 0.68, 'GAG': 0.32,
            'GGT': 0.35, 'GGC': 0.37, 'GGA': 0.13, 'GGG': 0.15
        }

        # 氨基酸理化性质（扩展版）
        self.aa_properties = {
            'A': [1.8, 0, 0, 0, 0, 89.1, 6.0, 0],    # 疏水性, 极性, 正电, 负电, 芳香性, 分子量, pKa, 体积
            'R': [-4.5, 1, 1, 0, 0, 174.2, 12.5, 148],
            'N': [-3.5, 1, 0, 0, 0, 132.1, 8.8, 96],
            'D': [-3.5, 1, 0, 1, 0, 133.1, 3.9, 91],
            'C': [2.5, 0, 0, 0, 0, 121.0, 8.3, 86],
            'Q': [-3.5, 1, 0, 0, 0, 146.1, 9.1, 114],
            'E': [-3.5, 1, 0, 1, 0, 147.1, 4.3, 109],
            'G': [-0.4, 0, 0, 0, 0, 75.1, 6.0, 48],
            'H': [-3.2, 1, 1, 0, 1, 155.2, 6.0, 118],
            'I': [4.5, 0, 0, 0, 0, 131.2, 6.0, 124],
            'L': [3.8, 0, 0, 0, 0, 131.2, 6.0, 124],
            'K': [-3.9, 1, 1, 0, 0, 146.2, 10.5, 135],
            'M': [1.9, 0, 0, 0, 0, 149.2, 5.7, 124],
            'F': [2.8, 0, 0, 0, 1, 165.2, 5.5, 135],
            'P': [-1.6, 0, 0, 0, 0, 115.1, 6.3, 90],
            'S': [-0.8, 1, 0, 0, 0, 105.1, 5.7, 73],
            'T': [-0.7, 1, 0, 0, 0, 119.1, 5.6, 93],
            'W': [-0.9, 0, 0, 0, 1, 204.2, 5.9, 163],
            'Y': [-1.3, 1, 0, 0, 1, 181.2, 10.1, 141],
            'V': [4.2, 0, 0, 0, 0, 117.1, 6.0, 105],
        }

        # 遗传密码
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }

    def extract_advanced_features(self, protein_seq, nucleotide_seq, position):
        """提取高级特征"""
        features = []

        # 基本氨基酸特征
        aa = protein_seq[position]
        aa_props = self.aa_properties.get(aa, [0] * 8)
        features.extend(aa_props)

        # 位置特征
        rel_pos = position / len(protein_seq)
        features.extend([rel_pos, len(protein_seq)])

        # 扩展上下文特征（±5位置）
        context_size = 5
        for offset in range(-context_size, context_size + 1):
            if offset == 0:
                continue
            ctx_pos = position + offset
            if 0 <= ctx_pos < len(protein_seq):
                ctx_aa = protein_seq[ctx_pos]
                ctx_props = self.aa_properties.get(ctx_aa, [0] * 8)
                features.extend(ctx_props[:4])  # 只取前4个主要属性
            else:
                features.extend([0, 0, 0, 0])

        # GC含量特征
        codon_start = position * 3
        if codon_start + 3 <= len(nucleotide_seq):
            codon = nucleotide_seq[codon_start:codon_start + 3]
            gc_content = (codon.count('G') + codon.count('C')) / 3
            features.append(gc_content)

            # 密码子使用偏好
            codon_usage = self.codon_usage.get(codon, 0.5)
            features.append(codon_usage)

            # 密码子稳定性（GC含量在第三位）
            wobble_gc = 1 if codon[2] in 'GC' else 0
            features.append(wobble_gc)
        else:
            features.extend([0.5, 0.5, 0])

        # 局部GC含量（±9bp窗口）
        window_start = max(0, codon_start - 9)
        window_end = min(len(nucleotide_seq), codon_start + 12)
        window_seq = nucleotide_seq[window_start:window_end]
        if len(window_seq) > 0:
            local_gc = (window_seq.count('G') + window_seq.count('C')) / len(window_seq)
            features.append(local_gc)
        else:
            features.append(0.5)

        return features

class FocalLoss(nn.Module):
    """焦点损失函数 - 处理类别不平衡"""

    def __init__(self, alpha=1, gamma=2, ignore_index=-100):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index

    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, ignore_index=self.ignore_index, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        return focal_loss.mean()

class BreakthroughDataset(Dataset):
    """突破性数据集 - 包含数据增强"""

    def __init__(self, proteins, nucleotides, codon_to_idx, augment=True):
        self.proteins = proteins
        self.nucleotides = nucleotides
        self.codon_to_idx = codon_to_idx
        self.augment = augment
        self.feature_extractor = AdvancedFeatureExtractor()

        # 预处理数据
        self.processed_data = []
        self._preprocess_data()

    def _preprocess_data(self):
        """预处理数据"""
        print("🔧 预处理数据中...")

        for i, (protein_seq, nucleotide_seq) in enumerate(zip(self.proteins, self.nucleotides)):
            try:
                # 基本验证
                if len(nucleotide_seq) % 3 != 0:
                    continue

                codons = [nucleotide_seq[j:j+3] for j in range(0, len(nucleotide_seq), 3)]
                if len(codons) > len(protein_seq):
                    codons = codons[:len(protein_seq)]

                if len(codons) != len(protein_seq):
                    continue

                # 提取特征和标签
                features = []
                labels = []

                for pos, (aa, codon) in enumerate(zip(protein_seq, codons)):
                    if codon not in self.codon_to_idx:
                        continue
                    if aa not in self.feature_extractor.aa_properties:
                        continue

                    # 验证遗传密码一致性
                    expected_aa = self.feature_extractor.genetic_code.get(codon, 'X')
                    if expected_aa != aa:
                        continue

                    # 提取高级特征
                    feature_vector = self.feature_extractor.extract_advanced_features(
                        protein_seq, nucleotide_seq, pos
                    )

                    features.append(feature_vector)
                    labels.append(self.codon_to_idx[codon])

                if len(features) > 0:
                    self.processed_data.append({
                        'features': np.array(features),
                        'labels': np.array(labels),
                        'protein_seq': protein_seq,
                        'nucleotide_seq': nucleotide_seq
                    })

            except Exception as e:
                continue

        print(f"✅ 预处理完成：{len(self.processed_data)} 个有效样本")

    def __len__(self):
        return len(self.processed_data)

    def __getitem__(self, idx):
        data = self.processed_data[idx]

        features = torch.FloatTensor(data['features'])
        labels = torch.LongTensor(data['labels'])

        # 数据增强
        if self.augment and random.random() < 0.3:
            features = self._augment_features(features)

        return {
            'features': features,
            'labels': labels
        }

    def _augment_features(self, features):
        """特征增强"""
        # 添加小量噪声
        noise = torch.randn_like(features) * 0.01
        features = features + noise

        # 随机遮蔽部分特征
        if random.random() < 0.1:
            mask_size = random.randint(1, min(5, features.size(1)))
            mask_indices = random.sample(range(features.size(1)), mask_size)
            features[:, mask_indices] = 0

        return features

class BreakthroughModel(nn.Module):
    """突破性模型架构"""

    def __init__(self, input_dim, num_classes, hidden_dim=512):
        super().__init__()

        # 输入层
        self.input_norm = nn.LayerNorm(input_dim)
        self.input_dropout = nn.Dropout(0.1)

        # 特征提取层
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),

            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
        )

        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        self.attention_norm = nn.LayerNorm(hidden_dim)

        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Dropout(0.1)
        )
        self.ffn_norm = nn.LayerNorm(hidden_dim)

        # 输出层
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, num_classes)
        )

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)

    def forward(self, x):
        # 输入标准化
        x = self.input_norm(x)
        x = self.input_dropout(x)

        # 特征提取
        features = self.feature_extractor(x)

        # 自注意力
        attn_out, _ = self.attention(features, features, features)
        features = self.attention_norm(features + attn_out)

        # 前馈网络
        ffn_out = self.ffn(features)
        features = self.ffn_norm(features + ffn_out)

        # 分类
        logits = self.classifier(features)

        return logits

def breakthrough_collate_fn(batch):
    """批处理函数"""
    features = [item['features'] for item in batch]
    labels = [item['labels'] for item in batch]

    # 填充到相同长度
    max_len = max(f.size(0) for f in features)

    padded_features = []
    padded_labels = []

    for f, l in zip(features, labels):
        pad_len = max_len - f.size(0)
        if pad_len > 0:
            f_padded = torch.cat([f, torch.zeros(pad_len, f.size(1))], dim=0)
            l_padded = torch.cat([l, torch.full((pad_len,), -100, dtype=torch.long)], dim=0)
        else:
            f_padded = f
            l_padded = l

        padded_features.append(f_padded)
        padded_labels.append(l_padded)

    return {
        'features': torch.stack(padded_features),
        'labels': torch.stack(padded_labels)
    }

class EnsembleModel:
    """集成模型"""

    def __init__(self, models):
        self.models = models

    def predict(self, x):
        """集成预测"""
        predictions = []
        for model in self.models:
            model.eval()
            with torch.no_grad():
                pred = F.softmax(model(x), dim=-1)
                predictions.append(pred)

        # 平均预测
        ensemble_pred = torch.stack(predictions).mean(dim=0)
        return ensemble_pred

    def train_models(self, train_loader, val_loader, device, epochs=50):
        """训练所有模型"""
        for i, model in enumerate(self.models):
            print(f"\n🚀 训练模型 {i+1}/{len(self.models)}")
            self._train_single_model(model, train_loader, val_loader, device, epochs)

    def _train_single_model(self, model, train_loader, val_loader, device, epochs):
        """训练单个模型"""
        model = model.to(device)

        # 损失函数和优化器
        criterion = FocalLoss(alpha=1, gamma=2)
        optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2)

        best_val_acc = 0.0
        patience = 15
        patience_counter = 0

        for epoch in range(epochs):
            # 训练
            model.train()
            train_loss = 0.0
            train_acc = 0.0
            train_batches = 0

            for batch in tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs} [Train]', leave=False):
                try:
                    features = batch['features'].to(device)
                    labels = batch['labels'].to(device)

                    optimizer.zero_grad()

                    logits = model(features)
                    loss = criterion(logits.view(-1, logits.size(-1)), labels.view(-1))

                    if torch.isnan(loss) or torch.isinf(loss):
                        continue

                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()

                    train_loss += loss.item()

                    # 计算准确率
                    with torch.no_grad():
                        predictions = torch.argmax(logits, dim=-1)
                        mask = (labels != -100)
                        if mask.sum() > 0:
                            correct = (predictions == labels).float() * mask.float()
                            acc = correct.sum() / mask.sum()
                            train_acc += acc.item()

                    train_batches += 1

                except Exception as e:
                    continue

            # 验证
            model.eval()
            val_acc = 0.0
            val_batches = 0

            with torch.no_grad():
                for batch in tqdm(val_loader, desc=f'Epoch {epoch+1}/{epochs} [Val]', leave=False):
                    try:
                        features = batch['features'].to(device)
                        labels = batch['labels'].to(device)

                        logits = model(features)
                        predictions = torch.argmax(logits, dim=-1)

                        mask = (labels != -100)
                        if mask.sum() > 0:
                            correct = (predictions == labels).float() * mask.float()
                            acc = correct.sum() / mask.sum()
                            val_acc += acc.item()

                        val_batches += 1

                    except Exception as e:
                        continue

            # 计算平均值
            avg_train_acc = train_acc / max(train_batches, 1)
            avg_val_acc = val_acc / max(val_batches, 1)

            scheduler.step()

            print(f'Epoch {epoch+1:3d}/{epochs}: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%')

            # 早停检查
            if avg_val_acc > best_val_acc:
                best_val_acc = avg_val_acc
                patience_counter = 0
                # 保存最佳模型
                torch.save(model.state_dict(), f'breakthrough_model_{id(model)}.pth')
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f'早停：{patience} 轮无改进')
                break

        print(f'最佳验证准确率: {best_val_acc*100:.1f}%')
        return best_val_acc

def train_breakthrough_model():
    """训练突破性模型"""
    print("🚀 突破性模型训练")
    print("目标：将准确度从55%提升到70%+")
    print("=" * 60)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]
    print(f"使用 {len(df)} 个样本")

    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()

    # 创建密码子映射
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }

    codons = [codon for codon, aa in genetic_code.items() if aa != '*']
    codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}

    print(f"密码子类别数: {len(codon_to_idx)}")

    # 创建数据集
    print("\n📊 创建数据集...")
    dataset = BreakthroughDataset(proteins, nucleotides, codon_to_idx, augment=True)

    if len(dataset) == 0:
        print("❌ 没有有效数据")
        return 0.0

    print(f"数据集大小: {len(dataset)}")

    # 数据分割
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])

    # 创建数据加载器
    batch_size = 8
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=breakthrough_collate_fn,
        num_workers=2
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=breakthrough_collate_fn,
        num_workers=2
    )

    print(f"训练批次: {len(train_loader)}, 验证批次: {len(val_loader)}")

    # 获取特征维度
    sample_batch = next(iter(train_loader))
    input_dim = sample_batch['features'].size(-1)
    num_classes = len(codon_to_idx)

    print(f"输入特征维度: {input_dim}")
    print(f"输出类别数: {num_classes}")

    # 创建集成模型
    print("\n🏗️ 创建集成模型...")
    models = []
    for i in range(3):  # 3个不同的模型
        model = BreakthroughModel(
            input_dim=input_dim,
            num_classes=num_classes,
            hidden_dim=512 + i * 64  # 不同的隐藏层大小
        )
        models.append(model)

    ensemble = EnsembleModel(models)

    # 训练集成模型
    print("\n🚀 开始训练集成模型...")
    ensemble.train_models(train_loader, val_loader, device, epochs=30)

    # 评估集成模型
    print("\n📊 评估集成模型...")
    ensemble_acc = evaluate_ensemble(ensemble, val_loader, device)

    print(f"\n🎯 最终结果:")
    print(f"   集成模型准确率: {ensemble_acc*100:.1f}%")
    print(f"   之前最佳准确率: 54.8%")

    if ensemble_acc > 0.65:
        print(f"\n🎉 突破成功！准确率提升到 {ensemble_acc*100:.1f}%")
        improvement = (ensemble_acc - 0.548) * 100
        print(f"   改进幅度: +{improvement:.1f} 个百分点")
    elif ensemble_acc > 0.6:
        print(f"\n📈 显著改进！准确率达到 {ensemble_acc*100:.1f}%")
        improvement = (ensemble_acc - 0.548) * 100
        print(f"   改进幅度: +{improvement:.1f} 个百分点")
    elif ensemble_acc > 0.548:
        print(f"\n✅ 有所改进！准确率提升到 {ensemble_acc*100:.1f}%")
        improvement = (ensemble_acc - 0.548) * 100
        print(f"   改进幅度: +{improvement:.1f} 个百分点")
    else:
        print(f"\n📊 需要进一步优化")

    # 保存结果
    results = {
        'ensemble_accuracy': float(ensemble_acc),
        'previous_best': 0.548,
        'improvement': float(ensemble_acc - 0.548),
        'model_type': 'breakthrough_ensemble',
        'features_used': [
            'advanced_aa_properties',
            'codon_usage_bias',
            'gc_content',
            'extended_context',
            'focal_loss',
            'data_augmentation'
        ]
    }

    with open('breakthrough_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n💾 结果已保存到: breakthrough_results.json")

    return ensemble_acc

def evaluate_ensemble(ensemble, val_loader, device):
    """评估集成模型"""
    total_correct = 0
    total_samples = 0

    for model in ensemble.models:
        model.eval()

    with torch.no_grad():
        for batch in tqdm(val_loader, desc='评估中'):
            try:
                features = batch['features'].to(device)
                labels = batch['labels'].to(device)

                # 集成预测
                predictions = ensemble.predict(features)
                pred_classes = torch.argmax(predictions, dim=-1)

                # 计算准确率
                mask = (labels != -100)
                if mask.sum() > 0:
                    correct = (pred_classes == labels).float() * mask.float()
                    total_correct += correct.sum().item()
                    total_samples += mask.sum().item()

            except Exception as e:
                continue

    accuracy = total_correct / max(total_samples, 1)
    return accuracy

if __name__ == "__main__":
    try:
        best_accuracy = train_breakthrough_model()

        print(f"\n🏁 训练完成！")
        print(f"最终准确率: {best_accuracy*100:.1f}%")

        if best_accuracy >= 0.7:
            print("🎉 成功达到70%目标！")
        elif best_accuracy >= 0.65:
            print("🎯 接近目标，继续优化可达到70%")
        elif best_accuracy > 0.55:
            print("📈 显著改进，为进一步优化奠定基础")
        else:
            print("📊 需要重新评估策略")

    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()