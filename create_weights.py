#!/usr/bin/env python3
"""
快速创建密码子权重文件
"""

import json

# 基于分析结果手动创建权重
codon_weights = {
    # 氨基酸A (4个密码子)
    'GCT': 1.558, 'GCC': 0.925, 'GCA': 1.174, 'GCG': 0.702,
    
    # 氨基酸C (2个密码子)
    'TGT': 1.125, 'TGC': 0.900,
    
    # 氨基酸D (2个密码子)
    'GAT': 0.798, 'GAC': 1.338,
    
    # 氨基酸E (2个密码子)
    'GAA': 0.722, 'GAG': 1.626,
    
    # 氨基酸F (2个密码子)
    'TTT': 0.874, 'TTC': 1.169,
    
    # 氨基酸G (4个密码子)
    'GGT': 0.738, 'GGC': 0.615, 'GGA': 2.355, 'GGG': 1.681,
    
    # 氨基酸H (2个密码子)
    'CAT': 0.880, 'CAC': 1.158,
    
    # 氨基酸I (3个密码子)
    'ATT': 0.656, 'ATC': 0.791, 'ATA': 4.745,
    
    # 氨基酸K (2个密码子)
    'AAA': 0.651, 'AAG': 2.154,
    
    # 氨基酸L (6个密码子)
    'TTA': 1.284, 'TTG': 1.317, 'CTT': 1.619, 'CTC': 1.596, 'CTA': 4.644, 'CTG': 0.333,
    
    # 氨基酸M (1个密码子)
    'ATG': 1.000,
    
    # 氨基酸N (2个密码子)
    'AAT': 1.116, 'AAC': 0.906,
    
    # 氨基酸P (4个密码子)
    'CCT': 1.585, 'CCC': 2.067, 'CCA': 1.309, 'CCG': 0.471,
    
    # 氨基酸Q (2个密码子)
    'CAA': 1.449, 'CAG': 0.763,
    
    # 氨基酸R (6个密码子)
    'CGT': 0.434, 'CGC': 0.412, 'CGA': 2.657, 'CGG': 1.782, 'AGA': 4.652, 'AGG': 8.646,
    
    # 氨基酸S (6个密码子)
    'TCT': 1.137, 'TCC': 1.113, 'TCA': 1.372, 'TCG': 1.088, 'AGT': 1.113, 'AGC': 0.597,
    
    # 氨基酸T (4个密码子)
    'ACT': 1.502, 'ACC': 0.571, 'ACA': 1.951, 'ACG': 0.934,
    
    # 氨基酸V (4个密码子)
    'GTT': 0.961, 'GTC': 1.168, 'GTA': 1.600, 'GTG': 0.676,
    
    # 氨基酸W (1个密码子)
    'TGG': 1.000,
    
    # 氨基酸Y (2个密码子)
    'TAT': 0.877, 'TAC': 1.164
}

# 氨基酸权重（基于密码子数量的倒数）
aa_weights = {
    'A': 0.25, 'C': 0.5, 'D': 0.5, 'E': 0.5, 'F': 0.5,
    'G': 0.25, 'H': 0.5, 'I': 0.333, 'K': 0.5, 'L': 0.167,
    'M': 1.0, 'N': 0.5, 'P': 0.25, 'Q': 0.5, 'R': 0.167,
    'S': 0.167, 'T': 0.25, 'V': 0.25, 'W': 1.0, 'Y': 0.5
}

# 氨基酸到密码子的映射
aa_codon_counts = {
    'A': 4, 'C': 2, 'D': 2, 'E': 2, 'F': 2,
    'G': 4, 'H': 2, 'I': 3, 'K': 2, 'L': 6,
    'M': 1, 'N': 2, 'P': 4, 'Q': 2, 'R': 6,
    'S': 6, 'T': 4, 'V': 4, 'W': 1, 'Y': 2
}

# 模拟使用统计
codon_usage_counts = {}
for codon in codon_weights.keys():
    # 基于权重的倒数来模拟使用频率
    codon_usage_counts[codon] = int(10000 / codon_weights[codon])

# 氨基酸密码子使用统计
aa_codon_usage = {
    'A': {'GCT': 18470, 'GCC': 31112, 'GCA': 24515, 'GCG': 41023},
    'C': {'TGT': 6195, 'TGC': 7742},
    'D': {'GAT': 38758, 'GAC': 23129},
    'E': {'GAA': 48149, 'GAG': 21387},
    'F': {'TTT': 27025, 'TTC': 20213},
    'G': {'GGT': 30192, 'GGC': 36260, 'GGA': 9467, 'GGG': 13261},
    'H': {'CAT': 15427, 'CAC': 11726},
    'I': {'ATT': 36966, 'ATC': 30665, 'ATA': 5110},
    'K': {'AAA': 40847, 'AAG': 12345},
    'L': {'TTA': 16639, 'TTG': 16221, 'CTT': 13197, 'CTC': 13381, 'CTA': 4600, 'CTG': 64131},
    'M': {'ATG': 34367},
    'N': {'AAT': 21230, 'AAC': 26165},
    'P': {'CCT': 8431, 'CCC': 6466, 'CCA': 10212, 'CCG': 28348},
    'Q': {'CAA': 18312, 'CAG': 34766},
    'R': {'CGT': 25223, 'CGC': 26528, 'CGA': 4116, 'CGG': 6139, 'AGA': 2351, 'AGG': 1265},
    'S': {'TCT': 10179, 'TCC': 10397, 'TCA': 8434, 'TCG': 10639, 'AGT': 10399, 'AGC': 19394},
    'T': {'ACT': 10785, 'ACC': 28380, 'ACA': 8304, 'ACG': 17341},
    'V': {'GTT': 22257, 'GTC': 18317, 'GTA': 13371, 'GTG': 31649},
    'W': {'TGG': 18426},
    'Y': {'TAT': 19658, 'TAC': 14812}
}

# 创建完整的权重信息
weight_info = {
    'aa_weights': aa_weights,
    'codon_weights': codon_weights,
    'aa_codon_counts': aa_codon_counts,
    'codon_usage_counts': codon_usage_counts,
    'aa_codon_usage': aa_codon_usage
}

# 保存到文件
with open('codon_weights.json', 'w') as f:
    json.dump(weight_info, f, indent=2)

print("密码子权重文件已创建: codon_weights.json")
print(f"包含 {len(codon_weights)} 个密码子的权重信息")
print(f"权重范围: {min(codon_weights.values()):.3f} - {max(codon_weights.values()):.3f}")

# 显示最高权重的密码子
sorted_weights = sorted(codon_weights.items(), key=lambda x: x[1], reverse=True)
print("\n最高权重密码子 (最需要关注的罕见密码子):")
for codon, weight in sorted_weights[:10]:
    print(f"  {codon}: 权重={weight:.3f}")
