#!/usr/bin/env python3
"""
改进训练脚本 v2
基于抗过拟合成功的基础，进一步提升准确度到80%
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import matplotlib.pyplot as plt
import gc
import os
import time
import random
warnings.filterwarnings('ignore')

from fixed_training import FixedCodonPreferenceModel, calculate_accuracy
from improved_training import ImprovedCodonDataset, collate_fn

class AdvancedCodonModel(nn.Module):
    """改进的密码子预测模型"""
    
    def __init__(self, esm_model_path="facebook/esm2_t33_650M_UR50D"):
        super().__init__()
        
        # 加载ESM2模型
        from transformers import EsmModel, EsmTokenizer
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            self.tokenizer = EsmTokenizer.from_pretrained(local_path)
            self.esm2 = EsmModel.from_pretrained(local_path)
        except:
            self.tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
            self.esm2 = EsmModel.from_pretrained(esm_model_path)
        
        # 冻结ESM2参数
        for param in self.esm2.parameters():
            param.requires_grad = False
            
        self.hidden_size = self.esm2.config.hidden_size
        
        # 构建遗传密码表
        self.genetic_code = self._build_genetic_code()
        self.aa_to_codons = self._build_aa_to_codon_mapping()
        self.codon_to_idx = self._build_codon_vocab()
        
        # 改进的特征提取层
        self.feature_norm = nn.LayerNorm(self.hidden_size)
        self.feature_dropout = nn.Dropout(0.2)
        
        # 多尺度特征提取
        self.local_conv = nn.Conv1d(self.hidden_size, 256, kernel_size=3, padding=1)
        self.global_conv = nn.Conv1d(self.hidden_size, 256, kernel_size=7, padding=3)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(512, num_heads=8, dropout=0.1, batch_first=True)
        
        # 改进的预测头
        self.predictor = nn.Sequential(
            nn.LayerNorm(512),
            nn.Dropout(0.3),
            
            nn.Linear(512, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(256, 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Linear(128, len(self.codon_to_idx))
        )
        
        # 权重初始化
        self._init_weights()
        
    def _init_weights(self):
        """权重初始化"""
        for module in [self.local_conv, self.global_conv, self.predictor]:
            for layer in module:
                if isinstance(layer, (nn.Linear, nn.Conv1d)):
                    nn.init.xavier_uniform_(layer.weight, gain=0.1)
                    if layer.bias is not None:
                        nn.init.zeros_(layer.bias)
    
    def _build_genetic_code(self):
        return {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def _build_aa_to_codon_mapping(self):
        aa_to_codons = {}
        for codon, aa in self.genetic_code.items():
            if aa != '*':
                if aa not in aa_to_codons:
                    aa_to_codons[aa] = []
                aa_to_codons[aa].append(codon)
        return aa_to_codons
    
    def _build_codon_vocab(self):
        codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        return {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    def forward(self, protein_sequences):
        """前向传播"""
        # 编码蛋白质序列
        encoded = self.tokenizer(
            protein_sequences,
            padding=True,
            truncation=True,
            max_length=1024,
            return_tensors="pt"
        )
        
        input_ids = encoded['input_ids'].to(next(self.parameters()).device)
        attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
        
        # ESM2特征提取
        with torch.no_grad():
            esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
        
        # 基础特征处理
        features = self.feature_norm(esm_output.last_hidden_state)
        features = self.feature_dropout(features)
        
        # 多尺度卷积特征
        # features: [batch, seq, hidden] -> [batch, hidden, seq]
        features_transposed = features.transpose(1, 2)
        
        local_features = self.local_conv(features_transposed)  # [batch, 256, seq]
        global_features = self.global_conv(features_transposed)  # [batch, 256, seq]
        
        # 合并特征: [batch, 512, seq] -> [batch, seq, 512]
        combined_features = torch.cat([local_features, global_features], dim=1).transpose(1, 2)
        
        # 自注意力
        attended_features, _ = self.attention(combined_features, combined_features, combined_features)
        
        # 残差连接
        enhanced_features = combined_features + attended_features
        
        # 预测
        logits = self.predictor(enhanced_features)
        
        # 数值稳定性
        logits = torch.clamp(logits, min=-8, max=8)
        
        return logits

class DataAugmentedDataset(Dataset):
    """数据增强数据集"""
    
    def __init__(self, proteins, nucleotides, codon_to_idx, augment_prob=0.3):
        self.proteins = proteins
        self.nucleotides = nucleotides
        self.codon_to_idx = codon_to_idx
        self.augment_prob = augment_prob
        
        # 构建氨基酸到密码子的映射
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        # 构建氨基酸到密码子映射
        self.aa_to_codons = {}
        for codon, aa in self.genetic_code.items():
            if aa != '*':
                if aa not in self.aa_to_codons:
                    self.aa_to_codons[aa] = []
                self.aa_to_codons[aa].append(codon)
        
        self.valid_pairs = []
        self._process_data()
    
    def _process_data(self):
        """处理数据"""
        print(f"处理 {len(self.proteins)} 条原始数据...")
        
        for idx, (protein, nucleotide) in enumerate(zip(self.proteins, self.nucleotides)):
            if len(protein) * 3 == len(nucleotide):
                # 提取密码子
                codons = []
                valid_sequence = True
                
                for i in range(0, len(nucleotide), 3):
                    codon = nucleotide[i:i+3]
                    if codon in self.codon_to_idx:
                        codons.append(self.codon_to_idx[codon])
                    else:
                        valid_sequence = False
                        break
                
                if valid_sequence and len(codons) == len(protein):
                    self.valid_pairs.append((protein, codons))
            
            if idx % 500 == 0:
                print(f"  处理进度: {idx}/{len(self.proteins)}, 有效数据: {len(self.valid_pairs)}")
        
        print(f"数据处理完成: {len(self.valid_pairs)} 条有效数据")
    
    def augment_sequence(self, protein, codons):
        """数据增强：同义密码子替换"""
        if random.random() > self.augment_prob:
            return protein, codons
        
        new_codons = codons.copy()
        
        # 随机替换一些密码子
        for i, aa in enumerate(protein):
            if aa in self.aa_to_codons and len(self.aa_to_codons[aa]) > 1:
                if random.random() < 0.2:  # 20%概率替换
                    # 随机选择同义密码子
                    possible_codons = self.aa_to_codons[aa]
                    new_codon = random.choice(possible_codons)
                    if new_codon in self.codon_to_idx:
                        new_codons[i] = self.codon_to_idx[new_codon]
        
        return protein, new_codons
    
    def __len__(self):
        return len(self.valid_pairs)
    
    def __getitem__(self, idx):
        protein, codons = self.valid_pairs[idx]
        
        # 应用数据增强
        protein, codons = self.augment_sequence(protein, codons)
        
        return {
            'protein': protein,
            'codons': torch.tensor(codons, dtype=torch.long)
        }

def enhanced_collate_fn(batch):
    """增强的批处理函数"""
    proteins = [item['protein'] for item in batch]
    codons = [item['codons'] for item in batch]
    
    # 填充密码子序列
    max_len = max(len(codon_seq) for codon_seq in codons)
    padded_codons = []
    
    for codon_seq in codons:
        padded = torch.full((max_len,), -100, dtype=torch.long)
        padded[:len(codon_seq)] = codon_seq
        padded_codons.append(padded)
    
    return {
        'proteins': proteins,
        'codons': torch.stack(padded_codons)
    }

def train_improved_model_v2():
    """训练改进模型v2"""
    print("🚀 开始改进训练 v2")
    print("基于抗过拟合成功基础，目标：80%准确度")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]
    print(f"使用 {len(df)} 条数据")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # 创建改进模型
    model = AdvancedCodonModel()
    model = model.to(device)
    
    # 创建数据增强数据集
    dataset = DataAugmentedDataset(proteins, nucleotides, model.codon_to_idx, augment_prob=0.4)
    
    if len(dataset) == 0:
        print("❌ 数据集为空，请检查数据处理逻辑")
        return 0.0
    
    # 数据划分 - 保持抗过拟合策略
    train_size = int(0.75 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    print(f"训练集: {len(train_dataset)}, 验证集: {len(val_dataset)}")
    
    # 数据加载器
    batch_size = 6
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=enhanced_collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=enhanced_collate_fn)
    
    # 加载权重
    try:
        with open('pure_codon_weights.json', 'r') as f:
            weight_info = json.load(f)
        codon_weights = weight_info['mixed_weights']
        weight_tensor = torch.ones(len(model.codon_to_idx))
        
        for codon, weight in codon_weights.items():
            if codon in model.codon_to_idx:
                idx = model.codon_to_idx[codon]
                weight_tensor[idx] = weight
        
        weight_tensor = weight_tensor / weight_tensor.mean()
        weight_tensor = weight_tensor.to(device)
        print("✅ 使用纯密码子权重")
    except:
        weight_tensor = None
        print("⚠️ 使用均匀权重")
    
    # 损失函数 - 继续使用标签平滑
    if weight_tensor is not None:
        criterion = nn.CrossEntropyLoss(weight=weight_tensor, ignore_index=-100, label_smoothing=0.1)
    else:
        criterion = nn.CrossEntropyLoss(ignore_index=-100, label_smoothing=0.1)
    
    # 优化器 - 稍微提高学习率
    optimizer = optim.AdamW(model.parameters(), lr=1e-5, weight_decay=0.05)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=30, T_mult=2)
    
    # 训练历史
    best_val_acc = 0.0
    patience = 100
    patience_counter = 0
    
    print("开始训练...")
    
    for epoch in range(300):
        # 训练
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc=f'Epoch {epoch+1}/300', leave=False):
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                logits = model(proteins)
                
                # 对齐维度
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                loss = criterion(
                    logits.reshape(-1, logits.size(-1)),
                    targets.reshape(-1)
                )
                
                if torch.isnan(loss) or torch.isinf(loss):
                    continue
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                train_acc += calculate_accuracy(logits, targets)
                train_batches += 1
                
            except Exception as e:
                print(f"训练异常: {e}")
                continue
        
        # 验证
        model.eval()
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    val_acc += calculate_accuracy(logits, targets)
                    val_batches += 1
                    
                except Exception as e:
                    continue
        
        # 计算平均值
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        
        scheduler.step()
        
        # 计算过拟合程度
        overfitting_gap = avg_train_acc - avg_val_acc
        
        print(f'Epoch {epoch+1:3d}/300: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%, Gap={overfitting_gap*100:.1f}%')
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_accuracy': avg_val_acc,
                'train_accuracy': avg_train_acc,
                'overfitting_gap': overfitting_gap
            }, 'improved_v2_best_model.pth')
            
            print(f'  🏆 新的最佳模型 (验证准确度: {best_val_acc*100:.1f}%)')
            
            if avg_val_acc >= 0.8:
                print("🎉 达到80%目标！")
                break
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= patience:
            print(f'早停: 验证准确度连续{patience}轮未改善')
            break
        
        # 内存清理
        if (epoch + 1) % 10 == 0:
            gc.collect()
            torch.cuda.empty_cache()
    
    print(f"🏁 训练完成！最佳验证准确度: {best_val_acc*100:.1f}%")
    return best_val_acc

if __name__ == "__main__":
    best_acc = train_improved_model_v2()
    
    if best_acc >= 0.8:
        print("🎉 成功达到80%目标准确度！")
    elif best_acc >= 0.65:
        print(f"显著改进！准确度达到 {best_acc*100:.1f}%")
        print("建议尝试方案3: 模型集成")
    else:
        print(f"当前最佳准确度: {best_acc*100:.1f}%")
        print("建议尝试XGBoost对比实验")
