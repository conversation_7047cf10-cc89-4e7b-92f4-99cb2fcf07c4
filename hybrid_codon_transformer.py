#!/usr/bin/env python3
"""
混合CodonTransformer - Hybrid CodonTransformer
结合54.8%成功方法 + CodonTransformer核心特性
目标准确率80%+

核心设计：
1. 基于54.8%成功的深度学习架构
2. 添加CodonTransformer的AA_CODON合并序列
3. 融合生物学特征工程
4. 大肠杆菌专用优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.cuda.amp import GradScaler, autocast
import pandas as pd
import numpy as np
import json
import time
import warnings
from tqdm import tqdm
import gc
warnings.filterwarnings('ignore')

# 设置GPU优化
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False

class HybridCodonTransformer(nn.Module):
    """混合CodonTransformer：成功方法 + CodonTransformer特性"""
    
    def __init__(self, 
                 vocab_size=25,
                 codon_vocab_size=64,
                 hidden_dim=512,
                 num_layers=6,
                 num_heads=8,
                 dropout=0.1,
                 max_length=512):
        super().__init__()
        
        self.hidden_dim = hidden_dim
        self.max_length = max_length
        
        # 大肠杆菌密码子使用频率
        self.ecoli_codon_usage = {
            'TTT': 0.58, 'TTC': 0.42, 'TTA': 0.14, 'TTG': 0.13,
            'TCT': 0.17, 'TCC': 0.15, 'TCA': 0.14, 'TCG': 0.14,
            'TAT': 0.59, 'TAC': 0.41, 'TGT': 0.46, 'TGC': 0.54, 'TGG': 1.00,
            'CTT': 0.12, 'CTC': 0.10, 'CTA': 0.04, 'CTG': 0.47,
            'CCT': 0.18, 'CCC': 0.13, 'CCA': 0.20, 'CCG': 0.49,
            'CAT': 0.57, 'CAC': 0.43, 'CAA': 0.34, 'CAG': 0.66,
            'CGT': 0.36, 'CGC': 0.36, 'CGA': 0.07, 'CGG': 0.11,
            'ATT': 0.49, 'ATC': 0.39, 'ATA': 0.11, 'ATG': 1.00,
            'ACT': 0.19, 'ACC': 0.40, 'ACA': 0.17, 'ACG': 0.25,
            'AAT': 0.49, 'AAC': 0.51, 'AAA': 0.74, 'AAG': 0.26,
            'AGT': 0.16, 'AGC': 0.25, 'AGA': 0.07, 'AGG': 0.04,
            'GTT': 0.28, 'GTC': 0.20, 'GTA': 0.17, 'GTG': 0.35,
            'GCT': 0.18, 'GCC': 0.26, 'GCA': 0.23, 'GCG': 0.33,
            'GAT': 0.63, 'GAC': 0.37, 'GAA': 0.68, 'GAG': 0.32,
            'GGT': 0.35, 'GGC': 0.37, 'GGA': 0.13, 'GGG': 0.15,
            'TAA': 0.61, 'TAG': 0.09, 'TGA': 0.30
        }
        
        # 创建词汇表
        self.create_vocabularies()
        
        # 1. 蛋白质序列编码器（基于54.8%成功方法）
        self.aa_embedding = nn.Embedding(vocab_size, hidden_dim)
        self.pos_embedding = nn.Embedding(max_length, hidden_dim)
        
        # 2. CodonTransformer特性：AA_CODON合并序列
        self.merged_embedding = nn.Embedding(len(self.aa_codon_to_idx), hidden_dim)
        
        # 3. 生物学特征编码器（基于54.8%成功的特征重要性）
        self.bio_feature_encoder = nn.Sequential(
            nn.Linear(17, hidden_dim // 2),  # 17个生物学特征
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim)  # 输出维度匹配hidden_dim
        )
        
        # 4. 特征融合层
        self.feature_fusion = nn.MultiheadAttention(
            hidden_dim, num_heads, dropout=dropout, batch_first=True
        )
        
        # 5. Transformer编码器（优化版）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 2,  # 减少复杂度
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 6. 输出层
        self.output_norm = nn.LayerNorm(hidden_dim)
        self.output_dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(hidden_dim, 61)  # 61个密码子
        
        # 初始化权重
        self._init_weights()
        
        print(f"✅ 混合CodonTransformer初始化完成")
        print(f"   隐藏维度: {hidden_dim}")
        print(f"   层数: {num_layers}")
        print(f"   AA_CODON词汇量: {len(self.aa_codon_to_idx)}")
        print(f"   生物学特征: 17个")
    
    def create_vocabularies(self):
        """创建词汇表"""
        # 氨基酸到索引
        self.aa_to_idx = {
            'A': 1, 'R': 2, 'N': 3, 'D': 4, 'C': 5, 'Q': 6, 'E': 7, 'G': 8,
            'H': 9, 'I': 10, 'L': 11, 'K': 12, 'M': 13, 'F': 14, 'P': 15,
            'S': 16, 'T': 17, 'W': 18, 'Y': 19, 'V': 20, '<PAD>': 0, '<UNK>': 21,
            '<MASK>': 22, '*': 23
        }
        
        # 遗传密码
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G',
            'TAA': '*', 'TAG': '*', 'TGA': '*'
        }
        
        # AA_CODON合并词汇表
        aa_codon_tokens = ['<PAD>', '<UNK>', '<MASK>']
        for codon, aa in self.genetic_code.items():
            token = f"{aa}_{codon}"
            aa_codon_tokens.append(token)
        
        self.aa_codon_to_idx = {token: idx for idx, token in enumerate(aa_codon_tokens)}
        self.idx_to_aa_codon = {idx: token for token, idx in self.aa_codon_to_idx.items()}
        
        # 密码子到索引（只包含61个有效密码子，排除终止密码子）
        valid_codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        self.codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(valid_codons))}
        
        # 氨基酸属性（基于54.8%成功方法的特征重要性）
        self.aa_properties = {
            'A': [1.8, 0, 0, 0, 0],   # 疏水性, 极性, 正电, 负电, 芳香性
            'R': [-4.5, 1, 1, 0, 0], 'N': [-3.5, 1, 0, 0, 0], 'D': [-3.5, 1, 0, 1, 0],
            'C': [2.5, 0, 0, 0, 0],  'Q': [-3.5, 1, 0, 0, 0], 'E': [-3.5, 1, 0, 1, 0],
            'G': [-0.4, 0, 0, 0, 0], 'H': [-3.2, 1, 1, 0, 1], 'I': [4.5, 0, 0, 0, 0],
            'L': [3.8, 0, 0, 0, 0],  'K': [-3.9, 1, 1, 0, 0], 'M': [1.9, 0, 0, 0, 0],
            'F': [2.8, 0, 0, 0, 1],  'P': [-1.6, 0, 0, 0, 0], 'S': [-0.8, 1, 0, 0, 0],
            'T': [-0.7, 1, 0, 0, 0], 'W': [-0.9, 0, 0, 0, 1], 'Y': [-1.3, 1, 0, 0, 1],
            'V': [4.2, 0, 0, 0, 0],  '*': [0, 0, 0, 0, 0]
        }
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.02)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, mean=0, std=0.02)
    
    def extract_biological_features(self, protein_seq, nucleotide_seq, position):
        """提取生物学特征（基于54.8%成功方法）"""
        features = []
        
        # 当前氨基酸属性
        aa = protein_seq[position] if position < len(protein_seq) else '*'
        aa_props = self.aa_properties.get(aa, [0] * 5)
        features.extend(aa_props)  # 5个特征
        
        # 前一个氨基酸属性
        prev_aa = protein_seq[position - 1] if position > 0 else '*'
        prev_props = self.aa_properties.get(prev_aa, [0] * 5)
        features.extend(prev_props)  # 5个特征
        
        # 后一个氨基酸属性
        next_aa = protein_seq[position + 1] if position + 1 < len(protein_seq) else '*'
        next_props = self.aa_properties.get(next_aa, [0] * 5)
        features.extend(next_props)  # 5个特征
        
        # 位置特征
        rel_position = position / len(protein_seq)
        features.append(rel_position)  # 1个特征
        
        # 序列长度
        features.append(len(protein_seq) / 1000.0)  # 1个特征，归一化
        
        return features  # 总共17个特征
    
    def create_aa_codon_sequence(self, protein_seq, nucleotide_seq):
        """创建AA_CODON合并序列"""
        aa_codon_tokens = []
        
        min_len = min(len(protein_seq), len(nucleotide_seq) // 3)
        
        for i in range(min_len):
            aa = protein_seq[i]
            codon = nucleotide_seq[i*3:(i+1)*3]
            
            if len(codon) == 3:
                token = f"{aa}_{codon}"
                if token in self.aa_codon_to_idx:
                    aa_codon_tokens.append(self.aa_codon_to_idx[token])
                else:
                    aa_codon_tokens.append(self.aa_codon_to_idx['<UNK>'])
            else:
                aa_codon_tokens.append(self.aa_codon_to_idx['<UNK>'])
        
        return aa_codon_tokens
    
    def forward(self, protein_sequences, nucleotide_sequences, labels=None):
        """前向传播"""
        batch_size = len(protein_sequences)
        device = next(self.parameters()).device
        
        # 1. 编码蛋白质序列
        encoded_proteins = []
        aa_codon_sequences = []
        bio_features_batch = []
        
        max_len = 0
        
        for protein_seq, nucleotide_seq in zip(protein_sequences, nucleotide_sequences):
            # 编码蛋白质
            encoded = [self.aa_to_idx.get(aa, self.aa_to_idx['<UNK>']) for aa in protein_seq]
            encoded_proteins.append(encoded)
            
            # 创建AA_CODON序列
            aa_codon_seq = self.create_aa_codon_sequence(protein_seq, nucleotide_seq)
            aa_codon_sequences.append(aa_codon_seq)
            
            # 提取生物学特征
            bio_features = []
            for pos in range(len(protein_seq)):
                features = self.extract_biological_features(protein_seq, nucleotide_seq, pos)
                bio_features.append(features)
            bio_features_batch.append(bio_features)
            
            max_len = max(max_len, len(encoded))
        
        # 2. 填充序列
        padded_proteins = []
        padded_aa_codons = []
        padded_bio_features = []
        
        for encoded, aa_codon_seq, bio_features in zip(encoded_proteins, aa_codon_sequences, bio_features_batch):
            pad_len = max_len - len(encoded)
            
            # 填充蛋白质序列
            padded_proteins.append(encoded + [self.aa_to_idx['<PAD>']] * pad_len)
            
            # 填充AA_CODON序列
            padded_aa_codons.append(aa_codon_seq + [self.aa_codon_to_idx['<PAD>']] * pad_len)
            
            # 填充生物学特征
            padded_bio_features.append(bio_features + [[0] * 17] * pad_len)
        
        # 3. 转换为张量
        protein_ids = torch.tensor(padded_proteins, dtype=torch.long, device=device)
        aa_codon_ids = torch.tensor(padded_aa_codons, dtype=torch.long, device=device)
        bio_features = torch.tensor(padded_bio_features, dtype=torch.float, device=device)
        
        # 4. 嵌入
        # 蛋白质嵌入
        protein_embeddings = self.aa_embedding(protein_ids)
        
        # 位置嵌入
        positions = torch.arange(max_len, device=device).unsqueeze(0).expand(batch_size, -1)
        pos_embeddings = self.pos_embedding(positions)
        
        # AA_CODON嵌入
        aa_codon_embeddings = self.merged_embedding(aa_codon_ids)
        
        # 生物学特征嵌入
        bio_embeddings = self.bio_feature_encoder(bio_features)
        
        # 5. 特征融合
        # 组合蛋白质和位置嵌入
        protein_features = protein_embeddings + pos_embeddings
        
        # 融合所有特征
        combined_features = protein_features + aa_codon_embeddings + bio_embeddings
        
        # 6. 注意力掩码
        attention_mask = (protein_ids != self.aa_to_idx['<PAD>'])
        
        # 7. Transformer编码
        encoded = self.transformer_encoder(
            combined_features,
            src_key_padding_mask=~attention_mask
        )
        
        # 8. 输出预测
        encoded = self.output_norm(encoded)
        encoded = self.output_dropout(encoded)
        logits = self.classifier(encoded)
        
        # 9. 计算损失
        loss = None
        if labels is not None:
            labels = labels.to(device)
            loss_fct = nn.CrossEntropyLoss(ignore_index=-100)
            loss = loss_fct(logits.view(-1, 61), labels.view(-1))
        
        return {
            'logits': logits,
            'loss': loss,
            'attention_mask': attention_mask
        }


class HybridCodonDataset(Dataset):
    """混合CodonTransformer数据集"""

    def __init__(self, data, max_length=512):
        self.data = data
        self.max_length = max_length

        # 创建密码子到索引的映射
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G',
            'TAA': '*', 'TAG': '*', 'TGA': '*'
        }

        # 只包含61个有效密码子（排除终止密码子）
        valid_codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        self.codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(valid_codons))}

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.iloc[idx]

        protein_seq = row['protein_sequence'][:self.max_length]
        nucleotide_seq = row['nucleotide_sequence'][:self.max_length*3]

        # 创建标签序列
        labels = []
        min_len = min(len(protein_seq), len(nucleotide_seq) // 3)

        for i in range(min_len):
            codon = nucleotide_seq[i*3:(i+1)*3]
            if len(codon) == 3 and codon in self.codon_to_idx:
                codon_idx = self.codon_to_idx[codon]
                # 确保索引在有效范围内
                if 0 <= codon_idx < 61:
                    labels.append(codon_idx)
                else:
                    labels.append(-100)  # 忽略超出范围的密码子
            else:
                labels.append(-100)  # 忽略无效密码子

        return {
            'protein_sequence': protein_seq,
            'nucleotide_sequence': nucleotide_seq,
            'labels': torch.tensor(labels, dtype=torch.long)
        }


def collate_fn(batch):
    """批处理函数"""
    protein_sequences = [item['protein_sequence'] for item in batch]
    nucleotide_sequences = [item['nucleotide_sequence'] for item in batch]
    labels = [item['labels'] for item in batch]

    # 填充标签到相同长度
    max_len = max(len(label_seq) for label_seq in labels)

    padded_labels = []
    for label_seq in labels:
        pad_len = max_len - len(label_seq)
        if pad_len > 0:
            padded_labels.append(torch.cat([label_seq, torch.full((pad_len,), -100, dtype=torch.long)]))
        else:
            padded_labels.append(label_seq)

    return {
        'protein_sequences': protein_sequences,
        'nucleotide_sequences': nucleotide_sequences,
        'labels': torch.stack(padded_labels)
    }


def train_hybrid_model():
    """训练混合CodonTransformer"""
    print("🧬 混合CodonTransformer训练")
    print("结合54.8%成功方法 + CodonTransformer特性")
    print("目标：准确率超过80%")
    print("=" * 70)

    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    if device.type == 'cuda':
        print(f"GPU名称: {torch.cuda.get_device_name()}")
        print(f"GPU显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

    # 加载数据
    print("\n📊 加载大肠杆菌数据...")
    data = pd.read_csv('processed_BL21_data.csv')
    print(f"大肠杆菌样本数: {len(data)}")

    # 数据分割
    train_size = int(0.8 * len(data))
    train_data = data[:train_size]
    val_data = data[train_size:]

    # 创建数据集
    train_dataset = HybridCodonDataset(train_data)
    val_dataset = HybridCodonDataset(val_data)

    # 创建数据加载器
    batch_size = 16  # 适中的批量大小
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)

    print(f"✅ 混合数据集创建完成: {len(train_data)} 训练, {len(val_data)} 验证")
    print(f"训练批次: {len(train_loader)}, 验证批次: {len(val_loader)}")
    print(f"批量大小: {batch_size}")

    # 创建模型
    print("\n🏗️ 创建混合CodonTransformer...")
    model = HybridCodonTransformer(
        vocab_size=25,
        codon_vocab_size=64,
        hidden_dim=512,  # 适中的隐藏维度
        num_layers=6,    # 适中的层数
        num_heads=8,
        dropout=0.1,
        max_length=512
    ).to(device)

    # 计算参数量
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"可训练参数量: {total_params:,}")

    # 优化器和调度器
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=1e-4,
        epochs=20,
        steps_per_epoch=len(train_loader),
        pct_start=0.1
    )

    # 混合精度训练
    scaler = GradScaler()

    print(f"\n🎯 训练配置:")
    print(f"  学习率: 1e-4")
    print(f"  混合精度: 启用")
    print(f"  训练轮数: 20")
    print(f"  基于54.8%成功方法: 启用")
    print(f"  CodonTransformer特性: 启用")
    print(f"  大肠杆菌专用优化: 启用")

    # 训练循环
    print(f"\n🏃 开始混合模型训练...")

    best_val_acc = 0
    patience = 5
    patience_counter = 0

    for epoch in range(20):
        # 训练阶段
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0

        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/20 [Train]")
        for batch in train_pbar:
            optimizer.zero_grad()

            with autocast():
                outputs = model(
                    batch['protein_sequences'],
                    batch['nucleotide_sequences'],
                    batch['labels'].to(device)
                )
                loss = outputs['loss']

            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            scheduler.step()

            # 计算准确率
            predictions = torch.argmax(outputs['logits'], dim=-1)
            mask = (batch['labels'].to(device) != -100)
            correct = (predictions == batch['labels'].to(device)) & mask

            train_loss += loss.item()
            train_correct += correct.sum().item()
            train_total += mask.sum().item()

            current_acc = train_correct / train_total * 100
            train_pbar.set_postfix({'loss': f'{loss.item():.4f}', 'acc': f'{current_acc:.1f}%'})

        # 验证阶段
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/20 [Val]")
            for batch in val_pbar:
                outputs = model(
                    batch['protein_sequences'],
                    batch['nucleotide_sequences'],
                    batch['labels'].to(device)
                )
                loss = outputs['loss']

                predictions = torch.argmax(outputs['logits'], dim=-1)
                mask = (batch['labels'].to(device) != -100)
                correct = (predictions == batch['labels'].to(device)) & mask

                val_loss += loss.item()
                val_correct += correct.sum().item()
                val_total += mask.sum().item()

                current_acc = val_correct / val_total * 100
                val_pbar.set_postfix({'loss': f'{loss.item():.4f}', 'acc': f'{current_acc:.1f}%'})

        # 计算平均指标
        train_acc = train_correct / train_total * 100
        val_acc = val_correct / val_total * 100
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        current_lr = scheduler.get_last_lr()[0]

        print(f"Epoch {epoch+1:2d}/20: Train={train_acc:.1f}%, Val={val_acc:.1f}%, "
              f"Loss={avg_val_loss:.4f}, LR={current_lr:.2e}")

        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            torch.save(model.state_dict(), 'hybrid_codon_transformer_best.pth')
            print(f"  🏆 新的最佳模型! 验证准确率: {val_acc:.1f}%")
        else:
            patience_counter += 1

        # 早停
        if patience_counter >= patience:
            print(f"早停: {patience} 轮无改进")
            break

        # 内存清理
        if epoch % 5 == 0:
            gc.collect()
            torch.cuda.empty_cache()

    print(f"\n🏁 混合CodonTransformer训练完成!")
    print(f"最佳验证准确率: {best_val_acc:.1f}%")

    # 保存结果
    results = {
        'hybrid_codon_transformer_accuracy': best_val_acc / 100,
        'previous_best': 0.548,
        'improvement': (best_val_acc / 100) - 0.548,
        'target_achieved': best_val_acc >= 80.0,
        'model_parameters': total_params,
        'batch_size': batch_size,
        'traditional_features': True,
        'codon_transformer_features': True,
        'aa_codon_format': True,
        'biological_features': True,
        'ecoli_specialized': True
    }

    with open('hybrid_codon_transformer_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n📈 性能比较:")
    print(f"   之前最佳 (传统方法): 54.8%")
    print(f"   混合CodonTransformer: {best_val_acc:.1f}%")
    if best_val_acc > 54.8:
        print(f"   🎉 提升: +{best_val_acc - 54.8:.1f}%")
    else:
        print(f"   📉 下降: {best_val_acc - 54.8:.1f}%")

    print(f"💾 结果已保存到: hybrid_codon_transformer_results.json")

    return best_val_acc


if __name__ == "__main__":
    train_hybrid_model()
