#!/usr/bin/env python3
"""
GPU优化训练 - GPU Optimized Training
利用RTX 4090的强大性能进行高效训练

优化策略：
1. GPU加速的深度学习模型
2. 大批量训练（充分利用24GB显存）
3. 混合精度训练
4. 高效数据加载
5. 模型并行和梯度累积
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torch.cuda.amp import GradScaler, autocast
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import time
import math
import gc
warnings.filterwarnings('ignore')

# 设置GPU优化
torch.backends.cudnn.benchmark = True  # 优化cudnn性能
torch.backends.cudnn.deterministic = False  # 允许非确定性算法以提高性能

class GPUOptimizedDataset(Dataset):
    """GPU优化数据集"""
    
    def __init__(self, proteins, nucleotides, codon_to_idx, device='cuda'):
        self.device = device
        self.codon_to_idx = codon_to_idx
        
        # 遗传密码
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        # 预处理数据
        self.processed_data = []
        self._preprocess_data(proteins, nucleotides)
        
        print(f"✅ GPU数据集创建完成: {len(self.processed_data)} 个样本")
    
    def _preprocess_data(self, proteins, nucleotides):
        """预处理数据"""
        print("🔧 GPU数据预处理中...")
        
        for i, (protein_seq, nucleotide_seq) in enumerate(zip(proteins, nucleotides)):
            try:
                # 基本验证
                if len(nucleotide_seq) % 3 != 0:
                    continue
                
                codons = [nucleotide_seq[j:j+3] for j in range(0, len(nucleotide_seq), 3)]
                if len(codons) > len(protein_seq):
                    codons = codons[:len(protein_seq)]
                
                if len(codons) != len(protein_seq):
                    continue
                
                # 验证并创建标签
                valid_positions = []
                valid_codons = []
                
                for pos, (aa, codon) in enumerate(zip(protein_seq, codons)):
                    if codon not in self.codon_to_idx:
                        continue
                    
                    expected_aa = self.genetic_code.get(codon, 'X')
                    if expected_aa != aa:
                        continue
                    
                    valid_positions.append(pos)
                    valid_codons.append(self.codon_to_idx[codon])
                
                if len(valid_positions) > 0:
                    self.processed_data.append({
                        'protein_seq': protein_seq,
                        'nucleotide_seq': nucleotide_seq,
                        'positions': valid_positions,
                        'labels': valid_codons
                    })
                    
            except Exception as e:
                continue
    
    def __len__(self):
        return len(self.processed_data)
    
    def __getitem__(self, idx):
        data = self.processed_data[idx]
        
        # 返回序列和标签
        return {
            'protein_seq': data['protein_seq'],
            'nucleotide_seq': data['nucleotide_seq'],
            'positions': torch.tensor(data['positions'], dtype=torch.long),
            'labels': torch.tensor(data['labels'], dtype=torch.long)
        }

class GPUOptimizedModel(nn.Module):
    """GPU优化的高性能模型"""
    
    def __init__(self, vocab_size=25, hidden_dim=1024, num_layers=6, num_heads=16, dropout=0.1):
        super().__init__()
        
        # 蛋白质序列编码
        self.aa_embedding = nn.Embedding(vocab_size, hidden_dim)
        self.pos_embedding = nn.Embedding(2048, hidden_dim)  # 位置编码
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True  # Pre-norm for better training stability
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 输出层
        self.output_norm = nn.LayerNorm(hidden_dim)
        self.output_dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(hidden_dim, 61)  # 61个密码子
        
        # 初始化权重
        self._init_weights()
        
        # 氨基酸到索引的映射
        self.aa_to_idx = {
            'A': 1, 'R': 2, 'N': 3, 'D': 4, 'C': 5, 'Q': 6, 'E': 7, 'G': 8,
            'H': 9, 'I': 10, 'L': 11, 'K': 12, 'M': 13, 'F': 14, 'P': 15,
            'S': 16, 'T': 17, 'W': 18, 'Y': 19, 'V': 20, '<PAD>': 0, '<UNK>': 21
        }
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.02)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, mean=0, std=0.02)
    
    def encode_protein(self, protein_seq):
        """编码蛋白质序列"""
        encoded = []
        for aa in protein_seq:
            encoded.append(self.aa_to_idx.get(aa, self.aa_to_idx['<UNK>']))
        return encoded
    
    def forward(self, protein_seqs, max_length=512):
        """前向传播"""
        batch_size = len(protein_seqs)
        
        # 编码蛋白质序列
        encoded_seqs = []
        seq_lengths = []
        
        for seq in protein_seqs:
            encoded = self.encode_protein(seq[:max_length])
            encoded_seqs.append(encoded)
            seq_lengths.append(len(encoded))
        
        # 填充到相同长度
        max_len = max(seq_lengths)
        padded_seqs = []
        attention_masks = []
        
        for encoded, length in zip(encoded_seqs, seq_lengths):
            padded = encoded + [self.aa_to_idx['<PAD>']] * (max_len - length)
            mask = [1] * length + [0] * (max_len - length)
            padded_seqs.append(padded)
            attention_masks.append(mask)
        
        # 转换为张量
        input_ids = torch.tensor(padded_seqs, dtype=torch.long, device=next(self.parameters()).device)
        attention_mask = torch.tensor(attention_masks, dtype=torch.bool, device=next(self.parameters()).device)
        
        # 嵌入
        embeddings = self.aa_embedding(input_ids)
        
        # 位置编码
        positions = torch.arange(max_len, device=input_ids.device).unsqueeze(0).expand(batch_size, -1)
        pos_embeddings = self.pos_embedding(positions)
        
        # 组合嵌入
        hidden_states = embeddings + pos_embeddings
        
        # Transformer编码
        # 创建注意力掩码（True表示忽略）
        transformer_mask = ~attention_mask
        
        encoded = self.transformer(hidden_states, src_key_padding_mask=transformer_mask)
        
        # 输出层
        encoded = self.output_norm(encoded)
        encoded = self.output_dropout(encoded)
        logits = self.classifier(encoded)
        
        return logits, attention_mask

def gpu_collate_fn(batch):
    """GPU优化的批处理函数"""
    protein_seqs = [item['protein_seq'] for item in batch]
    nucleotide_seqs = [item['nucleotide_seq'] for item in batch]
    all_positions = [item['positions'] for item in batch]
    all_labels = [item['labels'] for item in batch]
    
    return {
        'protein_seqs': protein_seqs,
        'nucleotide_seqs': nucleotide_seqs,
        'positions': all_positions,
        'labels': all_labels
    }

def train_gpu_optimized_model():
    """GPU优化训练"""
    print("🚀 GPU优化高性能训练")
    print("利用RTX 4090进行加速训练")
    print("=" * 60)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    print(f"GPU名称: {torch.cuda.get_device_name(0)}")
    print(f"GPU显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 清理GPU缓存
    torch.cuda.empty_cache()
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]  # 限制序列长度
    print(f"使用 {len(df)} 个样本")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # 创建密码子映射
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    codons = [codon for codon, aa in genetic_code.items()]
    codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    print(f"密码子类别数: {len(codon_to_idx)}")
    
    # 创建GPU优化数据集
    dataset = GPUOptimizedDataset(proteins, nucleotides, codon_to_idx, device=device)
    
    if len(dataset) == 0:
        print("❌ 没有有效数据")
        return 0.0
    
    # 数据分割
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # GPU优化的数据加载器 - 使用大批量充分利用GPU
    batch_size = 32  # RTX 4090可以处理更大的批量
    num_workers = 8  # 多进程数据加载
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=gpu_collate_fn,
        num_workers=num_workers,
        pin_memory=True,  # 加速GPU传输
        persistent_workers=True  # 保持worker进程
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=gpu_collate_fn,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=True
    )
    
    print(f"训练批次: {len(train_loader)}, 验证批次: {len(val_loader)}")
    print(f"批量大小: {batch_size}")
    
    # 创建GPU优化模型
    model = GPUOptimizedModel(
        vocab_size=25,
        hidden_dim=1024,  # 大模型充分利用GPU
        num_layers=8,     # 更深的网络
        num_heads=16,     # 更多注意力头
        dropout=0.1
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数量: {total_params:,}")
    
    # GPU优化的训练配置
    criterion = nn.CrossEntropyLoss(ignore_index=-100, label_smoothing=0.1)
    optimizer = optim.AdamW(model.parameters(), lr=2e-4, weight_decay=0.01, eps=1e-6)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=2e-4,
        epochs=50,
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )
    
    # 混合精度训练
    scaler = GradScaler()
    
    print(f"\n🎯 GPU训练配置:")
    print(f"  混合精度训练: 启用")
    print(f"  梯度缩放: 启用")
    print(f"  学习率调度: OneCycle")
    print(f"  最大学习率: 2e-4")
    
    print(f"\n🏃 开始GPU加速训练...")
    
    best_val_acc = 0.0
    patience = 15
    patience_counter = 0
    
    for epoch in range(50):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0
        
        epoch_start_time = time.time()
        
        for batch_idx, batch in enumerate(tqdm(train_loader, desc=f'Epoch {epoch+1}/50 [Train]')):
            try:
                optimizer.zero_grad()
                
                # 使用混合精度
                with autocast():
                    logits, attention_mask = model(batch['protein_seqs'])
                    
                    # 计算损失
                    batch_loss = 0.0
                    batch_correct = 0
                    batch_total = 0
                    
                    for i, (positions, labels) in enumerate(zip(batch['positions'], batch['labels'])):
                        if len(positions) == 0:
                            continue
                        
                        # 获取对应位置的logits
                        seq_logits = logits[i][positions]  # [num_positions, num_classes]
                        seq_labels = labels.to(device)
                        
                        loss = criterion(seq_logits, seq_labels)
                        batch_loss += loss
                        
                        # 计算准确率
                        with torch.no_grad():
                            predictions = torch.argmax(seq_logits, dim=-1)
                            batch_correct += (predictions == seq_labels).sum().item()
                            batch_total += len(seq_labels)
                    
                    if batch_total > 0:
                        batch_loss = batch_loss / len([p for p in batch['positions'] if len(p) > 0])
                
                # 反向传播
                scaler.scale(batch_loss).backward()
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                scaler.step(optimizer)
                scaler.update()
                scheduler.step()
                
                train_loss += batch_loss.item()
                if batch_total > 0:
                    train_acc += batch_correct / batch_total
                train_batches += 1
                
                # 显存管理
                if batch_idx % 100 == 0:
                    torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"训练批次错误: {e}")
                continue
        
        epoch_time = time.time() - epoch_start_time
        
        # 验证阶段
        model.eval()
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f'Epoch {epoch+1}/50 [Val]'):
                try:
                    with autocast():
                        logits, attention_mask = model(batch['protein_seqs'])
                    
                    batch_correct = 0
                    batch_total = 0
                    
                    for i, (positions, labels) in enumerate(zip(batch['positions'], batch['labels'])):
                        if len(positions) == 0:
                            continue
                        
                        seq_logits = logits[i][positions]
                        seq_labels = labels.to(device)
                        
                        predictions = torch.argmax(seq_logits, dim=-1)
                        batch_correct += (predictions == seq_labels).sum().item()
                        batch_total += len(seq_labels)
                    
                    if batch_total > 0:
                        val_acc += batch_correct / batch_total
                        val_batches += 1
                        
                except Exception as e:
                    continue
        
        # 计算平均值
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        current_lr = scheduler.get_last_lr()[0]
        
        print(f'Epoch {epoch+1:3d}/50: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%, '
              f'LR={current_lr:.2e}, Time={epoch_time:.1f}s')
        
        # 检查改进
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            patience_counter = 0
            
            # 保存最佳模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_accuracy': avg_val_acc,
                'train_accuracy': avg_train_acc
            }, 'gpu_optimized_best_model.pth')
            
            print(f'  🏆 新的最佳模型! 验证准确率: {best_val_acc*100:.1f}%')
            
            if avg_val_acc >= 0.7:
                print("🎉 达到70%目标准确率!")
                break
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= patience:
            print(f'早停: {patience} 轮无改进')
            break
        
        # 定期清理显存
        if epoch % 5 == 0:
            torch.cuda.empty_cache()
            gc.collect()
    
    print(f"\n🏁 GPU训练完成!")
    print(f"最佳验证准确率: {best_val_acc*100:.1f}%")
    print(f"之前最佳准确率: 54.8%")
    
    improvement = best_val_acc - 0.548
    if best_val_acc >= 0.7:
        print(f"🎉 突破成功! 达到70%目标!")
        print(f"改进幅度: +{improvement*100:.1f} 个百分点")
    elif best_val_acc >= 0.65:
        print(f"🎯 显著突破! 接近70%目标!")
        print(f"改进幅度: +{improvement*100:.1f} 个百分点")
    elif best_val_acc > 0.548:
        print(f"📈 GPU训练带来改进!")
        print(f"改进幅度: +{improvement*100:.1f} 个百分点")
    
    # 保存结果
    results = {
        'gpu_optimized_accuracy': float(best_val_acc),
        'previous_best': 0.548,
        'improvement': float(improvement),
        'model_parameters': total_params,
        'batch_size': batch_size,
        'gpu_used': True,
        'mixed_precision': True
    }
    
    with open('gpu_optimized_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"💾 结果已保存到: gpu_optimized_results.json")
    
    return best_val_acc

if __name__ == "__main__":
    try:
        best_accuracy = train_gpu_optimized_model()
        
        print(f"\n🎯 GPU优化训练总结:")
        print(f"最终准确率: {best_accuracy*100:.1f}%")
        
        if best_accuracy >= 0.7:
            print("🏆 成功达到70%突破目标!")
        elif best_accuracy >= 0.65:
            print("🎯 显著改进，接近目标!")
        elif best_accuracy > 0.55:
            print("📈 GPU加速带来明显提升!")
        
    except Exception as e:
        print(f"❌ GPU训练失败: {e}")
        import traceback
        traceback.print_exc()
