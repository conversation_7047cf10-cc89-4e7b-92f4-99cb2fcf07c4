[tool.poetry]
name = "CodonTransformer"
version = "1.6.7"
description = "The ultimate tool for codon optimization, transforming protein sequences into optimized DNA sequences specific for your target organisms."
authors = ["Adibvafa Fallahpour <<EMAIL>>"]
license = "Apache-2.0"
readme = "README.md"
homepage = "https://github.com/adibvafa/CodonTransformer"
repository = "https://github.com/adibvafa/CodonTransformer"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
]

[tool.poetry.dependencies]
python = "^3.9"
biopython = "^1.83"
ipywidgets = "^7.0.0"
numpy = "<2.0.0"
onnxruntime = "^1.16.3"
pandas = "^2.0.0"
python_codon_tables = "^0.1.12"
pytorch_lightning = "^2.2.1"
scikit-learn = "^1.2.2"
scipy = "^1.13.1"
setuptools = "^70.0.0"
torch = "^2.0.0"
tqdm = "^4.66.2"
transformers = "^4.40.0"
CAI-PyPI = "^2.0.1"

[tool.poetry.dev-dependencies]
coverage = {version = "^7.0", extras = ["toml"]}

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 88
indent-width = 4
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "I"]
ignore = []

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.coverage.run]
omit = [
    # omit pytorch-generated files in /tmp
    "/tmp/*",
]
