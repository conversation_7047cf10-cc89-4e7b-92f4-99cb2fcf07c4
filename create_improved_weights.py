#!/usr/bin/env python3
"""
创建改进的密码子权重
解决权重过于极端的问题，使用更平衡的策略
"""

import json
import numpy as np
import pandas as pd
from collections import defaultdict

def analyze_codon_distribution(data_file="processed_BL21_data.csv"):
    """分析密码子分布"""
    print("分析密码子分布...")
    
    df = pd.read_csv(data_file)
    
    # 统计密码子使用频率
    codon_counts = defaultdict(int)
    aa_codon_counts = defaultdict(lambda: defaultdict(int))
    
    # 遗传密码表
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
        'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    for _, row in df.iterrows():
        dna_seq = row['nucleotide_sequence']
        
        # 提取密码子
        for i in range(0, len(dna_seq), 3):
            if i + 3 <= len(dna_seq):
                codon = dna_seq[i:i+3]
                if codon in genetic_code and genetic_code[codon] != '*':
                    aa = genetic_code[codon]
                    codon_counts[codon] += 1
                    aa_codon_counts[aa][codon] += 1
    
    return codon_counts, aa_codon_counts, genetic_code

def create_improved_weights(codon_counts, aa_codon_counts):
    """创建改进的权重策略"""
    print("创建改进的权重策略...")
    
    # 策略1: 基于氨基酸内部的相对频率
    aa_relative_weights = {}
    
    for aa, codons in aa_codon_counts.items():
        total_count = sum(codons.values())
        if total_count == 0:
            continue
            
        # 计算每个密码子在该氨基酸内的相对频率
        relative_freqs = {codon: count / total_count for codon, count in codons.items()}
        
        # 使用平方根倒数来减少极端权重
        weights = {}
        for codon, freq in relative_freqs.items():
            if freq > 0:
                # 使用平方根倒数 + 平滑
                weight = 1.0 / np.sqrt(freq + 0.01)
                weights[codon] = weight
        
        # 归一化到合理范围 [0.5, 2.0]
        if weights:
            min_weight = min(weights.values())
            max_weight = max(weights.values())

            # 避免除零错误
            if max_weight > min_weight:
                for codon in weights:
                    normalized = (weights[codon] - min_weight) / (max_weight - min_weight)
                    weights[codon] = 0.5 + 1.5 * normalized
            else:
                # 如果所有权重相同，设为1.0
                for codon in weights:
                    weights[codon] = 1.0
        
        aa_relative_weights[aa] = weights
    
    # 策略2: 全局频率平衡
    total_codons = sum(codon_counts.values())
    global_weights = {}
    
    for codon, count in codon_counts.items():
        if count > 0:
            freq = count / total_codons
            # 使用对数平滑
            weight = -np.log(freq + 1e-6)
            global_weights[codon] = weight
    
    # 归一化全局权重
    if global_weights:
        min_weight = min(global_weights.values())
        max_weight = max(global_weights.values())

        if max_weight > min_weight:
            for codon in global_weights:
                normalized = (global_weights[codon] - min_weight) / (max_weight - min_weight)
                global_weights[codon] = 0.5 + 1.5 * normalized
        else:
            for codon in global_weights:
                global_weights[codon] = 1.0
    
    # 策略3: 混合权重（相对权重 * 0.7 + 全局权重 * 0.3）
    mixed_weights = {}
    
    for aa, codons in aa_relative_weights.items():
        for codon, rel_weight in codons.items():
            global_weight = global_weights.get(codon, 1.0)
            mixed_weights[codon] = 0.7 * rel_weight + 0.3 * global_weight
    
    return {
        'relative_weights': aa_relative_weights,
        'global_weights': global_weights,
        'mixed_weights': mixed_weights
    }

def create_class_balanced_weights(codon_counts):
    """使用sklearn风格的类平衡权重"""
    print("创建类平衡权重...")
    
    # 计算每个密码子的权重
    total_samples = sum(codon_counts.values())
    n_classes = len(codon_counts)
    
    balanced_weights = {}
    for codon, count in codon_counts.items():
        if count > 0:
            # sklearn的balanced权重公式
            weight = total_samples / (n_classes * count)
            balanced_weights[codon] = weight
    
    # 限制权重范围，避免极端值
    if balanced_weights:
        weights_array = np.array(list(balanced_weights.values()))
        median_weight = np.median(weights_array)
        
        # 限制权重在中位数的0.2-5倍之间
        for codon in balanced_weights:
            weight = balanced_weights[codon]
            weight = max(weight, median_weight * 0.2)
            weight = min(weight, median_weight * 5.0)
            balanced_weights[codon] = weight
    
    return balanced_weights

def main():
    """主函数"""
    print("创建改进的密码子权重文件...")
    
    # 分析数据
    codon_counts, aa_codon_counts, genetic_code = analyze_codon_distribution()
    
    print(f"发现 {len(codon_counts)} 种密码子")
    print(f"总密码子数: {sum(codon_counts.values())}")
    
    # 创建不同的权重策略
    improved_weights = create_improved_weights(codon_counts, aa_codon_counts)
    balanced_weights = create_class_balanced_weights(codon_counts)
    
    # 氨基酸到密码子的映射
    aa_to_codons = defaultdict(list)
    for codon, aa in genetic_code.items():
        if aa != '*':
            aa_to_codons[aa].append(codon)
    
    # 氨基酸权重（基于密码子数量的倒数）
    aa_weights = {}
    for aa, codons in aa_to_codons.items():
        aa_weights[aa] = 1.0 / len(codons)
    
    # 创建完整的权重信息
    weight_info = {
        'codon_counts': dict(codon_counts),
        'aa_codon_counts': {aa: dict(codons) for aa, codons in aa_codon_counts.items()},
        'aa_to_codons': dict(aa_to_codons),
        'aa_weights': aa_weights,
        
        # 不同的权重策略
        'original_weights': improved_weights['mixed_weights'],  # 默认使用混合权重
        'relative_weights': improved_weights['relative_weights'],
        'global_weights': improved_weights['global_weights'],
        'mixed_weights': improved_weights['mixed_weights'],
        'balanced_weights': balanced_weights,
        
        # 为了兼容性，保留原始接口
        'codon_weights': improved_weights['mixed_weights']
    }
    
    # 保存到文件
    with open('improved_codon_weights.json', 'w') as f:
        json.dump(weight_info, f, indent=2)
    
    print("改进的密码子权重文件已创建: improved_codon_weights.json")
    
    # 显示权重统计
    for strategy_name, weights in [
        ('混合权重', improved_weights['mixed_weights']),
        ('平衡权重', balanced_weights)
    ]:
        if weights:
            weights_array = np.array(list(weights.values()))
            print(f"\n{strategy_name}统计:")
            print(f"  范围: {weights_array.min():.3f} - {weights_array.max():.3f}")
            print(f"  均值: {weights_array.mean():.3f}")
            print(f"  标准差: {weights_array.std():.3f}")
            
            # 显示最高和最低权重的密码子
            sorted_weights = sorted(weights.items(), key=lambda x: x[1])
            print(f"  最低权重: {sorted_weights[0][0]}={sorted_weights[0][1]:.3f}")
            print(f"  最高权重: {sorted_weights[-1][0]}={sorted_weights[-1][1]:.3f}")

if __name__ == "__main__":
    main()
