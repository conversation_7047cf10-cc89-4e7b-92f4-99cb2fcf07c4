#!/usr/bin/env python3
"""
训练监控和恢复工具
用于查看训练进度、恢复训练状态等
"""

import torch
import json
import os
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime

def check_training_status():
    """检查训练状态"""
    checkpoint_path = 'ultra_training_checkpoint.pth'
    best_path = 'ultra_training_best.pth'
    
    print("🔍 训练状态检查")
    print("=" * 50)
    
    # 检查检查点文件
    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        print(f"📂 检查点文件: {checkpoint_path}")
        print(f"   最后轮次: {checkpoint['epoch'] + 1}")
        print(f"   最佳准确度: {checkpoint['best_accuracy']*100:.2f}%")
        print(f"   耐心计数: {checkpoint['patience_counter']}")
        print(f"   保存时间: {checkpoint.get('timestamp', '未知')}")
        
        # 显示训练历史
        train_acc = checkpoint.get('train_accuracies', [])
        val_acc = checkpoint.get('val_accuracies', [])
        
        if train_acc and val_acc:
            print(f"   训练轮次: {len(train_acc)}")
            print(f"   最新训练准确度: {train_acc[-1]*100:.2f}%")
            print(f"   最新验证准确度: {val_acc[-1]*100:.2f}%")
            
            # 显示里程碑
            milestones = checkpoint.get('achieved_milestones', [])
            if milestones:
                print(f"   已达成里程碑: {sorted([int(m*100) for m in milestones])}%")
        
        print()
    else:
        print(f"❌ 未找到检查点文件: {checkpoint_path}")
        print()
    
    # 检查最佳模型文件
    if os.path.exists(best_path):
        best_checkpoint = torch.load(best_path, map_location='cpu')
        
        print(f"🏆 最佳模型文件: {best_path}")
        print(f"   最佳轮次: {best_checkpoint['epoch'] + 1}")
        print(f"   最佳准确度: {best_checkpoint['best_accuracy']*100:.2f}%")
        print(f"   保存时间: {best_checkpoint.get('timestamp', '未知')}")
        print()
    else:
        print(f"❌ 未找到最佳模型文件: {best_path}")
        print()

def plot_training_progress():
    """绘制训练进度图"""
    checkpoint_path = 'ultra_training_checkpoint.pth'
    
    if not os.path.exists(checkpoint_path):
        print("❌ 未找到检查点文件，无法绘制进度图")
        return
    
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    train_losses = checkpoint.get('train_losses', [])
    val_losses = checkpoint.get('val_losses', [])
    train_accuracies = checkpoint.get('train_accuracies', [])
    val_accuracies = checkpoint.get('val_accuracies', [])
    
    if not train_accuracies:
        print("❌ 检查点中没有训练历史数据")
        return
    
    epochs = list(range(1, len(train_accuracies) + 1))
    
    plt.figure(figsize=(15, 5))
    
    # 损失图
    plt.subplot(1, 3, 1)
    if train_losses and val_losses:
        plt.plot(epochs, train_losses, label='Train Loss', alpha=0.7)
        plt.plot(epochs, val_losses, label='Val Loss', alpha=0.7)
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training and Validation Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
    
    # 准确度图
    plt.subplot(1, 3, 2)
    plt.plot(epochs, [acc*100 for acc in train_accuracies], label='Train Accuracy', alpha=0.7)
    plt.plot(epochs, [acc*100 for acc in val_accuracies], label='Val Accuracy', alpha=0.7)
    plt.axhline(y=80, color='r', linestyle='--', alpha=0.8, label='Target 80%')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.title('Training and Validation Accuracy')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 验证准确度详细图
    plt.subplot(1, 3, 3)
    plt.plot(epochs, [acc*100 for acc in val_accuracies], 'b-', linewidth=2, label='Val Accuracy')
    plt.axhline(y=80, color='r', linestyle='--', linewidth=2, label='Target 80%')
    
    # 标记里程碑
    milestones = checkpoint.get('achieved_milestones', [])
    for milestone in milestones:
        plt.axhline(y=milestone*100, color='g', linestyle=':', alpha=0.6)
    
    best_acc = checkpoint.get('best_accuracy', 0)
    plt.axhline(y=best_acc*100, color='orange', linestyle='-', alpha=0.8, label=f'Best {best_acc*100:.1f}%')
    
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.title('Validation Accuracy Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('training_progress.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 训练进度图已保存: training_progress.png")

def export_training_data():
    """导出训练数据到CSV"""
    checkpoint_path = 'ultra_training_checkpoint.pth'
    
    if not os.path.exists(checkpoint_path):
        print("❌ 未找到检查点文件")
        return
    
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    train_losses = checkpoint.get('train_losses', [])
    val_losses = checkpoint.get('val_losses', [])
    train_accuracies = checkpoint.get('train_accuracies', [])
    val_accuracies = checkpoint.get('val_accuracies', [])
    
    if not train_accuracies:
        print("❌ 检查点中没有训练历史数据")
        return
    
    # 创建DataFrame
    data = {
        'epoch': list(range(1, len(train_accuracies) + 1)),
        'train_loss': train_losses if len(train_losses) == len(train_accuracies) else [None] * len(train_accuracies),
        'val_loss': val_losses if len(val_losses) == len(train_accuracies) else [None] * len(train_accuracies),
        'train_accuracy': train_accuracies,
        'val_accuracy': val_accuracies
    }
    
    df = pd.DataFrame(data)
    
    # 保存到CSV
    csv_path = 'training_history.csv'
    df.to_csv(csv_path, index=False)
    
    print(f"📄 训练数据已导出: {csv_path}")
    print(f"   总轮次: {len(df)}")
    print(f"   最佳验证准确度: {df['val_accuracy'].max()*100:.2f}%")

def clean_checkpoints():
    """清理检查点文件"""
    files_to_check = [
        'ultra_training_checkpoint.pth',
        'ultra_training_best.pth',
        'training_progress.png',
        'training_history.csv'
    ]
    
    print("🧹 检查点文件清理")
    print("=" * 30)
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"📁 发现文件: {file_path}")
            response = input(f"   是否删除? (y/N): ").strip().lower()
            if response == 'y':
                os.remove(file_path)
                print(f"   ✅ 已删除: {file_path}")
            else:
                print(f"   ⏭️  跳过: {file_path}")
        else:
            print(f"❌ 未找到: {file_path}")

def resume_training_info():
    """显示恢复训练的信息"""
    print("🔄 恢复训练指南")
    print("=" * 30)
    print("1. 直接运行训练脚本:")
    print("   python ultra_large_batch_training.py")
    print()
    print("2. 脚本会自动检测并加载检查点文件")
    print("3. 训练将从上次中断的地方继续")
    print()
    print("📝 注意事项:")
    print("- 确保数据文件 processed_BL21_data.csv 存在")
    print("- 确保权重文件 pure_codon_weights.json 存在")
    print("- 检查GPU内存是否充足")

def main():
    """主菜单"""
    while True:
        print("\n🎛️  训练监控工具")
        print("=" * 40)
        print("1. 检查训练状态")
        print("2. 绘制训练进度图")
        print("3. 导出训练数据")
        print("4. 恢复训练指南")
        print("5. 清理检查点文件")
        print("0. 退出")
        print()
        
        choice = input("请选择操作 (0-5): ").strip()
        
        if choice == '1':
            check_training_status()
        elif choice == '2':
            plot_training_progress()
        elif choice == '3':
            export_training_data()
        elif choice == '4':
            resume_training_info()
        elif choice == '5':
            clean_checkpoints()
        elif choice == '0':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
