#!/usr/bin/env python3
"""
改进的密码子偏好性预测模型训练脚本
解决Loss=NaN问题，增加准确度监控和模型保存
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
import os
warnings.filterwarnings('ignore')

from codon_preference_model import CodonPreferenceModel

class ImprovedCodonDataset(Dataset):
    """改进的密码子数据集类"""
    
    def __init__(self, protein_sequences, nucleotide_sequences, codon_to_idx):
        self.protein_sequences = protein_sequences
        self.nucleotide_sequences = nucleotide_sequences
        self.codon_to_idx = codon_to_idx
        
        # 预处理数据
        self.data = []
        for prot_seq, dna_seq in zip(protein_sequences, nucleotide_sequences):
            # 移除终止密码子（如果存在）
            stop_codons = ['TAA', 'TAG', 'TGA']
            cleaned_dna = dna_seq
            
            if len(dna_seq) >= 3:
                last_codon = dna_seq[-3:]
                if last_codon in stop_codons:
                    cleaned_dna = dna_seq[:-3]
            
            # 确保序列长度匹配
            if len(prot_seq) * 3 == len(cleaned_dna):
                # 提取密码子
                codons = [cleaned_dna[i:i+3] for i in range(0, len(cleaned_dna), 3)]
                codon_indices = []
                
                valid = True
                for codon in codons:
                    if codon in codon_to_idx:
                        codon_indices.append(codon_to_idx[codon])
                    else:
                        valid = False
                        break
                
                if valid and len(codon_indices) > 0:
                    self.data.append({
                        'protein': prot_seq,
                        'codons': codon_indices
                    })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx]

class RobustCodonLoss(nn.Module):
    """数值稳定的密码子预测损失函数"""
    
    def __init__(self, label_smoothing=0.1, ignore_index=-100):
        super().__init__()
        self.label_smoothing = label_smoothing
        self.ignore_index = ignore_index
        
    def forward(self, predicted_logits, true_codons, valid_mask=None):
        # 输入验证
        if torch.isnan(predicted_logits).any():
            print("错误: 预测logits包含NaN")
            return torch.tensor(0.0, device=predicted_logits.device, requires_grad=True)
            
        if torch.isinf(predicted_logits).any():
            print("警告: 预测logits包含Inf，进行裁剪")
            predicted_logits = torch.clamp(predicted_logits, min=-1e9, max=1e9)
        
        # 重新整形
        logits_flat = predicted_logits.reshape(-1, predicted_logits.size(-1))
        targets_flat = true_codons.reshape(-1)
        
        # 计算损失
        try:
            loss = F.cross_entropy(
                logits_flat,
                targets_flat,
                label_smoothing=self.label_smoothing,
                ignore_index=self.ignore_index,
                reduction='mean'
            )
            
            # 最终检查
            if torch.isnan(loss):
                print("错误: 计算出的损失为NaN")
                return torch.tensor(0.0, device=predicted_logits.device, requires_grad=True)
                
            return loss
            
        except Exception as e:
            print(f"损失计算异常: {e}")
            return torch.tensor(0.0, device=predicted_logits.device, requires_grad=True)

def calculate_accuracy(predicted_logits, true_codons, attention_mask=None):
    """计算预测准确度"""
    with torch.no_grad():
        # 获取预测的密码子
        predicted_codons = torch.argmax(predicted_logits, dim=-1)
        
        # 计算准确度
        correct = (predicted_codons == true_codons).float()
        
        if attention_mask is not None:
            correct = correct * attention_mask
            accuracy = correct.sum() / attention_mask.sum()
        else:
            accuracy = correct.mean()
        
        return accuracy.item()

def collate_fn(batch):
    """自定义批处理函数"""
    proteins = [item['protein'] for item in batch]
    codons = [item['codons'] for item in batch]
    
    # 找到最大长度
    max_len = max(len(seq) for seq in codons)
    
    # 填充序列
    padded_codons = []
    attention_masks = []
    
    for codon_seq in codons:
        padded = codon_seq + [-100] * (max_len - len(codon_seq))  # 使用-100作为ignore_index
        mask = [1] * len(codon_seq) + [0] * (max_len - len(codon_seq))
        
        padded_codons.append(padded)
        attention_masks.append(mask)
    
    return {
        'proteins': proteins,
        'codons': torch.tensor(padded_codons, dtype=torch.long),
        'attention_mask': torch.tensor(attention_masks, dtype=torch.float)
    }

def load_data(data_file="processed_BL21_data.csv"):
    """加载预处理后的数据"""
    print(f"正在加载数据: {data_file}")
    
    df = pd.read_csv(data_file)
    print(f"加载了 {len(df)} 条数据")
    
    # 使用ESM2的最大长度限制
    max_length = 1024
    df = df[df['protein_length'] <= max_length]
    print(f"过滤后剩余 {len(df)} 条数据（长度 <= {max_length}）")
    
    return df['protein_sequence'].tolist(), df['nucleotide_sequence'].tolist()

def save_checkpoint(model, optimizer, epoch, train_loss, val_loss, accuracy, filename):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'train_loss': train_loss,
        'val_loss': val_loss,
        'accuracy': accuracy,
        'model_config': {
            'codon_to_idx': model.codon_to_idx,
            'aa_to_codons': model.aa_to_codons,
            'genetic_code': model.genetic_code
        }
    }
    torch.save(checkpoint, filename)
    print(f"保存检查点: {filename}")

def load_checkpoint(model, optimizer, filename):
    """加载检查点"""
    if os.path.exists(filename):
        checkpoint = torch.load(filename)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        print(f"从检查点恢复训练: epoch {start_epoch}")
        return start_epoch
    return 0

def train_model_robust(model, train_loader, val_loader, num_epochs=50, learning_rate=1e-5, target_accuracy=0.99):
    """稳健的模型训练"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    model = model.to(device)
    
    # 修复约束函数中的数值不稳定问题
    def apply_codon_constraints_fixed(self, logits, amino_acid_sequence):
        """修复后的约束函数，避免数值不稳定"""
        batch_size, seq_len, vocab_size = logits.shape
        constrained_logits = logits.clone()
        
        # 使用有限的大负数而不是-inf
        LARGE_NEGATIVE = -1e9
        
        for b in range(batch_size):
            protein_seq = amino_acid_sequence[b]
            # 跳过<cls>token，从位置1开始
            for pos in range(min(len(protein_seq), seq_len - 2)):
                if pos + 1 < seq_len:
                    aa = protein_seq[pos]
                    
                    if aa in self.aa_to_codons:
                        valid_codons = self.aa_to_codons[aa]
                        
                        # 创建mask
                        mask = torch.zeros(vocab_size, dtype=torch.bool, device=logits.device)
                        for codon in valid_codons:
                            if codon in self.codon_to_idx:
                                codon_idx = self.codon_to_idx[codon]
                                mask[codon_idx] = True
                        
                        # 屏蔽无效密码子，使用有限负数
                        constrained_logits[b, pos + 1, ~mask] = LARGE_NEGATIVE
        
        return constrained_logits
    
    # 替换模型的约束函数
    model.apply_codon_constraints = apply_codon_constraints_fixed.__get__(model, model.__class__)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01, eps=1e-8)
    criterion = RobustCodonLoss(label_smoothing=0.1, ignore_index=-100)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    
    # 尝试加载检查点
    start_epoch = load_checkpoint(model, optimizer, 'latest_checkpoint.pth')
    
    # 训练历史
    train_losses = []
    val_losses = []
    accuracies = []
    
    best_accuracy = 0.0
    patience = 10
    patience_counter = 0
    
    for epoch in range(start_epoch, num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_accuracy = 0.0
        nan_count = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for batch_idx, batch in enumerate(train_pbar):
            try:
                proteins = batch['proteins']
                true_codons = batch['codons'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                
                optimizer.zero_grad()
                
                # 前向传播
                codon_probs, predicted_logits, _ = model(proteins)
                
                # 调整维度以匹配真实标签
                if predicted_logits.shape[1] > true_codons.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + true_codons.shape[1]
                    predicted_logits = predicted_logits[:, start_idx:end_idx, :]
                
                # 计算损失
                loss = criterion(predicted_logits, true_codons, attention_mask)
                
                # NaN检查
                if torch.isnan(loss):
                    nan_count += 1
                    print(f"批次 {batch_idx}: 检测到NaN损失，跳过")
                    continue
                
                # 计算准确度
                accuracy = calculate_accuracy(predicted_logits, true_codons, attention_mask)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                if torch.isnan(grad_norm):
                    print(f"批次 {batch_idx}: 检测到NaN梯度，跳过")
                    continue
                
                optimizer.step()
                
                train_loss += loss.item()
                train_accuracy += accuracy
                
                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{accuracy:.4f}',
                    'GradNorm': f'{grad_norm:.4f}'
                })
                
            except Exception as e:
                print(f"批次 {batch_idx} 训练异常: {e}")
                continue
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_accuracy = 0.0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for batch in val_pbar:
                proteins = batch['proteins']
                true_codons = batch['codons'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                
                # 前向传播
                codon_probs, predicted_logits, _ = model(proteins)
                
                # 调整维度
                if predicted_logits.shape[1] > true_codons.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + true_codons.shape[1]
                    predicted_logits = predicted_logits[:, start_idx:end_idx, :]
                
                # 计算损失和准确度
                loss = criterion(predicted_logits, true_codons, attention_mask)
                accuracy = calculate_accuracy(predicted_logits, true_codons, attention_mask)
                
                val_loss += loss.item()
                val_accuracy += accuracy
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{accuracy:.4f}'
                })
        
        # 计算平均值
        avg_train_loss = train_loss / (len(train_loader) - nan_count) if len(train_loader) > nan_count else 0
        avg_val_loss = val_loss / len(val_loader)
        avg_train_accuracy = train_accuracy / (len(train_loader) - nan_count) if len(train_loader) > nan_count else 0
        avg_val_accuracy = val_accuracy / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        accuracies.append(avg_val_accuracy)
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}, Train Acc: {avg_train_accuracy:.4f}')
        print(f'  Val Loss: {avg_val_loss:.4f}, Val Acc: {avg_val_accuracy:.4f}')
        print(f'  NaN批次数: {nan_count}')
        
        # 每轮都保存检查点
        save_checkpoint(model, optimizer, epoch, avg_train_loss, avg_val_loss, avg_val_accuracy, 'latest_checkpoint.pth')
        
        # 保存最佳模型
        if avg_val_accuracy > best_accuracy:
            best_accuracy = avg_val_accuracy
            patience_counter = 0
            save_checkpoint(model, optimizer, epoch, avg_train_loss, avg_val_loss, avg_val_accuracy, 'best_codon_model.pth')
            print(f'  保存最佳模型 (Val Acc: {best_accuracy:.4f})')
        else:
            patience_counter += 1
            print(f'  验证准确度未改善 ({patience_counter}/{patience})')
        
        # 检查是否达到目标准确度
        if avg_val_accuracy >= target_accuracy:
            print(f'  达到目标准确度 {target_accuracy:.2f}，停止训练')
            break
        
        # 早停检查
        if patience_counter >= patience:
            print(f'  早停：验证准确度连续{patience}轮未改善')
            break
        
        scheduler.step()
        print(f'  Learning Rate: {scheduler.get_last_lr()[0]:.6f}')
        print()
    
    return train_losses, val_losses, accuracies

def main():
    """主训练函数"""
    print("开始训练改进的密码子偏好性预测模型")
    
    # 加载数据
    proteins, nucleotides = load_data()
    
    # 创建模型
    print("创建模型...")
    model = CodonPreferenceModel()
    
    # 创建数据集
    print("创建数据集...")
    dataset = ImprovedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    print(f"有效数据集大小: {len(dataset)}")
    
    # 划分训练集和验证集
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 创建数据加载器
    batch_size = 4  # 使用较小的批次大小确保稳定性
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)
    
    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    
    # 训练模型
    train_losses, val_losses, accuracies = train_model_robust(
        model, train_loader, val_loader, 
        num_epochs=100,  # 增加最大训练轮数
        learning_rate=1e-5,  # 使用较小的学习率
        target_accuracy=0.99  # 目标准确度
    )
    
    # 绘制训练曲线
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 3, 2)
    plt.plot(accuracies, label='Validation Accuracy')
    plt.axhline(y=0.99, color='r', linestyle='--', label='Target Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.title('Validation Accuracy')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 3, 3)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.plot([acc * max(max(train_losses), max(val_losses)) for acc in accuracies], label='Val Acc (scaled)')
    plt.xlabel('Epoch')
    plt.ylabel('Value')
    plt.title('Training Overview')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('improved_training_curve.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("训练完成！")
    print(f"最佳验证准确度: {max(accuracies):.4f}")

if __name__ == "__main__":
    main()
