#!/usr/bin/env python3
"""
增强特征训练脚本
通过数据增强、特征工程、多任务学习提升准确度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import random
warnings.filterwarnings('ignore')

from fixed_training import calculate_accuracy
from improved_training import collate_fn

class EnhancedCodonDataset(Dataset):
    """增强的密码子数据集，包含数据增强"""
    
    def __init__(self, proteins, nucleotides, codon_to_idx, augment_prob=0.3):
        self.proteins = proteins
        self.nucleotides = nucleotides
        self.codon_to_idx = codon_to_idx
        self.augment_prob = augment_prob
        
        # 构建氨基酸到密码子的映射
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        # 构建氨基酸到密码子映射
        self.aa_to_codons = {}
        for codon, aa in self.genetic_code.items():
            if aa != '*':
                if aa not in self.aa_to_codons:
                    self.aa_to_codons[aa] = []
                self.aa_to_codons[aa].append(codon)
        
        self.valid_pairs = []
        self._process_data()
    
    def _process_data(self):
        """处理数据"""
        for protein, nucleotide in zip(self.proteins, self.nucleotides):
            if len(protein) * 3 == len(nucleotide):
                # 提取密码子
                codons = []
                for i in range(0, len(nucleotide), 3):
                    codon = nucleotide[i:i+3]
                    if codon in self.codon_to_idx:
                        codons.append(self.codon_to_idx[codon])
                    else:
                        break
                
                if len(codons) == len(protein):
                    self.valid_pairs.append((protein, codons))
    
    def augment_sequence(self, protein, codons):
        """数据增强：同义密码子替换"""
        if random.random() > self.augment_prob:
            return protein, codons
        
        new_codons = codons.copy()
        
        # 随机替换一些密码子
        for i, aa in enumerate(protein):
            if aa in self.aa_to_codons and len(self.aa_to_codons[aa]) > 1:
                if random.random() < 0.2:  # 20%概率替换
                    # 随机选择同义密码子
                    possible_codons = self.aa_to_codons[aa]
                    new_codon = random.choice(possible_codons)
                    if new_codon in self.codon_to_idx:
                        new_codons[i] = self.codon_to_idx[new_codon]
        
        return protein, new_codons
    
    def __len__(self):
        return len(self.valid_pairs)
    
    def __getitem__(self, idx):
        protein, codons = self.valid_pairs[idx]
        
        # 应用数据增强
        protein, codons = self.augment_sequence(protein, codons)
        
        return {
            'protein': protein,
            'codons': torch.tensor(codons, dtype=torch.long)
        }

class MultiTaskCodonModel(nn.Module):
    """多任务密码子预测模型"""
    
    def __init__(self, esm_model_path="facebook/esm2_t33_650M_UR50D"):
        super().__init__()
        
        # 加载ESM2
        from transformers import EsmModel, EsmTokenizer
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            self.tokenizer = EsmTokenizer.from_pretrained(local_path)
            self.esm2 = EsmModel.from_pretrained(local_path)
        except:
            self.tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
            self.esm2 = EsmModel.from_pretrained(esm_model_path)
        
        # 冻结ESM2
        for param in self.esm2.parameters():
            param.requires_grad = False
            
        self.hidden_size = self.esm2.config.hidden_size
        
        # 构建密码子词汇表
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        self.codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
        
        # 特征提取层
        self.feature_extractor = nn.Sequential(
            nn.LayerNorm(self.hidden_size),
            nn.Dropout(0.2),
            nn.Linear(self.hidden_size, 512),
            nn.LayerNorm(512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 主任务：密码子预测
        self.codon_predictor = nn.Sequential(
            nn.Linear(512, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, len(self.codon_to_idx))
        )
        
        # 辅助任务1：氨基酸预测（帮助学习蛋白质特征）
        self.aa_predictor = nn.Sequential(
            nn.Linear(512, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 20)  # 20种氨基酸
        )
        
        # 辅助任务2：密码子使用偏好预测
        self.usage_predictor = nn.Sequential(
            nn.Linear(512, 64),
            nn.ReLU(),
            nn.Linear(64, 1)  # 使用频率
        )
        
        self._init_weights()
    
    def _init_weights(self):
        """权重初始化"""
        for module in [self.feature_extractor, self.codon_predictor, self.aa_predictor, self.usage_predictor]:
            for layer in module:
                if isinstance(layer, nn.Linear):
                    nn.init.xavier_uniform_(layer.weight, gain=0.1)
                    nn.init.zeros_(layer.bias)
    
    def forward(self, protein_sequences):
        """前向传播"""
        # ESM2编码
        encoded = self.tokenizer(
            protein_sequences,
            padding=True,
            truncation=True,
            max_length=1024,
            return_tensors="pt"
        )
        
        input_ids = encoded['input_ids'].to(next(self.parameters()).device)
        attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
        
        # ESM2特征提取
        with torch.no_grad():
            esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
        
        # 特征提取
        features = self.feature_extractor(esm_output.last_hidden_state)
        
        # 多任务预测
        codon_logits = self.codon_predictor(features)
        aa_logits = self.aa_predictor(features)
        usage_logits = self.usage_predictor(features)
        
        return {
            'codon_logits': codon_logits,
            'aa_logits': aa_logits,
            'usage_logits': usage_logits
        }

def enhanced_collate_fn(batch):
    """增强的批处理函数"""
    proteins = [item['protein'] for item in batch]
    codons = [item['codons'] for item in batch]
    
    # 填充密码子序列
    max_len = max(len(codon_seq) for codon_seq in codons)
    padded_codons = []
    
    for codon_seq in codons:
        padded = torch.full((max_len,), -100, dtype=torch.long)
        padded[:len(codon_seq)] = codon_seq
        padded_codons.append(padded)
    
    return {
        'proteins': proteins,
        'codons': torch.stack(padded_codons)
    }

def train_enhanced_model():
    """训练增强模型"""
    print("🚀 开始增强特征训练")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # 创建模型
    model = MultiTaskCodonModel()
    model = model.to(device)
    
    # 创建增强数据集
    dataset = EnhancedCodonDataset(proteins, nucleotides, model.codon_to_idx, augment_prob=0.4)
    
    # 数据划分
    train_size = int(0.75 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=6, shuffle=True, collate_fn=enhanced_collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=6, shuffle=False, collate_fn=enhanced_collate_fn)
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=1e-5, weight_decay=0.05)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=20, T_mult=2)
    
    # 训练历史
    best_val_acc = 0.0
    
    print("开始多任务训练...")
    
    for epoch in range(200):
        # 训练
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc=f'Epoch {epoch+1}/200', leave=False):
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                outputs = model(proteins)
                codon_logits = outputs['codon_logits']
                
                # 对齐维度
                if codon_logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    codon_logits = codon_logits[:, start_idx:end_idx, :]
                
                # 主任务损失
                main_loss = F.cross_entropy(
                    codon_logits.reshape(-1, codon_logits.size(-1)),
                    targets.reshape(-1),
                    ignore_index=-100
                )
                
                # 总损失
                total_loss = main_loss
                
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += total_loss.item()
                train_acc += calculate_accuracy(codon_logits, targets)
                train_batches += 1
                
            except Exception as e:
                continue
        
        # 验证
        model.eval()
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    outputs = model(proteins)
                    codon_logits = outputs['codon_logits']
                    
                    if codon_logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        codon_logits = codon_logits[:, start_idx:end_idx, :]
                    
                    val_acc += calculate_accuracy(codon_logits, targets)
                    val_batches += 1
                    
                except Exception as e:
                    continue
        
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        
        scheduler.step()
        
        print(f'Epoch {epoch+1}: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%')
        
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            torch.save(model.state_dict(), 'enhanced_best_model.pth')
            
            if avg_val_acc >= 0.8:
                print("🎉 达到80%目标！")
                break
    
    print(f"最佳验证准确度: {best_val_acc*100:.1f}%")
    return best_val_acc

if __name__ == "__main__":
    train_enhanced_model()
