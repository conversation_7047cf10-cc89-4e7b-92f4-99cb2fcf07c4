#!/usr/bin/env python3
"""
Optimized Model Architecture
Advanced architectural improvements based on analysis results
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import gc
import os
import time
import math
warnings.filterwarnings('ignore')

from fixed_data_processor import FixedCodonDataset, fixed_collate_fn

class PositionalEncoding(nn.Module):
    """Positional encoding for sequence modeling"""
    
    def __init__(self, d_model, max_len=1024):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class MultiHeadSelfAttention(nn.Module):
    """Optimized multi-head self-attention"""
    
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model, bias=False)
        self.w_k = nn.Linear(d_model, d_model, bias=False)
        self.w_v = nn.Linear(d_model, d_model, bias=False)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x, mask=None):
        batch_size, seq_len, d_model = x.size()
        
        # Residual connection
        residual = x
        
        # Multi-head attention
        Q = self.w_q(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(x).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        # Scaled dot-product attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        context = torch.matmul(attn_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)
        
        # Output projection
        output = self.w_o(context)
        output = self.dropout(output)
        
        # Residual connection and layer norm
        return self.layer_norm(residual + output)

class FeedForward(nn.Module):
    """Position-wise feed-forward network"""
    
    def __init__(self, d_model, d_ff, dropout=0.1):
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        residual = x
        x = F.gelu(self.linear1(x))
        x = self.dropout(x)
        x = self.linear2(x)
        x = self.dropout(x)
        return self.layer_norm(residual + x)

class TransformerBlock(nn.Module):
    """Transformer block with self-attention and feed-forward"""
    
    def __init__(self, d_model, num_heads, d_ff, dropout=0.1):
        super().__init__()
        self.attention = MultiHeadSelfAttention(d_model, num_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        
    def forward(self, x, mask=None):
        x = self.attention(x, mask)
        x = self.feed_forward(x)
        return x

class OptimizedCodonModel(nn.Module):
    """Optimized codon prediction model with advanced architecture"""
    
    def __init__(self, esm_model_path="facebook/esm2_t33_650M_UR50D"):
        super().__init__()
        
        # Load ESM2 model
        from transformers import EsmModel, EsmTokenizer
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            self.tokenizer = EsmTokenizer.from_pretrained(local_path)
            self.esm2 = EsmModel.from_pretrained(local_path)
        except:
            self.tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
            self.esm2 = EsmModel.from_pretrained(esm_model_path)
        
        # Freeze ESM2 parameters
        for param in self.esm2.parameters():
            param.requires_grad = False
            
        self.hidden_size = self.esm2.config.hidden_size
        
        # Build genetic code and codon vocabulary
        self.genetic_code = self._build_genetic_code()
        self.codon_to_idx = self._build_codon_vocab()
        
        # Feature projection and normalization
        self.feature_projection = nn.Linear(self.hidden_size, 512)
        self.feature_norm = nn.LayerNorm(512)
        self.feature_dropout = nn.Dropout(0.1)
        
        # Positional encoding
        self.pos_encoding = PositionalEncoding(512)
        
        # Multi-scale convolutional features
        self.conv_layers = nn.ModuleList([
            nn.Conv1d(512, 128, kernel_size=k, padding=k//2) 
            for k in [3, 5, 7, 9]
        ])
        self.conv_norms = nn.ModuleList([nn.BatchNorm1d(128) for _ in range(4)])
        
        # Transformer layers for sequence modeling
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(512, num_heads=8, d_ff=1024, dropout=0.1)
            for _ in range(3)
        ])
        
        # Multi-scale feature fusion
        self.feature_fusion = nn.Sequential(
            nn.Linear(512 + 128*4, 512),  # ESM2 + conv features
            nn.LayerNorm(512),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # Codon-specific attention
        self.codon_attention = nn.MultiheadAttention(512, num_heads=8, dropout=0.1, batch_first=True)
        
        # Final prediction layers with residual connections
        self.predictor = nn.ModuleList([
            nn.Sequential(
                nn.Linear(512, 384),
                nn.LayerNorm(384),
                nn.ReLU(),
                nn.Dropout(0.2)
            ),
            nn.Sequential(
                nn.Linear(384, 256),
                nn.LayerNorm(256),
                nn.ReLU(),
                nn.Dropout(0.1)
            ),
            nn.Linear(256, len(self.codon_to_idx))
        ])
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        """Xavier initialization with small gains"""
        for module in [self.feature_projection, self.conv_layers, self.feature_fusion, self.predictor]:
            if isinstance(module, (nn.ModuleList, nn.Sequential)):
                for layer in module:
                    if isinstance(layer, (nn.Linear, nn.Conv1d)):
                        nn.init.xavier_uniform_(layer.weight, gain=0.1)
                        if hasattr(layer, 'bias') and layer.bias is not None:
                            nn.init.zeros_(layer.bias)
            elif isinstance(module, (nn.Linear, nn.Conv1d)):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if hasattr(module, 'bias') and module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def _build_genetic_code(self):
        return {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def _build_codon_vocab(self):
        codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        return {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    def forward(self, protein_sequences):
        """Optimized forward pass with advanced architecture"""
        # ESM2 encoding
        encoded = self.tokenizer(
            protein_sequences,
            padding=True,
            truncation=True,
            max_length=1024,
            return_tensors="pt"
        )
        
        input_ids = encoded['input_ids'].to(next(self.parameters()).device)
        attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
        
        # ESM2 feature extraction
        with torch.no_grad():
            esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
        
        # Feature projection and normalization
        features = self.feature_projection(esm_output.last_hidden_state)
        features = self.feature_norm(features)
        features = self.feature_dropout(features)
        
        # Add positional encoding
        features = self.pos_encoding(features.transpose(0, 1)).transpose(0, 1)
        
        # Multi-scale convolutional features
        conv_features = []
        features_t = features.transpose(1, 2)  # [batch, hidden, seq]
        
        for conv, norm in zip(self.conv_layers, self.conv_norms):
            conv_out = F.relu(norm(conv(features_t)))
            conv_features.append(conv_out)
        
        # Concatenate conv features
        conv_combined = torch.cat(conv_features, dim=1)  # [batch, 128*4, seq]
        conv_combined = conv_combined.transpose(1, 2)  # [batch, seq, 128*4]
        
        # Combine ESM2 and conv features
        combined_features = torch.cat([features, conv_combined], dim=-1)
        fused_features = self.feature_fusion(combined_features)
        
        # Transformer layers for sequence modeling
        transformer_out = fused_features
        for transformer in self.transformer_layers:
            transformer_out = transformer(transformer_out, mask=attention_mask.unsqueeze(1).unsqueeze(2))
        
        # Codon-specific attention
        attended_features, _ = self.codon_attention(
            transformer_out, transformer_out, transformer_out,
            key_padding_mask=~attention_mask.bool()
        )
        
        # Residual connection
        final_features = transformer_out + attended_features
        
        # Multi-layer prediction with residual connections
        x = final_features
        for i, layer in enumerate(self.predictor[:-1]):
            residual = x if x.shape[-1] == layer[0].in_features else None
            x = layer(x)
            if residual is not None:
                x = x + residual
        
        # Final output layer
        logits = self.predictor[-1](x)
        
        # Numerical stability
        logits = torch.clamp(logits, min=-8, max=8)
        
        return logits

def train_optimized_model():
    """Train the optimized model"""
    print("🚀 Training Optimized Architecture")
    print("Advanced transformer-based model with multi-scale features")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Load data
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]
    print(f"Using {len(df)} samples")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # Create optimized model
    model = OptimizedCodonModel()
    model = model.to(device)
    
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Trainable parameters: {total_params:,}")
    
    # Create dataset
    dataset = FixedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    
    if len(dataset) == 0:
        print("❌ No valid data found")
        return 0.0
    
    # Data split
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    print(f"Training: {len(train_dataset)}, Validation: {len(val_dataset)}")
    
    # Data loaders with smaller batch size for complex model
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=fixed_collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=fixed_collate_fn)
    
    # Load class weights
    try:
        with open('pure_codon_weights.json', 'r') as f:
            weight_info = json.load(f)
        codon_weights = weight_info['mixed_weights']
        weight_tensor = torch.ones(len(model.codon_to_idx))
        
        for codon, weight in codon_weights.items():
            if codon in model.codon_to_idx:
                idx = model.codon_to_idx[codon]
                weight_tensor[idx] = weight
        
        weight_tensor = weight_tensor / weight_tensor.mean()
        weight_tensor = weight_tensor.to(device)
        print("✅ Using balanced class weights")
    except:
        weight_tensor = None
        print("⚠️ Using uniform weights")
    
    # Loss function
    if weight_tensor is not None:
        criterion = nn.CrossEntropyLoss(weight=weight_tensor, ignore_index=-100, label_smoothing=0.1)
    else:
        criterion = nn.CrossEntropyLoss(ignore_index=-100, label_smoothing=0.1)
    
    # Optimizer with different learning rates for different components
    esm_params = []
    transformer_params = []
    other_params = []
    
    for name, param in model.named_parameters():
        if 'esm2' in name:
            esm_params.append(param)
        elif 'transformer' in name or 'attention' in name:
            transformer_params.append(param)
        else:
            other_params.append(param)
    
    optimizer = optim.AdamW([
        {'params': other_params, 'lr': 1e-4},
        {'params': transformer_params, 'lr': 5e-5},
    ], weight_decay=0.01)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=50, T_mult=2)
    
    best_val_acc = 0.0
    patience = 50
    patience_counter = 0
    
    print(f"\n🎯 Training Configuration:")
    print(f"  Epochs: 200")
    print(f"  Batch size: {batch_size}")
    print(f"  Learning rates: 1e-4 (main), 5e-5 (transformer)")
    print(f"  Patience: {patience}")
    
    print(f"\n🏃 Starting optimized training...")
    
    def calculate_accuracy(logits, targets, ignore_index=-100):
        with torch.no_grad():
            predictions = torch.argmax(logits, dim=-1)
            mask = (targets != ignore_index)
            
            if mask.sum() == 0:
                return 0.0
            
            correct = (predictions == targets).float() * mask.float()
            accuracy = correct.sum() / mask.sum()
            
            return accuracy.item()
    
    for epoch in range(200):
        # Training phase
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc=f'Epoch {epoch+1}/200 [Train]', leave=False):
            try:
                if batch is None:
                    continue
                
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                logits = model(proteins)
                
                # Align dimensions
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                loss = criterion(
                    logits.reshape(-1, logits.size(-1)),
                    targets.reshape(-1)
                )
                
                if torch.isnan(loss) or torch.isinf(loss):
                    continue
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                train_acc += calculate_accuracy(logits, targets)
                train_batches += 1
                
            except Exception as e:
                continue
        
        # Validation phase
        model.eval()
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f'Epoch {epoch+1}/200 [Val]', leave=False):
                try:
                    if batch is None:
                        continue
                    
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    val_acc += calculate_accuracy(logits, targets)
                    val_batches += 1
                    
                except Exception as e:
                    continue
        
        # Calculate averages
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        
        scheduler.step()
        
        # Calculate overfitting gap
        overfitting_gap = avg_train_acc - avg_val_acc
        current_lr = scheduler.get_last_lr()[0]
        
        print(f'Epoch {epoch+1:3d}/200: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%, Gap={overfitting_gap*100:.1f}%, LR={current_lr:.2e}')
        
        # Check for improvement
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'val_accuracy': avg_val_acc,
                'train_accuracy': avg_train_acc,
                'overfitting_gap': overfitting_gap
            }, 'optimized_best_model.pth')
            
            print(f'  🏆 New best model! Validation accuracy: {best_val_acc*100:.1f}%')
            
            if avg_val_acc >= 0.8:
                print("🎉 Reached 80% target accuracy!")
                break
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= patience:
            print(f'Early stopping: No improvement for {patience} epochs')
            break
        
        # Memory cleanup
        if (epoch + 1) % 10 == 0:
            gc.collect()
            torch.cuda.empty_cache()
    
    print(f"\n🏁 Optimized training complete!")
    print(f"Best validation accuracy: {best_val_acc*100:.1f}%")
    
    return best_val_acc

if __name__ == "__main__":
    best_accuracy = train_optimized_model()
    
    if best_accuracy >= 0.8:
        print("🎉 SUCCESS: Optimized architecture achieved 80% target!")
    elif best_accuracy >= 0.65:
        print(f"🎯 EXCELLENT: Reached {best_accuracy*100:.1f}% accuracy")
    else:
        print(f"📈 IMPROVEMENT: Current best {best_accuracy*100:.1f}%")
