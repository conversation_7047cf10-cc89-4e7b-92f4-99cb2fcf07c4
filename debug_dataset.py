#!/usr/bin/env python3
"""
调试数据集创建问题
"""

import pandas as pd
from codon_preference_model import CodonPreferenceModel

def debug_dataset():
    """调试数据集创建"""
    print("调试数据集创建问题...")
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    print(f"加载了 {len(df)} 条数据")
    
    # 过滤长度
    max_length = 500
    df = df[df['protein_length'] <= max_length]
    print(f"过滤后剩余 {len(df)} 条数据")
    
    # 创建模型获取codon_to_idx
    print("创建模型...")
    model = CodonPreferenceModel()
    print(f"密码子词汇表: {len(model.codon_to_idx)} 个密码子")
    print("前10个密码子:", list(model.codon_to_idx.keys())[:10])
    
    # 检查前几条数据
    print("\n检查前5条数据:")
    valid_count = 0
    
    for idx, row in df.head(10).iterrows():
        protein_seq = row['protein_sequence']
        dna_seq = row['nucleotide_sequence']
        
        print(f"\n数据 {idx+1}:")
        print(f"  蛋白质长度: {len(protein_seq)}")
        print(f"  DNA长度: {len(dna_seq)}")
        print(f"  长度比例: {len(dna_seq) / len(protein_seq)}")
        
        # 检查长度匹配
        if len(protein_seq) * 3 == len(dna_seq):
            print("  ✓ 长度匹配")
            
            # 提取密码子
            codons = [dna_seq[i:i+3] for i in range(0, len(dna_seq), 3)]
            print(f"  密码子数量: {len(codons)}")
            
            # 检查密码子是否在词汇表中
            valid_codons = 0
            invalid_codons = []
            
            for codon in codons:
                if codon in model.codon_to_idx:
                    valid_codons += 1
                else:
                    invalid_codons.append(codon)
            
            print(f"  有效密码子: {valid_codons}/{len(codons)}")
            if invalid_codons:
                print(f"  无效密码子: {set(invalid_codons)}")
            
            if len(invalid_codons) == 0:
                valid_count += 1
                print("  ✓ 数据有效")
            else:
                print("  ✗ 包含无效密码子")
        else:
            print("  ✗ 长度不匹配")
    
    print(f"\n前10条数据中有效数据: {valid_count}")
    
    # 检查所有数据
    print("\n检查所有数据...")
    total_valid = 0
    
    for idx, row in df.iterrows():
        protein_seq = row['protein_sequence']
        dna_seq = row['nucleotide_sequence']
        
        if len(protein_seq) * 3 == len(dna_seq):
            codons = [dna_seq[i:i+3] for i in range(0, len(dna_seq), 3)]
            
            valid = True
            for codon in codons:
                if codon not in model.codon_to_idx:
                    valid = False
                    break
            
            if valid:
                total_valid += 1
    
    print(f"总有效数据: {total_valid}/{len(df)}")
    
    # 检查遗传密码表
    print(f"\n遗传密码表检查:")
    print(f"模型中的密码子数量: {len(model.codon_to_idx)}")
    print(f"遗传密码表中的密码子数量: {len(model.genetic_code)}")
    
    # 找出数据中的所有密码子
    all_codons_in_data = set()
    for idx, row in df.iterrows():
        dna_seq = row['nucleotide_sequence']
        codons = [dna_seq[i:i+3] for i in range(0, len(dna_seq), 3)]
        all_codons_in_data.update(codons)
    
    print(f"数据中的密码子数量: {len(all_codons_in_data)}")
    
    # 找出不在模型词汇表中的密码子
    missing_codons = all_codons_in_data - set(model.codon_to_idx.keys())
    print(f"不在模型词汇表中的密码子: {missing_codons}")
    
    # 检查这些密码子在遗传密码表中的情况
    for codon in missing_codons:
        if codon in model.genetic_code:
            aa = model.genetic_code[codon]
            print(f"  {codon} -> {aa} (在遗传密码表中)")
        else:
            print(f"  {codon} -> 未知密码子")

if __name__ == "__main__":
    debug_dataset()
