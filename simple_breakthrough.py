#!/usr/bin/env python3
"""
简化突破模型 - Simple Breakthrough Model
专注于核心改进，避免复杂依赖

目标：通过关键改进将准确度从55%提升到65%+
策略：
1. 高级特征工程（生物学相关）
2. 改进的机器学习方法
3. 集成学习
"""

import numpy as np
import pandas as pd
import json
import time
import warnings
from collections import Counter
import random
warnings.filterwarnings('ignore')

class BiologicalFeatureExtractor:
    """生物学特征提取器"""
    
    def __init__(self):
        # 密码子使用频率（大肠杆菌优化）
        self.codon_usage = {
            'TTT': 0.58, 'TTC': 0.42, 'TTA': 0.14, 'TTG': 0.13,
            'TCT': 0.17, 'TCC': 0.15, 'TCA': 0.14, 'TCG': 0.14,
            'TAT': 0.59, 'TAC': 0.41, 'TGT': 0.46, 'TGC': 0.54, 'TGG': 1.00,
            'CTT': 0.12, 'CTC': 0.10, 'CTA': 0.04, 'CTG': 0.47,
            'CCT': 0.18, 'CCC': 0.13, 'CCA': 0.20, 'CCG': 0.49,
            'CAT': 0.57, 'CAC': 0.43, 'CAA': 0.34, 'CAG': 0.66,
            'CGT': 0.36, 'CGC': 0.36, 'CGA': 0.07, 'CGG': 0.11,
            'ATT': 0.49, 'ATC': 0.39, 'ATA': 0.11, 'ATG': 1.00,
            'ACT': 0.19, 'ACC': 0.40, 'ACA': 0.17, 'ACG': 0.25,
            'AAT': 0.49, 'AAC': 0.51, 'AAA': 0.74, 'AAG': 0.26,
            'AGT': 0.16, 'AGC': 0.25, 'AGA': 0.07, 'AGG': 0.04,
            'GTT': 0.28, 'GTC': 0.20, 'GTA': 0.17, 'GTG': 0.35,
            'GCT': 0.18, 'GCC': 0.26, 'GCA': 0.23, 'GCG': 0.33,
            'GAT': 0.63, 'GAC': 0.37, 'GAA': 0.68, 'GAG': 0.32,
            'GGT': 0.35, 'GGC': 0.37, 'GGA': 0.13, 'GGG': 0.15
        }
        
        # 氨基酸属性（扩展）
        self.aa_properties = {
            'A': [1.8, 0, 0, 0, 0, 89.1],   # 疏水性, 极性, 正电, 负电, 芳香性, 分子量
            'R': [-4.5, 1, 1, 0, 0, 174.2],
            'N': [-3.5, 1, 0, 0, 0, 132.1],
            'D': [-3.5, 1, 0, 1, 0, 133.1],
            'C': [2.5, 0, 0, 0, 0, 121.0],
            'Q': [-3.5, 1, 0, 0, 0, 146.1],
            'E': [-3.5, 1, 0, 1, 0, 147.1],
            'G': [-0.4, 0, 0, 0, 0, 75.1],
            'H': [-3.2, 1, 1, 0, 1, 155.2],
            'I': [4.5, 0, 0, 0, 0, 131.2],
            'L': [3.8, 0, 0, 0, 0, 131.2],
            'K': [-3.9, 1, 1, 0, 0, 146.2],
            'M': [1.9, 0, 0, 0, 0, 149.2],
            'F': [2.8, 0, 0, 0, 1, 165.2],
            'P': [-1.6, 0, 0, 0, 0, 115.1],
            'S': [-0.8, 1, 0, 0, 0, 105.1],
            'T': [-0.7, 1, 0, 0, 0, 119.1],
            'W': [-0.9, 0, 0, 0, 1, 204.2],
            'Y': [-1.3, 1, 0, 0, 1, 181.2],
            'V': [4.2, 0, 0, 0, 0, 117.1],
        }
        
        # 遗传密码
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def extract_enhanced_features(self, protein_seq, nucleotide_seq, position):
        """提取增强特征"""
        features = []
        
        # 1. 基本氨基酸特征
        aa = protein_seq[position]
        aa_props = self.aa_properties.get(aa, [0] * 6)
        features.extend(aa_props)
        
        # 2. 位置特征
        rel_pos = position / len(protein_seq)
        features.extend([rel_pos, len(protein_seq)])
        
        # 3. 扩展上下文特征（±3位置）
        for offset in [-3, -2, -1, 1, 2, 3]:
            ctx_pos = position + offset
            if 0 <= ctx_pos < len(protein_seq):
                ctx_aa = protein_seq[ctx_pos]
                ctx_props = self.aa_properties.get(ctx_aa, [0] * 6)
                features.extend(ctx_props[:3])  # 疏水性, 极性, 正电
            else:
                features.extend([0, 0, 0])
        
        # 4. 密码子特征
        codon_start = position * 3
        if codon_start + 3 <= len(nucleotide_seq):
            codon = nucleotide_seq[codon_start:codon_start + 3]
            
            # GC含量
            gc_content = (codon.count('G') + codon.count('C')) / 3
            features.append(gc_content)
            
            # 密码子使用偏好
            codon_usage = self.codon_usage.get(codon, 0.5)
            features.append(codon_usage)
            
            # Wobble位置特征
            wobble_gc = 1 if codon[2] in 'GC' else 0
            features.append(wobble_gc)
            
            # 密码子稳定性
            stability = gc_content * 0.7 + wobble_gc * 0.3
            features.append(stability)
        else:
            features.extend([0.5, 0.5, 0, 0.5])
        
        # 5. 局部序列特征
        window_size = 9
        window_start = max(0, codon_start - window_size)
        window_end = min(len(nucleotide_seq), codon_start + window_size + 3)
        window_seq = nucleotide_seq[window_start:window_end]
        
        if len(window_seq) > 0:
            local_gc = (window_seq.count('G') + window_seq.count('C')) / len(window_seq)
            features.append(local_gc)
        else:
            features.append(0.5)
        
        # 6. 蛋白质全局特征
        # 整体疏水性
        global_hydrophobicity = np.mean([self.aa_properties.get(aa, [0]*6)[0] for aa in protein_seq])
        features.append(global_hydrophobicity)
        
        # 整体电荷
        positive_count = sum(1 for aa in protein_seq if self.aa_properties.get(aa, [0]*6)[2] > 0)
        negative_count = sum(1 for aa in protein_seq if self.aa_properties.get(aa, [0]*6)[3] > 0)
        net_charge = (positive_count - negative_count) / len(protein_seq)
        features.append(net_charge)
        
        return features

class SimpleEnsembleClassifier:
    """简单集成分类器"""
    
    def __init__(self):
        self.models = []
        self.weights = []
    
    def add_model(self, model, weight=1.0):
        """添加模型"""
        self.models.append(model)
        self.weights.append(weight)
    
    def fit(self, X, y):
        """训练所有模型"""
        for model in self.models:
            model.fit(X, y)
    
    def predict(self, X):
        """集成预测"""
        predictions = []
        for model in self.models:
            pred = model.predict(X)
            predictions.append(pred)
        
        # 加权投票
        ensemble_pred = []
        for i in range(len(X)):
            votes = {}
            for j, pred in enumerate(predictions):
                vote = pred[i]
                weight = self.weights[j]
                votes[vote] = votes.get(vote, 0) + weight
            
            best_vote = max(votes.items(), key=lambda x: x[1])[0]
            ensemble_pred.append(best_vote)
        
        return np.array(ensemble_pred)

class SimpleRandomForest:
    """简化随机森林实现"""
    
    def __init__(self, n_trees=50, max_depth=10, min_samples=5):
        self.n_trees = n_trees
        self.max_depth = max_depth
        self.min_samples = min_samples
        self.trees = []
    
    def fit(self, X, y):
        """训练随机森林"""
        self.trees = []
        n_samples, n_features = X.shape
        
        for _ in range(self.n_trees):
            # Bootstrap采样
            indices = np.random.choice(n_samples, n_samples, replace=True)
            X_bootstrap = X[indices]
            y_bootstrap = y[indices]
            
            # 特征子集
            feature_indices = np.random.choice(n_features, int(np.sqrt(n_features)), replace=False)
            
            # 训练决策树
            tree = self._build_tree(X_bootstrap[:, feature_indices], y_bootstrap, 0)
            self.trees.append((tree, feature_indices))
    
    def _build_tree(self, X, y, depth):
        """构建决策树"""
        if depth >= self.max_depth or len(X) < self.min_samples or len(np.unique(y)) == 1:
            return {'prediction': Counter(y).most_common(1)[0][0]}
        
        best_feature = None
        best_threshold = None
        best_gain = -1
        
        n_features = X.shape[1]
        for feature in range(n_features):
            thresholds = np.unique(X[:, feature])
            for threshold in thresholds:
                left_mask = X[:, feature] <= threshold
                right_mask = ~left_mask
                
                if np.sum(left_mask) == 0 or np.sum(right_mask) == 0:
                    continue
                
                gain = self._information_gain(y, y[left_mask], y[right_mask])
                if gain > best_gain:
                    best_gain = gain
                    best_feature = feature
                    best_threshold = threshold
        
        if best_feature is None:
            return {'prediction': Counter(y).most_common(1)[0][0]}
        
        left_mask = X[:, best_feature] <= best_threshold
        right_mask = ~left_mask
        
        return {
            'feature': best_feature,
            'threshold': best_threshold,
            'left': self._build_tree(X[left_mask], y[left_mask], depth + 1),
            'right': self._build_tree(X[right_mask], y[right_mask], depth + 1)
        }
    
    def _information_gain(self, parent, left, right):
        """计算信息增益"""
        def entropy(labels):
            if len(labels) == 0:
                return 0
            counts = Counter(labels)
            probs = [count / len(labels) for count in counts.values()]
            return -sum(p * np.log2(p + 1e-10) for p in probs)
        
        parent_entropy = entropy(parent)
        left_weight = len(left) / len(parent)
        right_weight = len(right) / len(parent)
        
        weighted_entropy = left_weight * entropy(left) + right_weight * entropy(right)
        return parent_entropy - weighted_entropy
    
    def predict(self, X):
        """预测"""
        predictions = []
        for x in X:
            votes = []
            for tree, feature_indices in self.trees:
                pred = self._predict_tree(tree, x[feature_indices])
                votes.append(pred)
            
            prediction = Counter(votes).most_common(1)[0][0]
            predictions.append(prediction)
        
        return np.array(predictions)
    
    def _predict_tree(self, tree, x):
        """单棵树预测"""
        if 'prediction' in tree:
            return tree['prediction']
        
        if x[tree['feature']] <= tree['threshold']:
            return self._predict_tree(tree['left'], x)
        else:
            return self._predict_tree(tree['right'], x)

def create_enhanced_features():
    """创建增强特征"""
    print("🔧 创建增强特征数据")
    print("=" * 40)
    
    # 加载数据
    try:
        df = pd.read_csv("processed_BL21_data.csv")
        print(f"✅ 加载 {len(df)} 个样本")
    except FileNotFoundError:
        print("❌ processed_BL21_data.csv 未找到")
        return None, None, None
    
    # 过滤数据
    df = df[df['protein_length'] <= 512]
    print(f"✅ 使用 {len(df)} 个样本")
    
    # 创建特征提取器
    extractor = BiologicalFeatureExtractor()
    
    # 创建密码子映射
    codons = [codon for codon, aa in extractor.genetic_code.items()]
    codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    print(f"✅ 密码子类别数: {len(codon_to_idx)}")
    
    # 提取特征和标签
    features = []
    labels = []
    
    successful_samples = 0
    
    print("🔄 提取特征中...")
    
    for idx, row in df.iterrows():
        try:
            protein_seq = row['protein_sequence']
            nucleotide_seq = row['nucleotide_sequence']
            
            # 基本验证
            if len(nucleotide_seq) % 3 != 0:
                continue
            
            # 提取密码子
            codons_in_seq = [nucleotide_seq[i:i+3] for i in range(0, len(nucleotide_seq), 3)]
            
            # 移除终止密码子
            if len(codons_in_seq) > len(protein_seq):
                codons_in_seq = codons_in_seq[:len(protein_seq)]
            
            if len(codons_in_seq) != len(protein_seq):
                continue
            
            # 处理每个位置
            for pos, (aa, codon) in enumerate(zip(protein_seq, codons_in_seq)):
                if codon not in codon_to_idx:
                    continue
                if aa not in extractor.aa_properties:
                    continue
                
                # 验证遗传密码一致性
                expected_aa = extractor.genetic_code.get(codon, 'X')
                if expected_aa != aa:
                    continue
                
                # 提取增强特征
                feature_vector = extractor.extract_enhanced_features(
                    protein_seq, nucleotide_seq, pos
                )
                
                features.append(feature_vector)
                labels.append(codon_to_idx[codon])
            
            successful_samples += 1
            
            if successful_samples % 500 == 0:
                print(f"   处理了 {successful_samples} 个样本...")
                
        except Exception as e:
            continue
    
    print(f"✅ 成功处理 {successful_samples} 个样本")
    print(f"✅ 创建 {len(features)} 个特征向量")
    
    if len(features) == 0:
        return None, None, None
    
    X = np.array(features)
    y = np.array(labels)
    
    print(f"✅ 特征矩阵形状: {X.shape}")
    print(f"✅ 特征维度: {X.shape[1]}")
    
    return X, y, codon_to_idx

def train_simple_breakthrough():
    """训练简化突破模型"""
    print("\n🚀 简化突破模型训练")
    print("目标：通过核心改进将准确度从55%提升到65%+")
    print("=" * 60)

    # 创建增强特征
    X, y, codon_mapping = create_enhanced_features()

    if X is None:
        print("❌ 特征创建失败")
        return

    print(f"\n📊 数据集统计:")
    print(f"   总样本数: {len(X):,}")
    print(f"   特征维度: {X.shape[1]}")
    print(f"   类别数: {len(np.unique(y))}")

    # 数据分割
    np.random.seed(42)
    indices = np.random.permutation(len(X))
    train_size = int(0.8 * len(X))

    train_indices = indices[:train_size]
    test_indices = indices[train_size:]

    X_train, X_test = X[train_indices], X[test_indices]
    y_train, y_test = y[train_indices], y[test_indices]

    print(f"\n🔄 数据分割:")
    print(f"   训练集: {len(X_train):,} 样本")
    print(f"   测试集: {len(X_test):,} 样本")

    # 特征标准化
    print(f"\n⚙️ 特征标准化...")
    X_mean = np.mean(X_train, axis=0)
    X_std = np.std(X_train, axis=0) + 1e-8

    X_train_scaled = (X_train - X_mean) / X_std
    X_test_scaled = (X_test - X_mean) / X_std

    # 创建模型
    print(f"\n🏗️ 创建模型...")

    # 简化随机森林
    print("训练随机森林...")
    start_time = time.time()
    rf_model = SimpleRandomForest(n_trees=30, max_depth=12, min_samples=10)
    rf_model.fit(X_train_scaled, y_train)
    rf_train_time = time.time() - start_time

    rf_pred = rf_model.predict(X_test_scaled)
    rf_accuracy = np.mean(rf_pred == y_test)

    print(f"✅ 随机森林结果:")
    print(f"   准确率: {rf_accuracy:.4f} ({rf_accuracy*100:.1f}%)")
    print(f"   训练时间: {rf_train_time:.2f} 秒")

    # K近邻改进版
    print("\n训练改进K近邻...")
    start_time = time.time()

    def improved_knn_predict(X_train, y_train, X_test, k=15):
        """改进的K近邻预测"""
        predictions = []

        for i, test_sample in enumerate(X_test):
            # 计算距离
            distances = np.sum((X_train - test_sample) ** 2, axis=1)

            # 找到k个最近邻
            nearest_indices = np.argsort(distances)[:k]
            nearest_labels = y_train[nearest_indices]

            # 加权投票（距离越近权重越大）
            nearest_distances = distances[nearest_indices]
            weights = 1 / (nearest_distances + 1e-8)

            # 计算加权投票
            label_weights = {}
            for label, weight in zip(nearest_labels, weights):
                label_weights[label] = label_weights.get(label, 0) + weight

            # 选择权重最大的标签
            best_label = max(label_weights.items(), key=lambda x: x[1])[0]
            predictions.append(best_label)

            if (i + 1) % 1000 == 0:
                print(f"     进度: {i+1}/{len(X_test)}")

        return np.array(predictions)

    # 使用子集进行K近邻（太慢了）
    test_subset_size = min(2000, len(X_test))
    test_subset_indices = np.random.choice(len(X_test), test_subset_size, replace=False)

    knn_pred_subset = improved_knn_predict(
        X_train_scaled, y_train,
        X_test_scaled[test_subset_indices],
        k=15
    )
    knn_accuracy = np.mean(knn_pred_subset == y_test[test_subset_indices])
    knn_train_time = time.time() - start_time

    print(f"✅ 改进K近邻结果 (子集测试):")
    print(f"   准确率: {knn_accuracy:.4f} ({knn_accuracy*100:.1f}%)")
    print(f"   训练时间: {knn_train_time:.2f} 秒")

    # 朴素贝叶斯改进版
    print("\n训练改进朴素贝叶斯...")
    start_time = time.time()

    def improved_naive_bayes(X_train, y_train, X_test):
        """改进的朴素贝叶斯"""
        classes = np.unique(y_train)
        class_priors = {}
        feature_stats = {}

        # 计算先验概率和特征统计
        for cls in classes:
            class_mask = (y_train == cls)
            class_priors[cls] = np.sum(class_mask) / len(y_train)

            class_features = X_train[class_mask]
            feature_stats[cls] = {
                'mean': np.mean(class_features, axis=0),
                'std': np.std(class_features, axis=0) + 1e-8
            }

        # 预测
        predictions = []
        for test_sample in X_test:
            class_scores = {}

            for cls in classes:
                # 计算对数似然
                mean = feature_stats[cls]['mean']
                std = feature_stats[cls]['std']

                log_likelihood = -0.5 * np.sum(((test_sample - mean) / std) ** 2)
                log_likelihood -= np.sum(np.log(std))

                # 加上先验概率
                class_scores[cls] = log_likelihood + np.log(class_priors[cls])

            # 选择得分最高的类别
            best_class = max(class_scores.items(), key=lambda x: x[1])[0]
            predictions.append(best_class)

        return np.array(predictions)

    nb_pred = improved_naive_bayes(X_train_scaled, y_train, X_test_scaled)
    nb_accuracy = np.mean(nb_pred == y_test)
    nb_train_time = time.time() - start_time

    print(f"✅ 改进朴素贝叶斯结果:")
    print(f"   准确率: {nb_accuracy:.4f} ({nb_accuracy*100:.1f}%)")
    print(f"   训练时间: {nb_train_time:.2f} 秒")

    # 集成模型
    print(f"\n🔗 创建集成模型...")
    ensemble = SimpleEnsembleClassifier()
    ensemble.add_model(rf_model, weight=2.0)  # 随机森林权重更高

    # 为集成模型创建朴素贝叶斯包装器
    class NBWrapper:
        def __init__(self):
            self.trained = False

        def fit(self, X, y):
            self.X_train = X
            self.y_train = y
            self.trained = True

        def predict(self, X):
            if not self.trained:
                raise ValueError("Model not trained")
            return improved_naive_bayes(self.X_train, self.y_train, X)

    nb_wrapper = NBWrapper()
    ensemble.add_model(nb_wrapper, weight=1.0)

    # 训练集成模型
    ensemble.fit(X_train_scaled, y_train)

    # 评估集成模型
    ensemble_pred = ensemble.predict(X_test_scaled)
    ensemble_accuracy = np.mean(ensemble_pred == y_test)

    print(f"✅ 集成模型结果:")
    print(f"   准确率: {ensemble_accuracy:.4f} ({ensemble_accuracy*100:.1f}%)")

    # 性能比较
    print(f"\n🏆 模型性能比较:")
    print(f"{'模型':<20} {'准确率':<12} {'训练时间':<12} {'状态'}")
    print("-" * 60)
    print(f"{'随机森林':<20} {rf_accuracy*100:<11.1f}% {rf_train_time:<11.1f}s {'✅ 完成'}")
    print(f"{'改进K近邻':<20} {knn_accuracy*100:<11.1f}% {knn_train_time:<11.1f}s {'✅ 完成'}")
    print(f"{'改进朴素贝叶斯':<20} {nb_accuracy*100:<11.1f}% {nb_train_time:<11.1f}s {'✅ 完成'}")
    print(f"{'集成模型':<20} {ensemble_accuracy*100:<11.1f}% {'-':<11} {'✅ 完成'}")

    # 与之前结果比较
    print(f"\n📈 与之前结果比较:")
    print(f"   之前最佳 (改进管道): 54.8%")
    print(f"   之前最佳 (XGBoost): 48.4%")

    best_accuracy = max(rf_accuracy, knn_accuracy, nb_accuracy, ensemble_accuracy)
    best_model = ['随机森林', '改进K近邻', '改进朴素贝叶斯', '集成模型'][
        [rf_accuracy, knn_accuracy, nb_accuracy, ensemble_accuracy].index(best_accuracy)
    ]

    print(f"   当前最佳 ({best_model}): {best_accuracy*100:.1f}%")

    # 分析改进效果
    previous_best = 0.548
    improvement = best_accuracy - previous_best

    if best_accuracy >= 0.65:
        print(f"\n🎉 突破成功！达到65%目标！")
        print(f"   改进幅度: +{improvement*100:.1f} 个百分点")
        print(f"   相对改进: +{(improvement/previous_best)*100:.1f}%")
    elif best_accuracy >= 0.6:
        print(f"\n📈 重要改进！准确率突破60%！")
        print(f"   改进幅度: +{improvement*100:.1f} 个百分点")
        print(f"   相对改进: +{(improvement/previous_best)*100:.1f}%")
    elif best_accuracy > previous_best:
        print(f"\n✅ 有所改进！")
        print(f"   改进幅度: +{improvement*100:.1f} 个百分点")
        print(f"   相对改进: +{(improvement/previous_best)*100:.1f}%")
    else:
        print(f"\n📊 需要进一步优化")

    # 保存结果
    results = {
        'random_forest_accuracy': float(rf_accuracy),
        'knn_accuracy': float(knn_accuracy),
        'naive_bayes_accuracy': float(nb_accuracy),
        'ensemble_accuracy': float(ensemble_accuracy),
        'best_accuracy': float(best_accuracy),
        'best_model': best_model,
        'previous_best': previous_best,
        'improvement': float(improvement),
        'relative_improvement': float((improvement/previous_best)*100),
        'feature_count': X.shape[1],
        'sample_count': len(X),
        'breakthrough_achieved': best_accuracy >= 0.6
    }

    with open('simple_breakthrough_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n💾 结果已保存到: simple_breakthrough_results.json")

    return best_accuracy

if __name__ == "__main__":
    try:
        best_accuracy = train_simple_breakthrough()

        print(f"\n🏁 简化突破训练完成！")

        if best_accuracy:
            print(f"最终最佳准确率: {best_accuracy*100:.1f}%")

            if best_accuracy >= 0.65:
                print("🎉 成功达到65%突破目标！")
            elif best_accuracy >= 0.6:
                print("📈 重要改进！突破60%关口！")
            elif best_accuracy > 0.55:
                print("✅ 有所改进！为进一步突破奠定基础！")
            else:
                print("📊 需要重新评估策略")
        else:
            print("❌ 训练过程中出现问题")

    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
