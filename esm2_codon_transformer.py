#!/usr/bin/env python3
"""
ESM2增强的CodonTransformer - ESM2-Enhanced CodonTransformer
结合ESM2蛋白质表示和CodonTransformer架构，目标准确率80%+

核心改进：
1. 使用ESM2提取蛋白质特征（替代简单嵌入）
2. 保持CodonTransformer的合并序列设计
3. 融合蛋白质上下文和密码子预测
4. GPU优化训练
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.cuda.amp import GradScaler, autocast
import pandas as pd
import numpy as np
from transformers import EsmModel, EsmTokenizer, AutoTokenizer, PreTrainedTokenizerFast
import json
import time
import warnings
from tqdm import tqdm
import gc
import math
warnings.filterwarnings('ignore')

# 设置GPU优化
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False

class ESM2CodonTransformer(nn.Module):
    """ESM2增强的CodonTransformer模型"""
    
    def __init__(self, 
                 esm_model_name="facebook/esm2_t12_35M_UR50D",
                 codon_vocab_size=64,
                 num_organisms=164,
                 hidden_dim=1024,
                 num_layers=8,
                 num_heads=16,
                 dropout=0.1,
                 max_length=1024):
        super().__init__()
        
        self.max_length = max_length
        self.hidden_dim = hidden_dim
        
        # ESM2蛋白质编码器
        print(f"🧬 加载ESM2模型: {esm_model_name}")
        self.esm_model = EsmModel.from_pretrained(esm_model_name)
        self.esm_tokenizer = EsmTokenizer.from_pretrained(esm_model_name)
        
        # 冻结ESM2的部分层以节省显存
        for param in self.esm_model.embeddings.parameters():
            param.requires_grad = False
        for i, layer in enumerate(self.esm_model.encoder.layer):
            if i < 6:  # 冻结前6层
                for param in layer.parameters():
                    param.requires_grad = False
        
        esm_dim = self.esm_model.config.hidden_size  # 通常是480或768
        
        # 密码子词汇表和嵌入
        self.codon_vocab_size = codon_vocab_size
        self.codon_embedding = nn.Embedding(codon_vocab_size, hidden_dim)
        
        # 物种嵌入
        self.organism_embedding = nn.Embedding(num_organisms, hidden_dim)
        
        # 特征融合层
        self.protein_projection = nn.Linear(esm_dim, hidden_dim)
        self.fusion_layer = nn.MultiheadAttention(hidden_dim, num_heads, dropout=dropout, batch_first=True)
        
        # Transformer解码器层
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True
        )
        self.transformer_decoder = nn.TransformerDecoder(decoder_layer, num_layers=num_layers)
        
        # 位置编码
        self.pos_embedding = nn.Embedding(max_length, hidden_dim)
        
        # 输出层
        self.output_norm = nn.LayerNorm(hidden_dim)
        self.output_dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(hidden_dim, codon_vocab_size)
        
        # 初始化权重
        self._init_weights()
        
        print(f"✅ ESM2-CodonTransformer初始化完成")
        print(f"   ESM2维度: {esm_dim}")
        print(f"   隐藏维度: {hidden_dim}")
        print(f"   密码子词汇量: {codon_vocab_size}")
        print(f"   物种数量: {num_organisms}")
    
    def _init_weights(self):
        """初始化权重"""
        for module in [self.codon_embedding, self.organism_embedding, 
                      self.protein_projection, self.classifier]:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.02)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, mean=0, std=0.02)
    
    def encode_protein_with_esm2(self, protein_sequences):
        """使用ESM2编码蛋白质序列"""
        # 准备ESM2输入
        esm_inputs = self.esm_tokenizer(
            protein_sequences, 
            return_tensors="pt", 
            padding=True, 
            truncation=True,
            max_length=self.max_length,
            add_special_tokens=True
        ).to(next(self.parameters()).device)
        
        # ESM2编码
        with torch.no_grad():
            esm_outputs = self.esm_model(**esm_inputs)
        
        # 获取序列表示（去除特殊token）
        protein_features = esm_outputs.last_hidden_state[:, 1:-1, :]  # 去除<cls>和<eos>
        
        # 投影到目标维度
        protein_features = self.protein_projection(protein_features)
        
        return protein_features, esm_inputs.attention_mask[:, 1:-1]
    
    def forward(self, protein_sequences, codon_ids=None, organism_ids=None, labels=None):
        """前向传播"""
        batch_size = len(protein_sequences)
        device = next(self.parameters()).device
        
        # 1. ESM2编码蛋白质
        protein_features, protein_mask = self.encode_protein_with_esm2(protein_sequences)
        seq_len = protein_features.size(1)
        
        # 2. 密码子嵌入（如果提供）
        if codon_ids is not None:
            codon_features = self.codon_embedding(codon_ids)
            # 确保长度匹配
            if codon_features.size(1) != seq_len:
                if codon_features.size(1) > seq_len:
                    codon_features = codon_features[:, :seq_len, :]
                else:
                    padding = torch.zeros(batch_size, seq_len - codon_features.size(1), self.hidden_dim, device=device)
                    codon_features = torch.cat([codon_features, padding], dim=1)
        else:
            # 训练时使用学习的查询向量
            codon_features = self.codon_embedding.weight[0].unsqueeze(0).unsqueeze(0).expand(batch_size, seq_len, -1)
        
        # 3. 物种嵌入
        if organism_ids is not None:
            organism_features = self.organism_embedding(organism_ids).unsqueeze(1).expand(-1, seq_len, -1)
            protein_features = protein_features + organism_features
        
        # 4. 位置编码
        positions = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)
        pos_features = self.pos_embedding(positions)
        protein_features = protein_features + pos_features
        codon_features = codon_features + pos_features
        
        # 5. 特征融合（蛋白质作为key/value，密码子作为query）
        fused_features, _ = self.fusion_layer(
            query=codon_features,
            key=protein_features,
            value=protein_features,
            key_padding_mask=~protein_mask
        )
        
        # 6. Transformer解码
        # 创建因果掩码
        causal_mask = torch.triu(torch.ones(seq_len, seq_len, device=device), diagonal=1).bool()
        
        decoded_features = self.transformer_decoder(
            tgt=fused_features,
            memory=protein_features,
            tgt_mask=causal_mask,
            memory_key_padding_mask=~protein_mask
        )
        
        # 7. 输出预测
        decoded_features = self.output_norm(decoded_features)
        decoded_features = self.output_dropout(decoded_features)
        logits = self.classifier(decoded_features)
        
        # 8. 计算损失
        loss = None
        if labels is not None:
            # 确保标签长度匹配
            if labels.size(1) != seq_len:
                if labels.size(1) > seq_len:
                    labels = labels[:, :seq_len]
                else:
                    padding = torch.full((batch_size, seq_len - labels.size(1)), -100, device=device)
                    labels = torch.cat([labels, padding], dim=1)
            
            loss_fct = nn.CrossEntropyLoss(ignore_index=-100)
            loss = loss_fct(logits.view(-1, self.codon_vocab_size), labels.view(-1))
        
        return {
            'logits': logits,
            'loss': loss,
            'protein_features': protein_features,
            'attention_mask': protein_mask
        }

class ESM2CodonDataset(Dataset):
    """ESM2-CodonTransformer数据集"""
    
    def __init__(self, proteins, nucleotides, organisms=None):
        self.proteins = proteins
        self.nucleotides = nucleotides
        self.organisms = organisms if organisms is not None else [0] * len(proteins)
        
        # 遗传密码
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        # 创建密码子到索引的映射
        codons = list(self.genetic_code.keys())
        self.codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
        self.codon_to_idx['<PAD>'] = len(self.codon_to_idx)
        self.codon_to_idx['<UNK>'] = len(self.codon_to_idx)
        
        print(f"✅ 数据集创建完成: {len(self.proteins)} 个样本")
        print(f"   密码子词汇量: {len(self.codon_to_idx)}")
    
    def __len__(self):
        return len(self.proteins)
    
    def __getitem__(self, idx):
        protein_seq = self.proteins[idx]
        nucleotide_seq = self.nucleotides[idx]
        organism_id = self.organisms[idx]
        
        # 提取密码子
        codons = []
        labels = []
        
        if len(nucleotide_seq) % 3 == 0:
            for i in range(0, len(nucleotide_seq), 3):
                codon = nucleotide_seq[i:i+3]
                if codon in self.codon_to_idx:
                    codons.append(self.codon_to_idx[codon])
                    labels.append(self.codon_to_idx[codon])
                else:
                    codons.append(self.codon_to_idx['<UNK>'])
                    labels.append(self.codon_to_idx['<UNK>'])
        
        # 确保长度匹配
        min_len = min(len(protein_seq), len(codons))
        protein_seq = protein_seq[:min_len]
        codons = codons[:min_len]
        labels = labels[:min_len]
        
        return {
            'protein_sequence': protein_seq,
            'codon_ids': torch.tensor(codons, dtype=torch.long),
            'organism_id': torch.tensor(organism_id, dtype=torch.long),
            'labels': torch.tensor(labels, dtype=torch.long)
        }

def collate_fn(batch):
    """批处理函数"""
    protein_sequences = [item['protein_sequence'] for item in batch]
    codon_ids = [item['codon_ids'] for item in batch]
    organism_ids = torch.stack([item['organism_id'] for item in batch])
    labels = [item['labels'] for item in batch]
    
    # 填充到相同长度
    max_len = max(len(seq) for seq in codon_ids)
    
    padded_codon_ids = []
    padded_labels = []
    
    for codon_seq, label_seq in zip(codon_ids, labels):
        pad_len = max_len - len(codon_seq)
        if pad_len > 0:
            padded_codon_ids.append(torch.cat([codon_seq, torch.full((pad_len,), 61, dtype=torch.long)]))  # 61是<PAD>
            padded_labels.append(torch.cat([label_seq, torch.full((pad_len,), -100, dtype=torch.long)]))
        else:
            padded_codon_ids.append(codon_seq)
            padded_labels.append(label_seq)
    
    return {
        'protein_sequences': protein_sequences,
        'codon_ids': torch.stack(padded_codon_ids),
        'organism_ids': organism_ids,
        'labels': torch.stack(padded_labels)
    }

def train_esm2_codon_transformer():
    """训练ESM2增强的CodonTransformer"""
    print("🚀 ESM2增强CodonTransformer训练")
    print("目标：通过ESM2蛋白质表示将准确率提升到80%+")
    print("=" * 70)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    if torch.cuda.is_available():
        print(f"GPU名称: {torch.cuda.get_device_name(0)}")
        print(f"GPU显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # 清理GPU缓存
    torch.cuda.empty_cache()
    
    # 加载数据
    print(f"\n📊 加载数据...")
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]  # 限制序列长度以适应ESM2
    print(f"使用 {len(df)} 个样本")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # 创建数据集
    dataset = ESM2CodonDataset(proteins, nucleotides)
    
    # 数据分割
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 数据加载器 - 使用较小的批量大小以适应ESM2
    batch_size = 8  # ESM2需要更多显存
    num_workers = 4
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=True
    )
    
    print(f"训练批次: {len(train_loader)}, 验证批次: {len(val_loader)}")
    print(f"批量大小: {batch_size}")
    
    # 创建模型
    print(f"\n🏗️ 创建ESM2-CodonTransformer模型...")
    model = ESM2CodonTransformer(
        esm_model_name="facebook/esm2_t12_35M_UR50D",  # 使用较小的ESM2模型
        codon_vocab_size=len(dataset.codon_to_idx),
        num_organisms=164,
        hidden_dim=768,  # 匹配ESM2维度
        num_layers=6,    # 减少层数以节省显存
        num_heads=12,
        dropout=0.1,
        max_length=512
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"可训练参数量: {total_params:,}")
    
    # 优化器和调度器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=1e-4,  # ESM2微调使用较小学习率
        weight_decay=0.01,
        eps=1e-6
    )

    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=1e-4,
        epochs=30,
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )

    # 混合精度训练
    scaler = GradScaler()

    print(f"\n🎯 训练配置:")
    print(f"  学习率: 1e-4 (ESM2微调)")
    print(f"  混合精度: 启用")
    print(f"  梯度缩放: 启用")
    print(f"  批量大小: {batch_size}")
    print(f"  训练轮数: 30")

    print(f"\n🏃 开始ESM2-CodonTransformer训练...")

    best_val_acc = 0.0
    patience = 10
    patience_counter = 0

    for epoch in range(30):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0

        epoch_start_time = time.time()

        for batch_idx, batch in enumerate(tqdm(train_loader, desc=f'Epoch {epoch+1}/30 [Train]')):
            try:
                optimizer.zero_grad()

                # 移动数据到GPU
                codon_ids = batch['codon_ids'].to(device)
                organism_ids = batch['organism_ids'].to(device)
                labels = batch['labels'].to(device)

                # 使用混合精度
                with autocast():
                    outputs = model(
                        protein_sequences=batch['protein_sequences'],
                        codon_ids=codon_ids,
                        organism_ids=organism_ids,
                        labels=labels
                    )

                    loss = outputs['loss']
                    logits = outputs['logits']

                # 反向传播
                scaler.scale(loss).backward()
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                scaler.step(optimizer)
                scaler.update()
                scheduler.step()

                # 计算准确率
                with torch.no_grad():
                    predictions = torch.argmax(logits, dim=-1)
                    mask = labels != -100
                    correct = (predictions == labels) & mask
                    accuracy = correct.sum().float() / mask.sum().float()

                    train_loss += loss.item()
                    train_acc += accuracy.item()
                    train_batches += 1

                # 显存管理
                if batch_idx % 50 == 0:
                    torch.cuda.empty_cache()

            except Exception as e:
                print(f"训练批次错误: {e}")
                continue

        epoch_time = time.time() - epoch_start_time

        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_acc = 0.0
        val_batches = 0

        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f'Epoch {epoch+1}/30 [Val]'):
                try:
                    codon_ids = batch['codon_ids'].to(device)
                    organism_ids = batch['organism_ids'].to(device)
                    labels = batch['labels'].to(device)

                    with autocast():
                        outputs = model(
                            protein_sequences=batch['protein_sequences'],
                            codon_ids=codon_ids,
                            organism_ids=organism_ids,
                            labels=labels
                        )

                        loss = outputs['loss']
                        logits = outputs['logits']

                    # 计算准确率
                    predictions = torch.argmax(logits, dim=-1)
                    mask = labels != -100
                    correct = (predictions == labels) & mask
                    accuracy = correct.sum().float() / mask.sum().float()

                    val_loss += loss.item()
                    val_acc += accuracy.item()
                    val_batches += 1

                except Exception as e:
                    continue

        # 计算平均值
        avg_train_loss = train_loss / max(train_batches, 1)
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_loss = val_loss / max(val_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        current_lr = scheduler.get_last_lr()[0]

        print(f'Epoch {epoch+1:2d}/30: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%, '
              f'Loss={avg_val_loss:.4f}, LR={current_lr:.2e}, Time={epoch_time:.1f}s')

        # 检查改进
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            patience_counter = 0

            # 保存最佳模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_accuracy': avg_val_acc,
                'train_accuracy': avg_train_acc,
                'codon_to_idx': dataset.codon_to_idx
            }, 'esm2_codon_transformer_best.pth')

            print(f'  🏆 新的最佳模型! 验证准确率: {best_val_acc*100:.1f}%')

            # 检查是否达到80%目标
            if avg_val_acc >= 0.8:
                print("🎉 达到80%目标准确率!")
                break
            elif avg_val_acc >= 0.75:
                print("🎯 接近80%目标!")
            elif avg_val_acc >= 0.7:
                print("📈 显著改进，超过70%!")
            elif avg_val_acc >= 0.6:
                print("✅ 突破60%关口!")
        else:
            patience_counter += 1

        # 早停
        if patience_counter >= patience:
            print(f'早停: {patience} 轮无改进')
            break

        # 定期清理显存
        if epoch % 3 == 0:
            torch.cuda.empty_cache()
            gc.collect()

    print(f"\n🏁 ESM2-CodonTransformer训练完成!")
    print(f"最佳验证准确率: {best_val_acc*100:.1f}%")

    # 与之前结果比较
    print(f"\n📈 性能比较:")
    print(f"   之前最佳 (GPU优化): ~51.6%")
    print(f"   之前最佳 (传统方法): 54.8%")
    print(f"   ESM2增强版: {best_val_acc*100:.1f}%")

    improvement = best_val_acc - 0.548
    if best_val_acc >= 0.8:
        print(f"🎉 成功达到80%目标!")
        print(f"改进幅度: +{improvement*100:.1f} 个百分点")
        print(f"相对改进: +{(improvement/0.548)*100:.1f}%")
    elif best_val_acc >= 0.75:
        print(f"🎯 接近80%目标!")
        print(f"改进幅度: +{improvement*100:.1f} 个百分点")
    elif best_val_acc >= 0.7:
        print(f"📈 显著突破!")
        print(f"改进幅度: +{improvement*100:.1f} 个百分点")
    elif best_val_acc > 0.548:
        print(f"✅ ESM2带来改进!")
        print(f"改进幅度: +{improvement*100:.1f} 个百分点")

    # 保存结果
    results = {
        'esm2_codon_transformer_accuracy': float(best_val_acc),
        'previous_best': 0.548,
        'improvement': float(improvement),
        'target_achieved': best_val_acc >= 0.8,
        'model_parameters': total_params,
        'batch_size': batch_size,
        'esm2_enhanced': True
    }

    with open('esm2_codon_transformer_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"💾 结果已保存到: esm2_codon_transformer_results.json")

    return best_val_acc

if __name__ == "__main__":
    try:
        best_accuracy = train_esm2_codon_transformer()

        print(f"\n🎯 ESM2-CodonTransformer训练总结:")
        print(f"最终准确率: {best_accuracy*100:.1f}%")

        if best_accuracy >= 0.8:
            print("🏆 成功达到80%突破目标!")
        elif best_accuracy >= 0.75:
            print("🎯 接近80%目标，ESM2显著提升性能!")
        elif best_accuracy >= 0.7:
            print("📈 重大突破，ESM2带来显著改进!")
        elif best_accuracy > 0.55:
            print("✅ ESM2增强有效，为进一步优化奠定基础!")

    except Exception as e:
        print(f"❌ ESM2训练失败: {e}")
        import traceback
        traceback.print_exc()
