#!/usr/bin/env python3
"""
密码子偏好性预测模型训练脚本
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

from codon_preference_model import CodonPreferenceModel

class CodonDataset(Dataset):
    """密码子数据集类"""
    
    def __init__(self, protein_sequences, nucleotide_sequences, codon_to_idx):
        self.protein_sequences = protein_sequences
        self.nucleotide_sequences = nucleotide_sequences
        self.codon_to_idx = codon_to_idx
        
        # 预处理数据
        self.data = []
        for prot_seq, dna_seq in zip(protein_sequences, nucleotide_sequences):
            # 移除终止密码子（如果存在）
            # 检查DNA序列末尾是否有终止密码子
            stop_codons = ['TAA', 'TAG', 'TGA']
            cleaned_dna = dna_seq

            # 如果DNA序列以终止密码子结尾，移除它
            if len(dna_seq) >= 3:
                last_codon = dna_seq[-3:]
                if last_codon in stop_codons:
                    cleaned_dna = dna_seq[:-3]

            # 确保序列长度匹配
            if len(prot_seq) * 3 == len(cleaned_dna):
                # 提取密码子
                codons = [cleaned_dna[i:i+3] for i in range(0, len(cleaned_dna), 3)]
                codon_indices = []

                valid = True
                for codon in codons:
                    if codon in codon_to_idx:
                        codon_indices.append(codon_to_idx[codon])
                    else:
                        valid = False
                        break

                if valid:
                    self.data.append({
                        'protein': prot_seq,
                        'codons': codon_indices
                    })
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx]

class CodonPreferenceLoss(nn.Module):
    """密码子偏好性损失函数"""
    
    def __init__(self, alpha=0.9, diversity_weight=0.1):
        super().__init__()
        self.alpha = alpha
        self.diversity_weight = diversity_weight
        self.ce_loss = nn.CrossEntropyLoss(ignore_index=-1)
    
    def forward(self, predicted_logits, true_codons, attention_mask=None):
        """
        计算损失
        predicted_logits: [batch_size, seq_len, vocab_size]
        true_codons: [batch_size, seq_len]
        """
        batch_size, seq_len, vocab_size = predicted_logits.shape
        
        # 主要损失：交叉熵
        ce_loss = self.ce_loss(
            predicted_logits.reshape(-1, vocab_size),
            true_codons.reshape(-1)
        )
        
        # 多样性损失：鼓励模型不总是预测最常见的密码子
        diversity_loss = self.compute_diversity_loss(predicted_logits, attention_mask)
        
        total_loss = self.alpha * ce_loss + (1 - self.alpha) * diversity_loss
        
        return total_loss, ce_loss, diversity_loss
    
    def compute_diversity_loss(self, logits, attention_mask=None):
        """计算多样性损失"""
        # 计算每个位置的熵
        probs = torch.softmax(logits, dim=-1)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
        
        if attention_mask is not None:
            entropy = entropy * attention_mask
            return -entropy.sum() / attention_mask.sum()
        else:
            return -entropy.mean()

def collate_fn(batch):
    """自定义批处理函数"""
    proteins = [item['protein'] for item in batch]
    codons = [item['codons'] for item in batch]
    
    # 找到最大长度
    max_len = max(len(seq) for seq in codons)
    
    # 填充序列
    padded_codons = []
    attention_masks = []
    
    for codon_seq in codons:
        padded = codon_seq + [-1] * (max_len - len(codon_seq))
        mask = [1] * len(codon_seq) + [0] * (max_len - len(codon_seq))
        
        padded_codons.append(padded)
        attention_masks.append(mask)
    
    return {
        'proteins': proteins,
        'codons': torch.tensor(padded_codons, dtype=torch.long),
        'attention_mask': torch.tensor(attention_masks, dtype=torch.float)
    }

def load_data(data_file="processed_BL21_data.csv"):
    """加载预处理后的数据"""
    print(f"正在加载数据: {data_file}")
    
    df = pd.read_csv(data_file)
    print(f"加载了 {len(df)} 条数据")
    
    # 过滤长度过长的序列（ESM2最大长度限制）
    max_length = 1024
    df = df[df['protein_length'] <= max_length]
    print(f"过滤后剩余 {len(df)} 条数据（长度 <= {max_length}）")
    
    return df['protein_sequence'].tolist(), df['nucleotide_sequence'].tolist()

def train_model(model, train_loader, val_loader, num_epochs=20, learning_rate=2e-4):
    """训练模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    model = model.to(device)
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
    criterion = CodonPreferenceLoss()
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    
    # 训练历史
    train_losses = []
    val_losses = []
    
    best_val_loss = float('inf')
    patience = 5
    patience_counter = 0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_ce_loss = 0.0
        train_div_loss = 0.0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        for batch in train_pbar:
            proteins = batch['proteins']
            true_codons = batch['codons'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            codon_probs, predicted_logits, _ = model(proteins)
            
            # 调整维度以匹配真实标签
            # ESM2输出包含特殊token，需要对齐
            if predicted_logits.shape[1] > true_codons.shape[1]:
                # 去掉<cls>token，保留中间部分
                start_idx = 1
                end_idx = start_idx + true_codons.shape[1]
                predicted_logits = predicted_logits[:, start_idx:end_idx, :]
            
            # 计算损失
            loss, ce_loss, div_loss = criterion(predicted_logits, true_codons, attention_mask)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            train_ce_loss += ce_loss.item()
            train_div_loss += div_loss.item()
            
            train_pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'CE': f'{ce_loss.item():.4f}',
                'Div': f'{div_loss.item():.4f}'
            })
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_ce_loss = 0.0
        val_div_loss = 0.0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for batch in val_pbar:
                proteins = batch['proteins']
                true_codons = batch['codons'].to(device)
                attention_mask = batch['attention_mask'].to(device)
                
                # 前向传播
                codon_probs, predicted_logits, _ = model(proteins)
                
                # 调整维度
                if predicted_logits.shape[1] > true_codons.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + true_codons.shape[1]
                    predicted_logits = predicted_logits[:, start_idx:end_idx, :]
                
                # 计算损失
                loss, ce_loss, div_loss = criterion(predicted_logits, true_codons, attention_mask)
                
                val_loss += loss.item()
                val_ce_loss += ce_loss.item()
                val_div_loss += div_loss.item()
                
                val_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'CE': f'{ce_loss.item():.4f}',
                    'Div': f'{div_loss.item():.4f}'
                })
        
        # 计算平均损失
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f} (CE: {train_ce_loss/len(train_loader):.4f}, Div: {train_div_loss/len(train_loader):.4f})')
        print(f'  Val Loss: {avg_val_loss:.4f} (CE: {val_ce_loss/len(val_loader):.4f}, Div: {val_div_loss/len(val_loader):.4f})')
        
        # 保存最佳模型和早停
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': best_val_loss,
                'train_loss': avg_train_loss
            }, 'best_codon_model.pth')
            print(f'  保存最佳模型 (Val Loss: {best_val_loss:.4f})')
        else:
            patience_counter += 1
            print(f'  验证损失未改善 ({patience_counter}/{patience})')

        # 早停检查
        if patience_counter >= patience:
            print(f'  早停：验证损失连续{patience}轮未改善')
            break

        scheduler.step()
        print(f'  Learning Rate: {scheduler.get_last_lr()[0]:.6f}')
        print()
    
    return train_losses, val_losses

def main():
    """主训练函数"""
    print("开始训练密码子偏好性预测模型")
    
    # 加载数据
    proteins, nucleotides = load_data()
    
    # 创建模型
    print("创建模型...")
    model = CodonPreferenceModel()
    
    # 创建数据集
    print("创建数据集...")
    dataset = CodonDataset(proteins, nucleotides, model.codon_to_idx)
    print(f"有效数据集大小: {len(dataset)}")
    
    # 划分训练集和验证集
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 创建数据加载器
    batch_size = 8 if torch.cuda.is_available() else 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)
    
    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    
    # 训练模型
    train_losses, val_losses = train_model(model, train_loader, val_loader, num_epochs=20)
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig('training_curve.png')
    plt.show()
    
    print("训练完成！")

if __name__ == "__main__":
    main()
