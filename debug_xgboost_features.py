#!/usr/bin/env python3
"""
Debug XGBoost Feature Creation
"""

import pandas as pd
import numpy as np

def debug_feature_creation():
    """Debug the feature creation process"""
    print("🔍 Debugging Feature Creation")
    print("=" * 40)
    
    # Load data
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]
    print(f"✅ Loaded {len(df)} samples")
    
    # Genetic code
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    # Check first few samples
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        protein_seq = row['protein_sequence']
        nucleotide_seq = row['nucleotide_sequence']
        
        print(f"\n--- Sample {i+1} ---")
        print(f"Protein length: {len(protein_seq)}")
        print(f"Nucleotide length: {len(nucleotide_seq)}")
        print(f"Nucleotide length % 3: {len(nucleotide_seq) % 3}")
        print(f"Expected protein length: {len(nucleotide_seq) // 3}")
        
        if len(nucleotide_seq) % 3 != 0:
            print("❌ Nucleotide length not divisible by 3")
            continue
            
        if len(nucleotide_seq) // 3 != len(protein_seq):
            print("❌ Length mismatch")
            continue
        
        # Extract codons
        codons = [nucleotide_seq[j:j+3] for j in range(0, len(nucleotide_seq), 3)]
        print(f"Number of codons: {len(codons)}")
        
        # Check first few positions
        valid_positions = 0
        for pos in range(min(5, len(protein_seq))):
            aa = protein_seq[pos]
            codon = codons[pos]
            expected_aa = genetic_code.get(codon, 'X')
            
            print(f"  Pos {pos}: {aa} <- {codon} (expected: {expected_aa})")
            
            if expected_aa == aa:
                valid_positions += 1
            else:
                print(f"    ❌ Mismatch!")
        
        print(f"Valid positions in first 5: {valid_positions}/5")
        
        if valid_positions > 0:
            print("✅ This sample has valid positions")
            break
    
    # Try a simpler approach - just count valid samples
    print(f"\n🔍 Checking all samples...")
    
    valid_samples = 0
    total_positions = 0
    
    for idx, row in df.iterrows():
        try:
            protein_seq = row['protein_sequence']
            nucleotide_seq = row['nucleotide_sequence']
            
            # Basic validation
            if len(nucleotide_seq) % 3 != 0:
                continue
            if len(nucleotide_seq) // 3 != len(protein_seq):
                continue
            
            # Extract codons
            codons = [nucleotide_seq[i:i+3] for i in range(0, len(nucleotide_seq), 3)]
            
            # Check positions
            sample_valid_positions = 0
            for pos, (aa, codon) in enumerate(zip(protein_seq, codons)):
                expected_aa = genetic_code.get(codon, 'X')
                if expected_aa == aa:
                    sample_valid_positions += 1
                    total_positions += 1
            
            if sample_valid_positions > 0:
                valid_samples += 1
                
        except Exception as e:
            print(f"Error processing sample {idx}: {e}")
            continue
    
    print(f"✅ Valid samples: {valid_samples}/{len(df)}")
    print(f"✅ Total valid positions: {total_positions}")
    
    if total_positions == 0:
        print("❌ No valid positions found!")
        print("This suggests a fundamental issue with the genetic code mapping or data format")
        
        # Let's check the actual genetic code in the data
        print("\n🔍 Checking actual codon->AA mappings in data...")
        codon_aa_pairs = {}
        
        for idx, row in df.head(100).iterrows():  # Check first 100 samples
            try:
                protein_seq = row['protein_sequence']
                nucleotide_seq = row['nucleotide_sequence']
                
                if len(nucleotide_seq) % 3 != 0:
                    continue
                if len(nucleotide_seq) // 3 != len(protein_seq):
                    continue
                
                codons = [nucleotide_seq[i:i+3] for i in range(0, len(nucleotide_seq), 3)]
                
                for aa, codon in zip(protein_seq, codons):
                    if codon not in codon_aa_pairs:
                        codon_aa_pairs[codon] = set()
                    codon_aa_pairs[codon].add(aa)
                    
            except:
                continue
        
        print(f"Found {len(codon_aa_pairs)} unique codons in data")
        
        # Show mismatches
        mismatches = 0
        for codon, aas in codon_aa_pairs.items():
            expected = genetic_code.get(codon, 'X')
            if len(aas) > 1 or (len(aas) == 1 and list(aas)[0] != expected):
                print(f"  {codon} -> {aas} (expected: {expected})")
                mismatches += 1
        
        print(f"Mismatches found: {mismatches}")

if __name__ == "__main__":
    debug_feature_creation()
