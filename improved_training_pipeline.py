#!/usr/bin/env python3
"""
Improved Training Pipeline
Builds on anti-overfitting success with enhanced architecture and training strategies
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import gc
import os
import time
import random
warnings.filterwarnings('ignore')

from anti_overfitting_training import AntiOverfittingModel, RegularizedLoss
from fixed_data_processor import FixedCodonDataset, fixed_collate_fn

class ImprovedCodonModel(nn.Module):
    """Improved codon prediction model with enhanced architecture"""
    
    def __init__(self, esm_model_path="facebook/esm2_t33_650M_UR50D"):
        super().__init__()
        
        # Load ESM2 model
        from transformers import EsmModel, EsmTokenizer
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            self.tokenizer = EsmTokenizer.from_pretrained(local_path)
            self.esm2 = EsmModel.from_pretrained(local_path)
        except:
            self.tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
            self.esm2 = EsmModel.from_pretrained(esm_model_path)
        
        # Freeze ESM2 parameters
        for param in self.esm2.parameters():
            param.requires_grad = False
            
        self.hidden_size = self.esm2.config.hidden_size
        
        # Build genetic code and codon vocabulary
        self.genetic_code = self._build_genetic_code()
        self.codon_to_idx = self._build_codon_vocab()
        
        # Enhanced feature extraction
        self.feature_norm = nn.LayerNorm(self.hidden_size)
        self.feature_dropout = nn.Dropout(0.2)
        
        # Multi-scale feature processing
        self.local_processor = nn.Sequential(
            nn.Conv1d(self.hidden_size, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        self.global_processor = nn.Sequential(
            nn.Conv1d(self.hidden_size, 256, kernel_size=7, padding=3),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # Attention mechanism for sequence modeling
        self.attention = nn.MultiheadAttention(
            embed_dim=512, 
            num_heads=8, 
            dropout=0.1, 
            batch_first=True
        )
        
        # Enhanced prediction head with residual connections
        self.predictor = nn.Sequential(
            nn.LayerNorm(512),
            nn.Dropout(0.3),
            
            # First block
            nn.Linear(512, 384),
            nn.LayerNorm(384),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            # Second block
            nn.Linear(384, 256),
            nn.LayerNorm(256),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            # Output layer
            nn.Linear(256, len(self.codon_to_idx))
        )
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        """Conservative weight initialization"""
        for module in [self.local_processor, self.global_processor, self.predictor]:
            for layer in module:
                if isinstance(layer, (nn.Linear, nn.Conv1d)):
                    nn.init.xavier_uniform_(layer.weight, gain=0.1)
                    if hasattr(layer, 'bias') and layer.bias is not None:
                        nn.init.zeros_(layer.bias)
    
    def _build_genetic_code(self):
        return {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def _build_codon_vocab(self):
        codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        return {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    def forward(self, protein_sequences):
        """Enhanced forward pass"""
        # ESM2 encoding
        encoded = self.tokenizer(
            protein_sequences,
            padding=True,
            truncation=True,
            max_length=1024,
            return_tensors="pt"
        )
        
        input_ids = encoded['input_ids'].to(next(self.parameters()).device)
        attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
        
        # ESM2 feature extraction
        with torch.no_grad():
            esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
        
        # Feature normalization and dropout
        features = self.feature_norm(esm_output.last_hidden_state)
        features = self.feature_dropout(features)
        
        # Multi-scale processing
        # Transpose for conv1d: [batch, seq, hidden] -> [batch, hidden, seq]
        features_t = features.transpose(1, 2)
        
        local_features = self.local_processor(features_t)  # [batch, 256, seq]
        global_features = self.global_processor(features_t)  # [batch, 256, seq]
        
        # Combine and transpose back: [batch, 512, seq] -> [batch, seq, 512]
        combined = torch.cat([local_features, global_features], dim=1).transpose(1, 2)
        
        # Self-attention
        attended, _ = self.attention(combined, combined, combined, key_padding_mask=~attention_mask.bool())
        
        # Residual connection
        enhanced = combined + attended
        
        # Prediction
        logits = self.predictor(enhanced)
        
        # Numerical stability
        logits = torch.clamp(logits, min=-10, max=10)
        
        return logits

def load_checkpoint_if_exists(checkpoint_path):
    """Load checkpoint if it exists"""
    if os.path.exists(checkpoint_path):
        print(f"📂 Loading checkpoint: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        start_epoch = checkpoint['epoch'] + 1
        best_val_acc = checkpoint.get('best_val_acc', 0.0)
        patience_counter = checkpoint.get('patience_counter', 0)
        
        print(f"✅ Resuming from epoch {start_epoch}, best accuracy: {best_val_acc*100:.1f}%")
        return start_epoch, best_val_acc, patience_counter, checkpoint
    else:
        print("📝 Starting fresh training")
        return 0, 0.0, 0, None

def save_checkpoint(epoch, model, optimizer, scheduler, best_val_acc, patience_counter, checkpoint_path, is_best=False):
    """Save training checkpoint"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'best_val_acc': best_val_acc,
        'patience_counter': patience_counter,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    torch.save(checkpoint, checkpoint_path)
    
    if is_best:
        best_path = checkpoint_path.replace('checkpoint', 'best')
        torch.save(checkpoint, best_path)
        print(f"💾 Saved best model: {best_path}")

def calculate_accuracy(logits, targets, ignore_index=-100):
    """Calculate accuracy"""
    with torch.no_grad():
        predictions = torch.argmax(logits, dim=-1)
        mask = (targets != ignore_index)
        
        if mask.sum() == 0:
            return 0.0
        
        correct = (predictions == targets).float() * mask.float()
        accuracy = correct.sum() / mask.sum()
        
        return accuracy.item()

def train_improved_pipeline():
    """Train the improved model pipeline"""
    print("🚀 Starting Improved Training Pipeline")
    print("Building on anti-overfitting success, targeting 80% accuracy")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Load data with fixed processing
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]
    print(f"Using {len(df)} samples")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # Create improved model
    model = ImprovedCodonModel()
    model = model.to(device)
    
    print(f"Model parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")
    
    # Create dataset with fixed processing
    dataset = FixedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    
    if len(dataset) == 0:
        print("❌ No valid data found")
        return 0.0
    
    # Data split - balanced approach
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    print(f"Training: {len(train_dataset)}, Validation: {len(val_dataset)}")
    
    # Data loaders
    batch_size = 8
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=fixed_collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=fixed_collate_fn)
    
    # Checkpoint management
    checkpoint_path = 'improved_pipeline_checkpoint.pth'
    start_epoch, best_val_acc, patience_counter, checkpoint = load_checkpoint_if_exists(checkpoint_path)
    
    # Load class weights
    try:
        with open('pure_codon_weights.json', 'r') as f:
            weight_info = json.load(f)
        codon_weights = weight_info['mixed_weights']
        weight_tensor = torch.ones(len(model.codon_to_idx))
        
        for codon, weight in codon_weights.items():
            if codon in model.codon_to_idx:
                idx = model.codon_to_idx[codon]
                weight_tensor[idx] = weight
        
        weight_tensor = weight_tensor / weight_tensor.mean()
        weight_tensor = weight_tensor.to(device)
        print("✅ Using balanced class weights")
    except:
        weight_tensor = None
        print("⚠️ Using uniform weights")
    
    # Loss function with regularization
    criterion = RegularizedLoss(class_weights=weight_tensor, label_smoothing=0.1)
    
    # Optimizer with adaptive learning rate
    optimizer = optim.AdamW(model.parameters(), lr=1e-5, weight_decay=0.05, betas=(0.9, 0.999))
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, 
        max_lr=1e-4, 
        epochs=300, 
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )
    
    # Restore checkpoint state
    if checkpoint is not None:
        try:
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            print("✅ Restored training state")
        except Exception as e:
            print(f"⚠️ Could not restore state: {e}")
    
    patience = 100
    
    print(f"\n🎯 Training Configuration:")
    print(f"  Epochs: 300")
    print(f"  Batch size: {batch_size}")
    print(f"  Learning rate: 1e-5 -> 1e-4 (OneCycle)")
    print(f"  Patience: {patience}")
    print(f"  Starting epoch: {start_epoch + 1}")
    print(f"  Best accuracy so far: {best_val_acc*100:.1f}%")
    
    print(f"\n🏃 Starting training...")
    
    for epoch in range(start_epoch, 300):
        # Training phase
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc=f'Epoch {epoch+1}/300 [Train]', leave=False):
            try:
                if batch is None:
                    continue
                
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                logits = model(proteins)
                
                # Align dimensions (handle ESM2 special tokens)
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                loss = criterion(logits, targets)
                
                if torch.isnan(loss) or torch.isinf(loss):
                    continue
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                scheduler.step()
                
                train_loss += loss.item()
                train_acc += calculate_accuracy(logits, targets)
                train_batches += 1
                
            except Exception as e:
                continue
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f'Epoch {epoch+1}/300 [Val]', leave=False):
                try:
                    if batch is None:
                        continue
                    
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    loss = criterion(logits, targets)
                    acc = calculate_accuracy(logits, targets)
                    
                    val_loss += loss.item()
                    val_acc += acc
                    val_batches += 1
                    
                except Exception as e:
                    continue
        
        # Calculate averages
        avg_train_loss = train_loss / max(train_batches, 1)
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_loss = val_loss / max(val_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        
        # Save checkpoint every epoch
        save_checkpoint(epoch, model, optimizer, scheduler, best_val_acc, patience_counter, checkpoint_path)
        
        # Calculate overfitting gap
        overfitting_gap = avg_train_acc - avg_val_acc
        current_lr = scheduler.get_last_lr()[0]
        
        print(f'Epoch {epoch+1:3d}/300: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%, Gap={overfitting_gap*100:.1f}%, LR={current_lr:.2e}')
        
        # Check for improvement
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            patience_counter = 0
            
            save_checkpoint(epoch, model, optimizer, scheduler, best_val_acc, patience_counter, checkpoint_path, is_best=True)
            
            print(f'  🏆 New best model! Validation accuracy: {best_val_acc*100:.1f}%')
            
            if avg_val_acc >= 0.8:
                print("🎉 Reached 80% target accuracy!")
                break
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= patience:
            print(f'Early stopping: No improvement for {patience} epochs')
            break
        
        # Memory cleanup
        if (epoch + 1) % 10 == 0:
            gc.collect()
            torch.cuda.empty_cache()
    
    print(f"\n🏁 Training complete!")
    print(f"Best validation accuracy: {best_val_acc*100:.1f}%")
    
    return best_val_acc

if __name__ == "__main__":
    best_accuracy = train_improved_pipeline()
    
    if best_accuracy >= 0.8:
        print("🎉 SUCCESS: Achieved 80% target accuracy!")
    elif best_accuracy >= 0.65:
        print(f"🎯 GOOD PROGRESS: Reached {best_accuracy*100:.1f}% accuracy")
        print("Consider ensemble methods or XGBoost comparison")
    else:
        print(f"📈 IMPROVEMENT: Current best {best_accuracy*100:.1f}%")
        print("Significant improvement over baseline 54.4%")
