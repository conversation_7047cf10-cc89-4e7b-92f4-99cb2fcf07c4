#!/usr/bin/env python3
"""
密码子偏好性预测模型
基于ESM2和Transformer的微生物密码子偏好性预测模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import EsmModel, EsmTokenizer
import numpy as np
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class CodonPreferenceModel(nn.Module):
    """密码子偏好性预测模型"""
    
    def __init__(self, esm_model_path: str = "facebook/esm2_t33_650M_UR50D"):
        super().__init__()

        # 加载ESM2模型和tokenizer
        print("正在加载ESM2模型...")
        try:
            # 首先尝试从本地路径加载
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            self.tokenizer = EsmTokenizer.from_pretrained(local_path)
            self.esm2 = EsmModel.from_pretrained(local_path)
            print("从本地路径加载ESM2模型成功")
        except:
            # 如果本地加载失败，从HuggingFace Hub加载
            print("本地加载失败，从HuggingFace Hub加载...")
            self.tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
            self.esm2 = EsmModel.from_pretrained(esm_model_path)
        
        # 冻结ESM2参数（可选择性微调）
        for param in self.esm2.parameters():
            param.requires_grad = False
            
        self.hidden_size = self.esm2.config.hidden_size  # 1280 for ESM2-650M
        
        # 构建遗传密码表和映射
        self.genetic_code = self._build_genetic_code()
        self.aa_to_codons = self._build_aa_to_codon_mapping()
        self.codon_to_idx = self._build_codon_vocab()
        self.idx_to_codon = {idx: codon for codon, idx in self.codon_to_idx.items()}
        
        print(f"密码子词汇表大小: {len(self.codon_to_idx)}")
        
        # 密码子预测头
        self.codon_predictor = nn.Sequential(
            nn.Linear(self.hidden_size, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, len(self.codon_to_idx))  # 61个密码子（排除终止密码子）
        )
        
        print("模型初始化完成")
    
    def _build_genetic_code(self) -> Dict[str, str]:
        """构建标准遗传密码表"""
        return {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
            'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def _build_aa_to_codon_mapping(self) -> Dict[str, List[str]]:
        """构建氨基酸到密码子的映射"""
        aa_to_codons = {}
        for codon, aa in self.genetic_code.items():
            if aa != '*':  # 排除终止密码子
                if aa not in aa_to_codons:
                    aa_to_codons[aa] = []
                aa_to_codons[aa].append(codon)
        return aa_to_codons
    
    def _build_codon_vocab(self) -> Dict[str, int]:
        """构建密码子词汇表"""
        all_codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        return {codon: idx for idx, codon in enumerate(sorted(all_codons))}
    
    def tokenize_protein_sequence(self, protein_sequences: List[str]) -> Dict[str, torch.Tensor]:
        """使用ESM2 tokenizer处理蛋白质序列"""
        # ESM2需要在序列前后添加特殊token
        formatted_sequences = [" ".join(list(seq)) for seq in protein_sequences]
        
        # 使用tokenizer
        encoded = self.tokenizer(
            formatted_sequences,
            padding=True,
            truncation=True,
            max_length=1024,  # ESM2的最大长度限制
            return_tensors="pt"
        )
        
        return encoded
    
    def apply_codon_constraints(self, logits: torch.Tensor, protein_sequences: List[str]) -> torch.Tensor:
        """
        应用硬约束：每个位置只能预测该氨基酸对应的合法密码子
        """
        batch_size, seq_len, vocab_size = logits.shape
        constrained_logits = torch.full_like(logits, float('-inf'))
        
        for b in range(batch_size):
            protein_seq = protein_sequences[b]
            # 注意：ESM2输出长度包含特殊token，需要对齐
            # 跳过<cls>token，从位置1开始
            for pos in range(min(len(protein_seq), seq_len - 2)):  # 减去<cls>和<eos>
                if pos + 1 < seq_len:  # 确保不越界
                    aa = protein_seq[pos]
                    
                    # 获取该氨基酸对应的所有合法密码子
                    if aa in self.aa_to_codons:
                        valid_codons = self.aa_to_codons[aa]
                        
                        # 只保留合法密码子的logits
                        for codon in valid_codons:
                            if codon in self.codon_to_idx:
                                codon_idx = self.codon_to_idx[codon]
                                constrained_logits[b, pos + 1, codon_idx] = logits[b, pos + 1, codon_idx]
        
        return constrained_logits
    
    def forward(self, protein_sequences: List[str], return_codons: bool = False) -> Tuple[torch.Tensor, torch.Tensor, Optional[List[List[str]]]]:
        """
        前向传播，确保输出严格遵循遗传密码表
        """
        # 1. 使用ESM2进行特征提取
        encoded = self.tokenize_protein_sequence(protein_sequences)
        
        # 将输入移到正确的设备
        input_ids = encoded['input_ids'].to(next(self.parameters()).device)
        attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
        
        # ESM2前向传播
        with torch.no_grad():  # ESM2参数被冻结
            esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
        
        # 获取上下文化的特征
        contextualized_features = esm_output.last_hidden_state
        
        # 2. 预测所有密码子的原始logits
        raw_codon_logits = self.codon_predictor(contextualized_features)
        
        # 3. 应用硬约束
        constrained_logits = self.apply_codon_constraints(raw_codon_logits, protein_sequences)
        
        # 4. 计算约束后的概率分布
        codon_probs = F.softmax(constrained_logits, dim=-1)
        
        # 5. 可选：直接解码为密码子序列
        predicted_codons = None
        if return_codons:
            predicted_codons = self.decode_to_codons(codon_probs, protein_sequences)
        
        return codon_probs, constrained_logits, predicted_codons
    
    def decode_to_codons(self, codon_probs: torch.Tensor, protein_sequences: List[str]) -> List[List[str]]:
        """将概率分布解码为实际的密码子序列"""
        batch_size = len(protein_sequences)
        predicted_codons = []
        
        for b in range(batch_size):
            sequence_codons = []
            protein_seq = protein_sequences[b]
            
            # 跳过<cls>token，从位置1开始
            for pos in range(len(protein_seq)):
                if pos + 1 < codon_probs.shape[1]:
                    aa = protein_seq[pos]
                    
                    if aa in self.aa_to_codons:
                        valid_codons = self.aa_to_codons[aa]
                        valid_probs = []
                        
                        for codon in valid_codons:
                            if codon in self.codon_to_idx:
                                codon_idx = self.codon_to_idx[codon]
                                prob = codon_probs[b, pos + 1, codon_idx].item()
                                valid_probs.append((codon, prob))
                        
                        # 选择概率最高的密码子
                        if valid_probs:
                            best_codon = max(valid_probs, key=lambda x: x[1])[0]
                            sequence_codons.append(best_codon)
                        else:
                            sequence_codons.append('NNN')  # 未知情况
                    else:
                        sequence_codons.append('NNN')  # 未知氨基酸
            
            predicted_codons.append(sequence_codons)
        
        return predicted_codons
    
    def predict_codon_sequence(self, protein_sequences: List[str]) -> Tuple[List[str], torch.Tensor]:
        """直接预测蛋白质序列对应的密码子序列"""
        self.eval()
        with torch.no_grad():
            codon_probs, _, predicted_codons = self.forward(protein_sequences, return_codons=True)
        
        # 将密码子序列连接成DNA序列
        dna_sequences = []
        for seq_codons in predicted_codons:
            dna_seq = ''.join(seq_codons)
            dna_sequences.append(dna_seq)
        
        return dna_sequences, codon_probs
    
    def get_device(self) -> torch.device:
        """获取模型所在设备"""
        return next(self.parameters()).device

def test_model():
    """测试模型基本功能"""
    print("测试密码子偏好性预测模型...")
    
    # 创建模型
    model = CodonPreferenceModel()
    
    # 测试序列
    test_proteins = [
        "MFVFLVLLPLVSSQCVNLTTRTQLPPA",
        "MKRISTTITTTITITTGNGAG"
    ]
    
    print(f"测试蛋白质序列: {test_proteins}")
    
    # 预测
    dna_sequences, codon_probs = model.predict_codon_sequence(test_proteins)
    
    print("\n预测结果:")
    for i, (protein, dna) in enumerate(zip(test_proteins, dna_sequences)):
        print(f"序列 {i+1}:")
        print(f"  蛋白质: {protein}")
        print(f"  预测DNA: {dna}")
        print(f"  长度检查: 蛋白质={len(protein)}, DNA={len(dna)}, 比例={len(dna)/len(protein)}")
        
        # 验证翻译
        predicted_codons = [dna[j:j+3] for j in range(0, len(dna), 3)]
        back_translated = ''.join([model.genetic_code.get(codon, 'X') for codon in predicted_codons])
        print(f"  回译验证: {back_translated}")
        print(f"  约束正确性: {'✓' if protein == back_translated else '✗'}")
        print()

if __name__ == "__main__":
    test_model()
