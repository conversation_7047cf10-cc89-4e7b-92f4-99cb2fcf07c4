#!/usr/bin/env python3
"""
大批次训练脚本
支持batch_size=500，使用梯度累积和内存优化技术
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import matplotlib.pyplot as plt
import gc
warnings.filterwarnings('ignore')

from fixed_training import FixedCodonPreferenceModel, calculate_accuracy
from improved_training import ImprovedCodonDataset, collate_fn

class LargeBatchLoss(nn.Module):
    """适用于大批次的损失函数"""
    
    def __init__(self, class_weights=None, ignore_index=-100):
        super().__init__()
        self.ignore_index = ignore_index
        if class_weights is not None:
            self.register_buffer('class_weights', class_weights)
        else:
            self.class_weights = None
    
    def forward(self, logits, targets):
        """计算损失，优化内存使用"""
        # 确保张量连续性
        logits = logits.contiguous()
        targets = targets.contiguous()
        
        # 分块处理大批次，避免内存溢出
        batch_size, seq_len, num_classes = logits.shape
        chunk_size = min(batch_size, 50)  # 每次处理50个样本
        
        total_loss = 0.0
        total_samples = 0
        
        for i in range(0, batch_size, chunk_size):
            end_idx = min(i + chunk_size, batch_size)
            
            logits_chunk = logits[i:end_idx]
            targets_chunk = targets[i:end_idx]
            
            # 重塑张量
            logits_flat = logits_chunk.view(-1, num_classes)
            targets_flat = targets_chunk.view(-1)
            
            # 计算有效样本数
            valid_mask = (targets_flat != self.ignore_index)
            valid_samples = valid_mask.sum().item()
            
            if valid_samples > 0:
                if self.class_weights is not None:
                    chunk_loss = F.cross_entropy(
                        logits_flat, 
                        targets_flat, 
                        weight=self.class_weights,
                        ignore_index=self.ignore_index,
                        reduction='sum'
                    )
                else:
                    chunk_loss = F.cross_entropy(
                        logits_flat, 
                        targets_flat, 
                        ignore_index=self.ignore_index,
                        reduction='sum'
                    )
                
                total_loss += chunk_loss
                total_samples += valid_samples
        
        if total_samples > 0:
            return total_loss / total_samples
        else:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)

def create_large_batch_dataloader(dataset, batch_size=500, num_workers=4):
    """创建大批次数据加载器"""
    return DataLoader(
        dataset, 
        batch_size=batch_size, 
        shuffle=True, 
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=True
    )

def gradient_accumulation_step(model, criterion, optimizer, batch, accumulation_steps, device, scaler=None):
    """梯度累积步骤"""
    proteins = batch['proteins']
    targets = batch['codons'].to(device)

    # 前向传播
    if scaler is not None:
        with torch.cuda.amp.autocast():
            logits = model(proteins)

            # 对齐维度
            if logits.shape[1] > targets.shape[1]:
                start_idx = 1
                end_idx = start_idx + targets.shape[1]
                logits = logits[:, start_idx:end_idx, :]

            # 计算损失
            loss = criterion(logits, targets)

            # 缩放损失（用于梯度累积）
            loss = loss / accumulation_steps

        # 使用scaler进行反向传播
        scaler.scale(loss).backward()
    else:
        logits = model(proteins)

        # 对齐维度
        if logits.shape[1] > targets.shape[1]:
            start_idx = 1
            end_idx = start_idx + targets.shape[1]
            logits = logits[:, start_idx:end_idx, :]

        # 计算损失
        loss = criterion(logits, targets)

        # 缩放损失（用于梯度累积）
        loss = loss / accumulation_steps

        # 反向传播
        loss.backward()

    # 计算准确度
    acc = calculate_accuracy(logits, targets)

    return loss.item() * accumulation_steps, acc

def train_large_batch_model():
    """大批次训练主函数"""
    print("开始大批次训练 (batch_size=500)")
    
    # 设置设备和内存优化
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]  # 限制序列长度以节省内存
    print(f"使用 {len(df)} 条数据进行训练")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # 创建模型
    model = FixedCodonPreferenceModel()
    model = model.to(device)
    
    # 启用混合精度训练以节省内存
    scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None
    
    # 创建数据集
    dataset = ImprovedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    print(f"有效数据集大小: {len(dataset)}")
    
    # 划分数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 加载纯密码子权重（不考虑基因组分布）
    try:
        with open('pure_codon_weights.json', 'r') as f:
            weight_info = json.load(f)

        print("使用纯密码子权重（不考虑基因组分布频率）")
        print(f"权重策略: {weight_info.get('strategy', 'mixed')}")

        # 使用混合权重策略
        codon_weights = weight_info['mixed_weights']
        weight_tensor = torch.ones(len(model.codon_to_idx))

        for codon, weight in codon_weights.items():
            if codon in model.codon_to_idx:
                idx = model.codon_to_idx[codon]
                weight_tensor[idx] = weight

        # 归一化权重
        weight_tensor = weight_tensor / weight_tensor.mean()
        weight_tensor = weight_tensor.to(device)

        print(f"纯密码子权重统计: min={weight_tensor.min():.3f}, max={weight_tensor.max():.3f}, mean={weight_tensor.mean():.3f}")

        # 显示一些具体的权重
        print("部分密码子权重示例:")
        example_codons = ['ATG', 'CTG', 'TTT', 'TTC', 'GCT', 'GCC', 'AGA', 'AGG']
        for codon in example_codons:
            if codon in model.codon_to_idx:
                idx = model.codon_to_idx[codon]
                print(f"  {codon}: {weight_tensor[idx]:.3f}")

    except FileNotFoundError:
        print("未找到纯密码子权重文件，尝试使用改进权重文件")
        try:
            with open('improved_codon_weights.json', 'r') as f:
                weight_info = json.load(f)
            codon_weights = weight_info['mixed_weights']
            weight_tensor = torch.ones(len(model.codon_to_idx))

            for codon, weight in codon_weights.items():
                if codon in model.codon_to_idx:
                    idx = model.codon_to_idx[codon]
                    weight_tensor[idx] = weight

            weight_tensor = weight_tensor / weight_tensor.mean()
            weight_tensor = weight_tensor.to(device)
            print("使用改进权重文件")

        except FileNotFoundError:
            print("未找到任何权重文件，使用均匀权重")
            weight_tensor = None
    
    # 大批次设置
    target_batch_size = 500
    physical_batch_size = 32  # 物理批次大小（受GPU内存限制）
    accumulation_steps = target_batch_size // physical_batch_size
    
    print(f"目标批次大小: {target_batch_size}")
    print(f"物理批次大小: {physical_batch_size}")
    print(f"梯度累积步数: {accumulation_steps}")
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=physical_batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=physical_batch_size, shuffle=False, collate_fn=collate_fn)
    
    # 损失函数和优化器
    criterion = LargeBatchLoss(class_weights=weight_tensor)
    
    # 大批次训练需要调整学习率
    base_lr = 2e-5
    scaled_lr = base_lr * np.sqrt(target_batch_size / 32)  # 根据批次大小缩放学习率

    optimizer = optim.AdamW(model.parameters(), lr=scaled_lr, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=2000)  # 2000轮的余弦退火
    
    print(f"基础学习率: {base_lr:.2e}")
    print(f"缩放学习率: {scaled_lr:.2e}")
    
    # 训练历史
    train_losses = []
    val_losses = []
    train_accuracies = []
    val_accuracies = []
    
    best_accuracy = 0.0
    patience = 200  # 增加耐心值，适应2000轮训练
    patience_counter = 0
    
    for epoch in range(2000):  # 2000轮训练
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        num_batches = 0
        
        optimizer.zero_grad()
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/2000 [Train]')
        for batch_idx, batch in enumerate(train_pbar):
            try:
                # 梯度累积
                loss, acc = gradient_accumulation_step(
                    model, criterion, optimizer, batch, accumulation_steps, device, scaler
                )
                
                train_loss += loss
                train_acc += acc
                
                # 每accumulation_steps步更新一次参数
                if (batch_idx + 1) % accumulation_steps == 0:
                    if scaler is not None:
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        optimizer.step()
                    
                    optimizer.zero_grad()
                    num_batches += 1
                    
                    # 清理GPU内存
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                
                train_pbar.set_postfix({
                    'Loss': f'{loss:.3f}',
                    'Acc': f'{acc:.3f}',
                    'LR': f'{scheduler.get_last_lr()[0]:.2e}',
                    'Batch': f'{num_batches}'
                })
                
            except Exception as e:
                print(f"训练批次 {batch_idx} 异常: {e}")
                continue
        
        # 处理剩余的梯度累积
        if len(train_loader) % accumulation_steps != 0:
            if scaler is not None:
                scaler.step(optimizer)
                scaler.update()
            else:
                optimizer.step()
            optimizer.zero_grad()
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/2000 [Val]')
            for batch in val_pbar:
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)

                    if scaler is not None:
                        with torch.cuda.amp.autocast():
                            logits = model(proteins)
                    else:
                        logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    loss = criterion(logits, targets)
                    acc = calculate_accuracy(logits, targets)
                    
                    val_loss += loss.item()
                    val_acc += acc
                    val_batches += 1
                    
                    val_pbar.set_postfix({
                        'Loss': f'{loss.item():.3f}',
                        'Acc': f'{acc:.3f}'
                    })
                    
                except Exception as e:
                    print(f"验证批次异常: {e}")
                    continue
        
        # 计算平均值
        if num_batches > 0:
            avg_train_loss = train_loss / len(train_loader)
            avg_train_acc = train_acc / len(train_loader)
        else:
            avg_train_loss = float('inf')
            avg_train_acc = 0.0
            
        if val_batches > 0:
            avg_val_loss = val_loss / val_batches
            avg_val_acc = val_acc / val_batches
        else:
            avg_val_loss = float('inf')
            avg_val_acc = 0.0
        
        # 记录历史
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        train_accuracies.append(avg_train_acc)
        val_accuracies.append(avg_val_acc)
        
        print(f'Epoch {epoch+1}/2000:')
        print(f'  Train - Loss: {avg_train_loss:.4f}, Acc: {avg_train_acc:.4f} ({avg_train_acc*100:.1f}%)')
        print(f'  Val   - Loss: {avg_val_loss:.4f}, Acc: {avg_val_acc:.4f} ({avg_val_acc*100:.1f}%)')
        print(f'  有效批次数: {num_batches}, 验证批次数: {val_batches}')

        # 每100轮显示详细信息
        if (epoch + 1) % 100 == 0:
            print(f"  🎯 第{epoch+1}轮里程碑: 验证准确度 {avg_val_acc*100:.1f}%")
            if avg_val_acc >= 0.8:
                print("  🎉 已达到80%目标准确度！")
            elif avg_val_acc >= 0.7:
                print("  🚀 准确度超过70%，继续冲刺80%！")
            elif avg_val_acc >= 0.6:
                print("  📈 准确度超过60%，进展良好！")
        
        # 保存最佳模型
        if avg_val_acc > best_accuracy:
            best_accuracy = avg_val_acc
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': avg_val_loss,
                'val_accuracy': avg_val_acc,
                'batch_size': target_batch_size,
                'learning_rate': scaled_lr
            }, 'large_batch_best_model.pth')
            print(f'  🎉 保存最佳模型 (准确度: {best_accuracy:.4f} = {best_accuracy*100:.1f}%)')
        else:
            patience_counter += 1
            print(f'  准确度未改善 ({patience_counter}/{patience})')
        
        # 早停检查
        if patience_counter >= patience:
            print(f'  早停：准确度连续{patience}轮未改善')
            break
        
        scheduler.step()
        
        # 内存清理
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        print()
    
    # 绘制训练曲线
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 3, 2)
    plt.plot([acc * 100 for acc in train_accuracies], label='Train Accuracy')
    plt.plot([acc * 100 for acc in val_accuracies], label='Val Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.title('Training and Validation Accuracy')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 3, 3)
    plt.plot([acc * 100 for acc in val_accuracies], 'b-', linewidth=3, label='Val Accuracy')
    plt.axhline(y=80, color='r', linestyle='--', linewidth=2, label='Target 80%')
    plt.axhline(y=best_accuracy*100, color='g', linestyle=':', linewidth=2, label=f'Best {best_accuracy*100:.1f}%')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.title(f'Large Batch Training (BS={target_batch_size})')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('large_batch_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("大批次训练完成！")
    print(f"最佳验证准确度: {best_accuracy:.4f} ({best_accuracy*100:.1f}%)")
    print(f"使用批次大小: {target_batch_size}")
    
    return best_accuracy

if __name__ == "__main__":
    best_acc = train_large_batch_model()
    
    if best_acc >= 0.8:
        print("🎉🎉🎉 恭喜！大批次训练成功达到80%目标准确度！")
    elif best_acc >= 0.7:
        print(f"大批次训练效果很好！准确度达到 {best_acc*100:.1f}%")
        print("建议：")
        print("1. 进一步增加批次大小到1000")
        print("2. 延长训练时间")
        print("3. 微调学习率")
    else:
        print(f"大批次训练准确度为 {best_acc*100:.1f}%")
        print("可能的改进方向：")
        print("1. 检查梯度累积是否正确")
        print("2. 调整学习率缩放策略")
        print("3. 尝试不同的优化器设置")
