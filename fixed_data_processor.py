#!/usr/bin/env python3
"""
Fixed Data Processor
Handles stop codons and other data processing issues correctly
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import json
import warnings
warnings.filterwarnings('ignore')

class FixedCodonDataset(Dataset):
    """Fixed dataset that properly handles stop codons and data issues"""
    
    def __init__(self, proteins, nucleotides, codon_to_idx, max_length=512):
        self.proteins = proteins
        self.nucleotides = nucleotides
        self.codon_to_idx = codon_to_idx
        self.max_length = max_length
        
        # Build genetic code (excluding stop codons for training)
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        # Stop codons (not included in training)
        self.stop_codons = {'TAA', 'TAG', 'TGA'}
        
        self.valid_pairs = []
        self._process_data()
    
    def _process_data(self):
        """Process data with proper stop codon handling"""
        print(f"Processing {len(self.proteins)} protein-nucleotide pairs...")
        
        valid_count = 0
        error_count = 0
        stop_codon_count = 0
        
        for idx, (protein, nucleotide) in enumerate(zip(self.proteins, self.nucleotides)):
            try:
                # Basic validation
                if not protein or not nucleotide:
                    error_count += 1
                    continue
                
                # Length validation
                if len(protein) > self.max_length:
                    continue
                
                # Handle stop codons - nucleotide might be longer due to stop codon
                expected_length = len(protein) * 3
                actual_length = len(nucleotide)
                
                # Check if there's a stop codon at the end
                has_stop_codon = False
                if actual_length == expected_length + 3:
                    stop_codon = nucleotide[-3:].upper()
                    if stop_codon in self.stop_codons:
                        has_stop_codon = True
                        nucleotide = nucleotide[:-3]  # Remove stop codon
                        stop_codon_count += 1
                
                # Now check if lengths match
                if len(protein) * 3 != len(nucleotide):
                    error_count += 1
                    continue
                
                # Extract and validate codons
                codons = []
                valid_sequence = True
                
                for i in range(0, len(nucleotide), 3):
                    codon = nucleotide[i:i+3].upper()
                    
                    # Check if codon is valid
                    if len(codon) != 3:
                        valid_sequence = False
                        break
                    
                    if codon not in self.genetic_code:
                        valid_sequence = False
                        break
                    
                    # Check if amino acid matches
                    expected_aa = self.genetic_code[codon]
                    actual_aa = protein[i//3] if i//3 < len(protein) else None
                    
                    if actual_aa != expected_aa:
                        valid_sequence = False
                        break
                    
                    # Check if codon is in vocabulary
                    if codon in self.codon_to_idx:
                        codons.append(self.codon_to_idx[codon])
                    else:
                        valid_sequence = False
                        break
                
                if valid_sequence and len(codons) == len(protein):
                    self.valid_pairs.append((protein, codons))
                    valid_count += 1
                else:
                    error_count += 1
                
            except Exception as e:
                error_count += 1
                continue
            
            if idx % 500 == 0:
                print(f"  Progress: {idx}/{len(self.proteins)}, Valid: {valid_count}, Errors: {error_count}, Stop codons: {stop_codon_count}")
        
        print(f"Data processing complete:")
        print(f"  Valid pairs: {valid_count}")
        print(f"  Errors: {error_count}")
        print(f"  Stop codons handled: {stop_codon_count}")
        print(f"  Success rate: {valid_count/(valid_count+error_count)*100:.1f}%")
    
    def __len__(self):
        return len(self.valid_pairs)
    
    def __getitem__(self, idx):
        protein, codons = self.valid_pairs[idx]
        return {
            'protein': protein,
            'codons': torch.tensor(codons, dtype=torch.long)
        }

def fixed_collate_fn(batch):
    """Fixed collate function with error handling"""
    try:
        proteins = [item['protein'] for item in batch]
        codons = [item['codons'] for item in batch]
        
        if not codons:
            return None
        
        # Find max length
        max_len = max(len(codon_seq) for codon_seq in codons)
        
        # Pad sequences
        padded_codons = []
        for codon_seq in codons:
            padded = torch.full((max_len,), -100, dtype=torch.long)
            padded[:len(codon_seq)] = codon_seq
            padded_codons.append(padded)
        
        return {
            'proteins': proteins,
            'codons': torch.stack(padded_codons)
        }
    
    except Exception as e:
        print(f"Collate function error: {e}")
        return None

def test_fixed_processing():
    """Test the fixed data processing"""
    print("🔧 Testing Fixed Data Processing")
    print("=" * 50)
    
    # Load data
    df = pd.read_csv("processed_BL21_data.csv")
    print(f"Loaded {len(df)} rows from CSV")
    
    # Filter by length
    df_filtered = df[df['protein_length'] <= 512]
    print(f"After length filtering: {len(df_filtered)} rows")
    
    proteins = df_filtered['protein_sequence'].tolist()
    nucleotides = df_filtered['nucleotide_sequence'].tolist()
    
    # Build codon vocabulary (excluding stop codons)
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    codons = [codon for codon, aa in genetic_code.items() if aa != '*']
    codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    print(f"Codon vocabulary size: {len(codon_to_idx)}")
    
    # Test dataset creation
    dataset = FixedCodonDataset(proteins, nucleotides, codon_to_idx)
    
    print(f"\nDataset Results:")
    print(f"  Valid samples: {len(dataset)}")
    print(f"  Success rate: {len(dataset)/len(proteins)*100:.1f}%")
    
    if len(dataset) > 0:
        # Test a few samples
        print(f"\nSample validation:")
        for i in range(min(3, len(dataset))):
            sample = dataset[i]
            protein = sample['protein']
            codons = sample['codons']
            print(f"  Sample {i}: Protein length={len(protein)}, Codons length={len(codons)}")
        
        # Test collate function
        loader = DataLoader(dataset, batch_size=4, shuffle=False, collate_fn=fixed_collate_fn)
        
        try:
            batch = next(iter(loader))
            if batch is not None:
                print(f"\nBatch test successful:")
                print(f"  Batch size: {len(batch['proteins'])}")
                print(f"  Codon tensor shape: {batch['codons'].shape}")
                print("✅ Fixed data processing is working correctly!")
                return True, dataset
            else:
                print("❌ Batch creation failed")
                return False, None
        except Exception as e:
            print(f"❌ DataLoader test failed: {e}")
            return False, None
    else:
        print("❌ No valid samples found")
        return False, None

def create_xgboost_features_fixed():
    """Create features for XGBoost with fixed processing"""
    print("\n🔬 Creating Fixed XGBoost Features")
    print("=" * 40)
    
    # Load data
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 256]  # Smaller for XGBoost
    
    print(f"Processing {len(df)} samples for XGBoost...")
    
    # Amino acid properties
    aa_properties = {
        'A': [0.31, 0.42, 0.00, 0.00, 0.00],
        'R': [-1.01, 0.00, 1.00, 0.00, 0.00],
        'N': [-0.60, 1.00, 0.00, 0.00, 0.00],
        'D': [-0.77, 1.00, 0.00, 1.00, 0.00],
        'C': [1.54, 0.00, 0.00, 0.00, 0.00],
        'Q': [-0.22, 1.00, 0.00, 0.00, 0.00],
        'E': [-0.64, 1.00, 0.00, 1.00, 0.00],
        'G': [0.00, 0.00, 0.00, 0.00, 0.00],
        'H': [0.13, 0.50, 0.50, 0.00, 1.00],
        'I': [1.80, 0.00, 0.00, 0.00, 0.00],
        'L': [1.70, 0.00, 0.00, 0.00, 0.00],
        'K': [-0.99, 0.00, 1.00, 0.00, 0.00],
        'M': [1.23, 0.00, 0.00, 0.00, 0.00],
        'F': [1.79, 0.00, 0.00, 0.00, 1.00],
        'P': [0.72, 0.00, 0.00, 0.00, 0.00],
        'S': [-0.04, 1.00, 0.00, 0.00, 0.00],
        'T': [0.26, 1.00, 0.00, 0.00, 0.00],
        'W': [2.25, 0.00, 0.00, 0.00, 1.00],
        'Y': [1.61, 1.00, 0.00, 0.00, 1.00],
        'V': [1.22, 0.00, 0.00, 0.00, 0.00]
    }
    
    # Genetic code
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    codons = [codon for codon, aa in genetic_code.items() if aa != '*']
    codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
    stop_codons = {'TAA', 'TAG', 'TGA'}
    
    X = []
    y = []
    
    for idx, row in df.iterrows():
        protein_seq = row['protein_sequence']
        nucleotide_seq = row['nucleotide_sequence']
        
        # Handle stop codons
        expected_length = len(protein_seq) * 3
        if len(nucleotide_seq) == expected_length + 3:
            stop_codon = nucleotide_seq[-3:].upper()
            if stop_codon in stop_codons:
                nucleotide_seq = nucleotide_seq[:-3]
        
        if len(protein_seq) * 3 != len(nucleotide_seq):
            continue
        
        # Extract features for each position
        for pos in range(len(protein_seq)):
            codon_start = pos * 3
            codon = nucleotide_seq[codon_start:codon_start + 3].upper()
            
            if codon not in codon_to_idx:
                continue
            
            # Verify amino acid matches
            expected_aa = genetic_code.get(codon)
            actual_aa = protein_seq[pos]
            if expected_aa != actual_aa:
                continue
            
            # Current amino acid features
            features = aa_properties.get(actual_aa, [0, 0, 0, 0, 0]).copy()
            
            # Position features
            features.append(pos / len(protein_seq))  # Relative position
            features.append(len(protein_seq))  # Sequence length
            
            # Context features (previous and next amino acids)
            for offset in [-1, 1]:
                context_pos = pos + offset
                if 0 <= context_pos < len(protein_seq):
                    context_aa = protein_seq[context_pos]
                    features.extend(aa_properties.get(context_aa, [0, 0, 0, 0, 0]))
                else:
                    features.extend([0, 0, 0, 0, 0])  # Padding
            
            X.append(features)
            y.append(codon_to_idx[codon])
        
        if idx % 100 == 0:
            print(f"  Progress: {idx}/{len(df)}, Features: {len(X)}")
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"Fixed XGBoost feature extraction complete:")
    print(f"  Feature matrix shape: {X.shape}")
    print(f"  Labels: {len(y)}")
    print(f"  Classes: {len(np.unique(y))}")
    
    if len(X) > 0:
        # Save features
        np.save('xgboost_features_fixed.npy', X)
        np.save('xgboost_labels_fixed.npy', y)
        
        with open('xgboost_codon_mapping_fixed.json', 'w') as f:
            json.dump(codon_to_idx, f, indent=2)
        
        print("✅ Fixed XGBoost features saved successfully!")
        return X, y, codon_to_idx
    else:
        print("❌ No features extracted")
        return None, None, None

if __name__ == "__main__":
    print("🔧 Fixed Data Processing Test")
    print("=" * 50)
    
    # Test fixed data processing
    success, dataset = test_fixed_processing()
    
    if success:
        print(f"\n✅ Fixed data processing successful!")
        print(f"Dataset size: {len(dataset)}")
        
        # Create XGBoost features
        X, y, codon_mapping = create_xgboost_features_fixed()
        
        if X is not None and len(X) > 0:
            print("\n✅ All fixed data processing tests passed!")
            print("Ready for improved training and XGBoost comparison")
        else:
            print("\n❌ XGBoost feature creation failed")
    else:
        print("\n❌ Fixed data processing test failed")
