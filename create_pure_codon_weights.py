#!/usr/bin/env python3
"""
创建纯密码子权重
只考虑密码子使用频率，不考虑基因组分布概率
"""

import json
import numpy as np
import pandas as pd
from collections import defaultdict

def analyze_pure_codon_usage(data_file="processed_BL21_data.csv"):
    """分析纯密码子使用频率"""
    print("分析纯密码子使用频率（不考虑基因组分布）...")
    
    df = pd.read_csv(data_file)
    
    # 统计密码子使用频率
    codon_counts = defaultdict(int)
    aa_codon_counts = defaultdict(lambda: defaultdict(int))
    
    # 遗传密码表
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
        'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    for _, row in df.iterrows():
        dna_seq = row['nucleotide_sequence']
        
        # 提取密码子
        for i in range(0, len(dna_seq), 3):
            if i + 3 <= len(dna_seq):
                codon = dna_seq[i:i+3]
                if codon in genetic_code and genetic_code[codon] != '*':
                    aa = genetic_code[codon]
                    codon_counts[codon] += 1
                    aa_codon_counts[aa][codon] += 1
    
    return codon_counts, aa_codon_counts, genetic_code

def create_pure_codon_weights(codon_counts, aa_codon_counts):
    """创建纯密码子权重策略"""
    print("创建纯密码子权重策略...")
    
    # 策略1: 全局逆频率权重
    total_count = sum(codon_counts.values())
    global_weights = {}
    
    for codon, count in codon_counts.items():
        if count > 0:
            # 简单的逆频率
            freq = count / total_count
            weight = 1.0 / freq
            global_weights[codon] = weight
    
    # 归一化到合理范围
    if global_weights:
        weights_array = np.array(list(global_weights.values()))
        median_weight = np.median(weights_array)
        
        for codon in global_weights:
            weight = global_weights[codon]
            # 使用对数缩放减少极端值
            weight = np.log(weight / median_weight + 1) + 1
            # 限制范围 [0.5, 3.0]
            weight = max(0.5, min(3.0, weight))
            global_weights[codon] = weight
    
    # 策略2: 氨基酸内部平衡权重
    aa_balanced_weights = {}
    
    for aa, codons in aa_codon_counts.items():
        total_aa_count = sum(codons.values())
        if total_aa_count == 0:
            continue
        
        # 计算该氨基酸内部的权重
        for codon, count in codons.items():
            if count > 0:
                freq = count / total_aa_count
                # 氨基酸内部的逆频率权重
                weight = 1.0 / freq
                aa_balanced_weights[codon] = weight
    
    # 归一化氨基酸内部权重
    if aa_balanced_weights:
        weights_array = np.array(list(aa_balanced_weights.values()))
        mean_weight = np.mean(weights_array)
        
        for codon in aa_balanced_weights:
            weight = aa_balanced_weights[codon] / mean_weight
            # 限制范围 [0.3, 3.0]
            weight = max(0.3, min(3.0, weight))
            aa_balanced_weights[codon] = weight
    
    # 策略3: 混合权重（全局30% + 氨基酸内部70%）
    mixed_weights = {}
    
    for codon in codon_counts.keys():
        global_w = global_weights.get(codon, 1.0)
        aa_w = aa_balanced_weights.get(codon, 1.0)
        mixed_weights[codon] = 0.3 * global_w + 0.7 * aa_w
    
    return {
        'global_weights': global_weights,
        'aa_balanced_weights': aa_balanced_weights,
        'mixed_weights': mixed_weights
    }

def main():
    """主函数"""
    print("创建纯密码子权重文件（不考虑基因组分布）...")
    
    # 分析数据
    codon_counts, aa_codon_counts, genetic_code = analyze_pure_codon_usage()
    
    print(f"发现 {len(codon_counts)} 种密码子")
    print(f"总密码子数: {sum(codon_counts.values())}")
    
    # 创建权重策略
    weight_strategies = create_pure_codon_weights(codon_counts, aa_codon_counts)
    
    # 氨基酸到密码子的映射
    aa_to_codons = defaultdict(list)
    for codon, aa in genetic_code.items():
        if aa != '*':
            aa_to_codons[aa].append(codon)
    
    # 创建完整的权重信息
    weight_info = {
        'codon_counts': dict(codon_counts),
        'aa_codon_counts': {aa: dict(codons) for aa, codons in aa_codon_counts.items()},
        'aa_to_codons': dict(aa_to_codons),
        
        # 不同的纯密码子权重策略
        'global_weights': weight_strategies['global_weights'],
        'aa_balanced_weights': weight_strategies['aa_balanced_weights'],
        'mixed_weights': weight_strategies['mixed_weights'],
        
        # 默认使用混合权重
        'codon_weights': weight_strategies['mixed_weights'],
        
        # 说明
        'description': '纯密码子权重，不考虑基因组分布概率',
        'strategy': 'mixed: 30% global + 70% aa_balanced'
    }
    
    # 保存到文件
    with open('pure_codon_weights.json', 'w') as f:
        json.dump(weight_info, f, indent=2)
    
    print("纯密码子权重文件已创建: pure_codon_weights.json")
    
    # 显示权重统计
    for strategy_name, weights in [
        ('全局权重', weight_strategies['global_weights']),
        ('氨基酸平衡权重', weight_strategies['aa_balanced_weights']),
        ('混合权重', weight_strategies['mixed_weights'])
    ]:
        if weights:
            weights_array = np.array(list(weights.values()))
            print(f"\n{strategy_name}统计:")
            print(f"  范围: {weights_array.min():.3f} - {weights_array.max():.3f}")
            print(f"  均值: {weights_array.mean():.3f}")
            print(f"  标准差: {weights_array.std():.3f}")
            
            # 显示最高和最低权重的密码子
            sorted_weights = sorted(weights.items(), key=lambda x: x[1])
            print(f"  最低权重: {sorted_weights[0][0]}={sorted_weights[0][1]:.3f}")
            print(f"  最高权重: {sorted_weights[-1][0]}={sorted_weights[-1][1]:.3f}")
    
    # 显示一些具体例子
    print(f"\n密码子权重示例（使用混合策略）:")
    example_codons = ['ATG', 'CTG', 'TTT', 'TTC', 'GCT', 'GCC', 'AGA', 'AGG']
    for codon in example_codons:
        if codon in weight_strategies['mixed_weights']:
            weight = weight_strategies['mixed_weights'][codon]
            aa = genetic_code.get(codon, '?')
            count = codon_counts.get(codon, 0)
            print(f"  {codon} ({aa}): 权重={weight:.3f}, 计数={count}")

if __name__ == "__main__":
    main()
