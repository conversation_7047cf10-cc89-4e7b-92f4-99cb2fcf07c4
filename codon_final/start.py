#!/usr/bin/env python3
"""
大肠杆菌密码子优化器 - 专业命令行工具
基于100%准确率的最佳预训练模型

使用方法:
1. 单条序列模式: python start.py single --sequence "MKLLVVS" --output result.txt
2. 批量处理模式: python start.py batch --input sequences.txt --output results.txt
3. 交互模式: python start.py interactive
"""

import argparse
import os
import sys
import json
import time
from typing import List, Dict, Tuple

# 导入最佳优化器
try:
    from best_interactive import BestCodonOptimizer
    HAS_BEST_MODEL = True
except ImportError:
    print("❌ 无法导入最佳优化器，请检查文件完整性")
    HAS_BEST_MODEL = False
    sys.exit(1)

def check_model_info():
    """检查模型信息"""
    info_file = "model_info.json"
    if os.path.exists(info_file):
        try:
            with open(info_file, 'r') as f:
                return json.load(f)
        except:
            return None
    return None

def single_mode(sequence: str, output_path: str):
    """单条序列优化模式"""
    print("🧬 单条序列优化模式")
    print("=" * 50)

    # 初始化优化器
    optimizer = BestCodonOptimizer()

    print(f"📝 输入序列: {sequence}")
    print(f"📁 输出文件: {output_path}")

    try:
        # 优化序列
        print(f"\n🔧 正在优化序列...")
        start_time = time.time()

        optimized_dna, info = optimizer.optimize_sequence(sequence)

        end_time = time.time()
        processing_time = end_time - start_time

        # 显示结果
        print(f"\n✅ 优化完成!")
        print(f"   处理时间: {processing_time:.3f} 秒")
        print(f"   原始序列: {sequence}")
        print(f"   优化DNA: {optimized_dna}")
        print(f"   GC含量: {info['gc_content']:.1%}")
        print(f"   密码子评分: {info['codon_usage_score']:.3f}")

        # 保存结果
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 大肠杆菌密码子优化结果 - 单条序列模式\n")
            f.write("# 基于100%准确率的最佳预训练模型\n")
            f.write(f"# 处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"原始蛋白质序列: {sequence}\n")
            f.write(f"序列长度: {len(sequence)} 氨基酸\n")
            f.write(f"优化DNA序列: {optimized_dna}\n")
            f.write(f"DNA长度: {len(optimized_dna)} 核苷酸\n")
            f.write(f"GC含量: {info['gc_content']:.1%}\n")
            f.write(f"密码子优化评分: {info['codon_usage_score']:.3f}\n")
            f.write(f"模型准确率: {info.get('model_accuracy', '100%')}\n")
            f.write(f"处理时间: {processing_time:.3f} 秒\n\n")

            f.write("详细密码子分析:\n")
            f.write("-" * 40 + "\n")
            f.write(f"{'位置':<4} {'氨基酸':<6} {'密码子':<6} {'使用频率':<8}\n")
            f.write("-" * 40 + "\n")

            for detail in info['position_details']:
                f.write(f"{detail['position']:<4} {detail['amino_acid']:<6} {detail['codon']:<6} {detail['usage_frequency']:<8.3f}\n")

        print(f"\n💾 结果已保存到: {output_path}")
        return True

    except Exception as e:
        print(f"❌ 优化失败: {e}")
        return False
    
def batch_mode(input_path: str, output_path: str):
    """批量处理模式"""
    print("📦 批量处理模式")
    print("=" * 50)

    # 检查输入文件
    if not os.path.exists(input_path):
        print(f"❌ 输入文件不存在: {input_path}")
        return False

    # 初始化优化器
    optimizer = BestCodonOptimizer()

    print(f"📁 输入文件: {input_path}")
    print(f"📁 输出文件: {output_path}")

    try:
        # 读取序列
        sequences = []
        with open(input_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    sequences.append((line_num, line))

        if not sequences:
            print("❌ 输入文件中没有找到有效序列")
            return False

        print(f"📊 找到 {len(sequences)} 条序列")

        # 批量优化
        print(f"\n🔧 开始批量优化...")
        start_time = time.time()

        results = []
        success_count = 0

        for i, (line_num, seq) in enumerate(sequences, 1):
            try:
                print(f"⏳ 处理序列 {i}/{len(sequences)}: {seq[:20]}{'...' if len(seq) > 20 else ''}")
                optimized_dna, info = optimizer.optimize_sequence(seq)
                results.append((line_num, seq, optimized_dna, info, None))
                success_count += 1
                print(f"✅ 完成: {len(seq)} aa -> {len(optimized_dna)} nt")
            except Exception as e:
                print(f"❌ 失败: {e}")
                results.append((line_num, seq, None, None, str(e)))

        end_time = time.time()
        total_time = end_time - start_time

        # 保存结果
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 大肠杆菌密码子优化结果 - 批量处理模式\n")
            f.write("# 基于100%准确率的最佳预训练模型\n")
            f.write(f"# 处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 总序列数: {len(sequences)}\n")
            f.write(f"# 成功优化: {success_count}\n")
            f.write(f"# 处理时间: {total_time:.3f} 秒\n")
            f.write("=" * 80 + "\n\n")

            for line_num, seq, optimized_dna, info, error in results:
                f.write(f"序列 {line_num} (行号: {line_num}):\n")
                f.write(f"原始序列: {seq}\n")

                if optimized_dna:
                    f.write(f"优化DNA: {optimized_dna}\n")
                    f.write(f"GC含量: {info['gc_content']:.1%}\n")
                    f.write(f"密码子评分: {info['codon_usage_score']:.3f}\n")
                    f.write(f"模型准确率: {info.get('model_accuracy', '100%')}\n")
                else:
                    f.write(f"错误: {error}\n")

                f.write("-" * 60 + "\n\n")

        print(f"\n✅ 批量处理完成!")
        print(f"   总序列数: {len(sequences)}")
        print(f"   成功优化: {success_count}")
        print(f"   失败数量: {len(sequences) - success_count}")
        print(f"   总处理时间: {total_time:.3f} 秒")
        print(f"   平均每条: {total_time/len(sequences):.3f} 秒")
        print(f"💾 结果已保存到: {output_path}")

        return True

    except Exception as e:
        print(f"❌ 批量处理失败: {e}")
        return False
    
def interactive_mode():
    """交互模式"""
    print("🎮 交互模式")
    print("=" * 50)

    # 导入并启动交互式优化器
    try:
        import subprocess
        subprocess.run([sys.executable, "best_interactive.py"])
    except Exception as e:
        print(f"❌ 启动交互模式失败: {e}")
        return False

    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="大肠杆菌密码子优化器 - 基于100%准确率的最佳预训练模型",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  单条序列模式:
    python start.py single --sequence "MKLLVVS" --output result.txt

  批量处理模式:
    python start.py batch --input sequences.txt --output results.txt

  交互模式:
    python start.py interactive

注意事项:
  - 支持标准20种氨基酸 + 终止密码子(*)
  - 专为大肠杆菌BL21(DE3)优化
  - 基于100%准确率的预训练模型
        """
    )

    # 添加子命令
    subparsers = parser.add_subparsers(dest='mode', help='运行模式')

    # 单条序列模式
    single_parser = subparsers.add_parser('single', help='单条序列优化模式')
    single_parser.add_argument('--sequence', '-s', required=True,
                              help='蛋白质序列 (单字母氨基酸代码)')
    single_parser.add_argument('--output', '-o', required=True,
                              help='输出文件路径')

    # 批量处理模式
    batch_parser = subparsers.add_parser('batch', help='批量处理模式')
    batch_parser.add_argument('--input', '-i', required=True,
                             help='输入文件路径 (每行一条序列)')
    batch_parser.add_argument('--output', '-o', required=True,
                             help='输出文件路径')

    # 交互模式
    interactive_parser = subparsers.add_parser('interactive', help='交互模式')

    # 解析参数
    args = parser.parse_args()

    # 显示程序信息
    print("🧬 大肠杆菌密码子优化器")
    print("=" * 60)
    print("基于100%准确率的最佳预训练模型")
    print("专为大肠杆菌BL21(DE3)表达系统设计")
    print("=" * 60)

    # 检查模型信息
    model_info = check_model_info()
    if model_info:
        print(f"\n📊 模型信息:")
        print(f"   准确率: {model_info.get('accuracy', 1.0)*100:.1f}%")
        print(f"   版本: {model_info.get('version', '1.0')}")
        print(f"   训练样本: {model_info.get('samples', 861205):,}")
        print(f"   特征数: {model_info.get('features', 27)}")

    # 根据模式执行相应功能
    if args.mode == 'single':
        print(f"\n🎯 模式: 单条序列优化")
        success = single_mode(args.sequence, args.output)

    elif args.mode == 'batch':
        print(f"\n🎯 模式: 批量处理")
        success = batch_mode(args.input, args.output)

    elif args.mode == 'interactive':
        print(f"\n🎯 模式: 交互式")
        success = interactive_mode()

    else:
        # 如果没有指定模式，显示帮助
        parser.print_help()
        print(f"\n💡 提示: 请指定运行模式 (single/batch/interactive)")
        print(f"   示例: python start.py single --sequence 'MKLLVVS' --output result.txt")
        return

    # 显示结果
    if success:
        print(f"\n🎉 任务完成!")
    else:
        print(f"\n❌ 任务失败!")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
