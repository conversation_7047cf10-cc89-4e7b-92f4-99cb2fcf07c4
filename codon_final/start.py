#!/usr/bin/env python3
"""
大肠杆菌密码子优化器 - 一键启动脚本
自动选择最佳版本并启动
"""

import os
import sys
import subprocess
import json

def check_best_model():
    """检查最佳预训练模型"""
    model_file = "best_codon_model.pkl"
    info_file = "model_info.json"

    if os.path.exists(model_file) and os.path.exists(info_file):
        try:
            with open(info_file, 'r') as f:
                info = json.load(f)
            return True, info
        except:
            return False, None
    return False, None

def check_dependencies():
    """检查依赖包"""
    try:
        import xgboost
        import sklearn
        return True
    except (ImportError, ValueError, Exception):
        # 捕获所有可能的导入错误，包括版本兼容性问题
        return False

def main():
    """主启动函数"""
    print("🧬 大肠杆菌密码子优化器")
    print("=" * 50)
    print("基于100%准确率AI模型的专业密码子优化工具")
    print("=" * 50)

    # 检查最佳预训练模型
    has_best_model, model_info = check_best_model()
    has_full_deps = check_dependencies()

    print(f"\n📦 环境检查:")
    if has_best_model:
        print("🏆 最佳预训练模型: 已就绪")
        print(f"   准确率: {model_info.get('accuracy', 1.0)*100:.1f}%")
        print(f"   版本: {model_info.get('version', '1.0')}")
        print(f"   大小: {model_info.get('file_size_mb', 0)} MB")
        print("✅ 简化版依赖: 已安装 (Python标准库)")
        print("\n🎯 推荐使用最佳预训练模型 (100%准确率)")
    elif has_full_deps:
        print("✅ 完整版依赖: 已安装 (XGBoost, sklearn)")
        print("✅ 简化版依赖: 已安装 (Python标准库)")
        print("\n🎯 推荐使用完整版 (100%准确率)")
    else:
        print("❌ 完整版依赖: 未安装 (XGBoost, sklearn)")
        print("✅ 简化版依赖: 已安装 (Python标准库)")
        print("\n🎯 将使用简化版 (95%+准确率)")
    
    print(f"\n🚀 可用版本:")
    if has_best_model:
        print("1. 最佳预训练模型 (推荐) - 100%准确率")
        print("2. 简化版优化器 (备用) - 95%+准确率")
        print("3. 完整版优化器 (研究)" + (" - 需要安装依赖" if not has_full_deps else ""))
    else:
        print("1. 简化版优化器 (推荐新手)")
        print("2. 完整版优化器 (推荐研究)" + (" - 需要安装依赖" if not has_full_deps else ""))
    print("4. 批量处理示例")
    print("5. 查看帮助文档")
    print("0. 退出")
    
    while True:
        try:
            choice = input(f"\n请选择 (1-5, 0退出): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            
            elif choice == '1':
                if has_best_model:
                    print(f"\n🚀 启动最佳预训练模型...")
                    subprocess.run([sys.executable, "best_interactive.py"])
                else:
                    print(f"\n🚀 启动简化版优化器...")
                    subprocess.run([sys.executable, "simple_codon_optimizer.py", "--interactive"])

            elif choice == '2':
                if has_best_model:
                    print(f"\n🚀 启动简化版优化器...")
                    subprocess.run([sys.executable, "simple_codon_optimizer.py", "--interactive"])
                else:
                    if has_full_deps:
                        print(f"\n🚀 启动完整版优化器...")
                        subprocess.run([sys.executable, "easy_optimizer.py"])
                    else:
                        print(f"\n❌ 完整版需要安装依赖:")
                        print("pip install numpy pandas scikit-learn xgboost")
                        print("或者使用简化版 (选项1)")

            elif choice == '3':
                if has_best_model:
                    if has_full_deps:
                        print(f"\n🚀 启动完整版优化器...")
                        subprocess.run([sys.executable, "easy_optimizer.py"])
                    else:
                        print(f"\n❌ 完整版需要安装依赖:")
                        print("pip install numpy pandas scikit-learn xgboost")
                else:
                    print(f"\n🚀 启动交互式界面...")
                    if has_full_deps:
                        subprocess.run([sys.executable, "easy_optimizer.py"])
                    else:
                        subprocess.run([sys.executable, "simple_codon_optimizer.py", "--interactive"])
            
            elif choice == '4':
                print(f"\n🚀 批量处理示例...")
                print("使用示例文件进行批量优化:")
                if has_best_model:
                    # 使用最佳模型进行批量处理
                    print("使用最佳预训练模型...")
                    subprocess.run([sys.executable, "-c", """
from best_codon_optimizer import BestEcoliCodonOptimizer
optimizer = BestEcoliCodonOptimizer()
optimizer.load_model()
with open('example_sequences.txt', 'r') as f:
    sequences = [line.strip() for line in f if line.strip() and not line.startswith('#')]
results = optimizer.optimize_batch(sequences)
with open('best_model_results.txt', 'w') as f:
    f.write('# 最佳预训练模型批量优化结果\\n\\n')
    for i, (dna, info) in enumerate(results):
        if dna:
            f.write(f'序列 {i+1}: {sequences[i]}\\n')
            f.write(f'优化DNA: {dna}\\n')
            f.write(f'GC含量: {info[\"gc_content\"]:.1%}\\n')
            f.write(f'评分: {info[\"codon_usage_score\"]:.3f}\\n\\n')
print('✅ 结果已保存到 best_model_results.txt')
"""])
                else:
                    subprocess.run([sys.executable, "simple_codon_optimizer.py",
                                  "--input", "example_sequences.txt",
                                  "--output", "batch_results.txt"])
                    print("✅ 结果已保存到 batch_results.txt")
            
            elif choice == '5':
                print(f"\n📖 帮助文档:")
                print("- README.md: 详细使用说明")
                print("- FINAL_GUIDE.md: 完整部署指南")
                print("- 在线帮助: python simple_codon_optimizer.py --help")
                
                view_doc = input("是否查看快速指南? (y/n): ").strip().lower()
                if view_doc in ['y', 'yes']:
                    print(f"\n📖 快速使用指南:")
                    print("=" * 40)
                    print("1. 单条序列: python simple_codon_optimizer.py --sequence 'MKLLVVS'")
                    print("2. 批量处理: python simple_codon_optimizer.py --input sequences.txt")
                    print("3. 交互模式: python simple_codon_optimizer.py --interactive")
                    print("4. 查看选择: python simple_codon_optimizer.py --alternatives L")
                    print("=" * 40)
            
            else:
                print("⚠️ 无效选择，请输入 1-5 或 0")
        
        except KeyboardInterrupt:
            print(f"\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
