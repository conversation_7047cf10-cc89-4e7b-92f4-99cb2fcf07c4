#!/usr/bin/env python3
"""
增强版传统机器学习模型 - Enhanced Traditional ML
基于54.8%成功方法 + CodonTransformer核心特性
目标准确率: 80%+

核心设计：
1. 保留54.8%成功的XGBoost架构和特征工程
2. 融合CodonTransformer的AA_CODON合并序列
3. 添加大肠杆菌专用密码子使用偏好
4. 引入序列级别的上下文建模
5. GPU加速特征提取和训练
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report
import json
import time
import warnings
from tqdm import tqdm
import gc
warnings.filterwarnings('ignore')

class EnhancedFeatureExtractor:
    """增强特征提取器：融合传统特征和CodonTransformer特性"""
    
    def __init__(self):
        # 大肠杆菌密码子使用频率表（CodonTransformer特性）
        self.ecoli_codon_usage = {
            'TTT': 0.58, 'TTC': 0.42, 'TTA': 0.14, 'TTG': 0.13,
            'TCT': 0.17, 'TCC': 0.15, 'TCA': 0.14, 'TCG': 0.14,
            'TAT': 0.59, 'TAC': 0.41, 'TGT': 0.46, 'TGC': 0.54, 'TGG': 1.00,
            'CTT': 0.12, 'CTC': 0.10, 'CTA': 0.04, 'CTG': 0.47,
            'CCT': 0.18, 'CCC': 0.13, 'CCA': 0.20, 'CCG': 0.49,
            'CAT': 0.57, 'CAC': 0.43, 'CAA': 0.34, 'CAG': 0.66,
            'CGT': 0.36, 'CGC': 0.36, 'CGA': 0.07, 'CGG': 0.11,
            'ATT': 0.49, 'ATC': 0.39, 'ATA': 0.11, 'ATG': 1.00,
            'ACT': 0.19, 'ACC': 0.40, 'ACA': 0.17, 'ACG': 0.25,
            'AAT': 0.49, 'AAC': 0.51, 'AAA': 0.74, 'AAG': 0.26,
            'AGT': 0.16, 'AGC': 0.25, 'AGA': 0.07, 'AGG': 0.04,
            'GTT': 0.28, 'GTC': 0.20, 'GTA': 0.17, 'GTG': 0.35,
            'GCT': 0.18, 'GCC': 0.26, 'GCA': 0.23, 'GCG': 0.33,
            'GAT': 0.63, 'GAC': 0.37, 'GAA': 0.68, 'GAG': 0.32,
            'GGT': 0.35, 'GGC': 0.37, 'GGA': 0.13, 'GGG': 0.15,
            'TAA': 0.61, 'TAG': 0.09, 'TGA': 0.30
        }
        
        # 遗传密码表
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G',
            'TAA': '*', 'TAG': '*', 'TGA': '*'
        }
        
        # 氨基酸属性（基于54.8%成功方法的特征重要性）
        self.aa_properties = {
            'A': [1.8, 0, 0, 0, 0],   # 疏水性, 极性, 正电, 负电, 芳香性
            'R': [-4.5, 1, 1, 0, 0], 'N': [-3.5, 1, 0, 0, 0], 'D': [-3.5, 1, 0, 1, 0],
            'C': [2.5, 0, 0, 0, 0],  'Q': [-3.5, 1, 0, 0, 0], 'E': [-3.5, 1, 0, 1, 0],
            'G': [-0.4, 0, 0, 0, 0], 'H': [-3.2, 1, 1, 0, 1], 'I': [4.5, 0, 0, 0, 0],
            'L': [3.8, 0, 0, 0, 0],  'K': [-3.9, 1, 1, 0, 0], 'M': [1.9, 0, 0, 0, 0],
            'F': [2.8, 0, 0, 0, 1],  'P': [-1.6, 0, 0, 0, 0], 'S': [-0.8, 1, 0, 0, 0],
            'T': [-0.7, 1, 0, 0, 0], 'W': [-0.9, 0, 0, 0, 1], 'Y': [-1.3, 1, 0, 0, 1],
            'V': [4.2, 0, 0, 0, 0],  '*': [0, 0, 0, 0, 0]
        }
        
        # 创建密码子到索引的映射
        self.codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(self.genetic_code.keys()))}
        
        print("✅ 增强特征提取器初始化完成")
        print(f"   大肠杆菌密码子使用频率: {len(self.ecoli_codon_usage)}个")
        print(f"   氨基酸属性特征: {len(self.aa_properties)}个")
        print(f"   密码子词汇表: {len(self.codon_to_idx)}个")
    
    def extract_traditional_features(self, protein_seq, nucleotide_seq, position):
        """提取传统特征（基于54.8%成功方法）"""
        features = []
        
        # 当前氨基酸属性（最重要的特征）
        aa = protein_seq[position] if position < len(protein_seq) else '*'
        aa_props = self.aa_properties.get(aa, [0] * 5)
        features.extend(aa_props)  # 5个特征
        
        # 前一个氨基酸属性
        prev_aa = protein_seq[position - 1] if position > 0 else '*'
        prev_props = self.aa_properties.get(prev_aa, [0] * 5)
        features.extend(prev_props)  # 5个特征
        
        # 后一个氨基酸属性
        next_aa = protein_seq[position + 1] if position + 1 < len(protein_seq) else '*'
        next_props = self.aa_properties.get(next_aa, [0] * 5)
        features.extend(next_props)  # 5个特征
        
        # 位置特征
        rel_position = position / len(protein_seq)
        features.append(rel_position)  # 1个特征
        
        # 序列长度
        features.append(len(protein_seq) / 1000.0)  # 1个特征，归一化
        
        return features  # 总共17个传统特征
    
    def extract_codon_transformer_features(self, protein_seq, nucleotide_seq, position):
        """提取CodonTransformer特性"""
        features = []
        
        # 1. AA_CODON合并序列特征
        aa = protein_seq[position] if position < len(protein_seq) else '*'
        codon = nucleotide_seq[position*3:(position+1)*3] if position*3+3 <= len(nucleotide_seq) else 'NNN'
        
        # AA_CODON组合的独特性评分
        aa_codon_token = f"{aa}_{codon}"
        aa_codon_hash = hash(aa_codon_token) % 1000  # 简化的哈希特征
        features.append(aa_codon_hash / 1000.0)  # 1个特征
        
        # 2. 大肠杆菌密码子使用偏好
        ecoli_preference = self.ecoli_codon_usage.get(codon, 0.0)
        features.append(ecoli_preference)  # 1个特征
        
        # 3. 密码子稀有度（1 - 使用频率）
        codon_rarity = 1.0 - ecoli_preference
        features.append(codon_rarity)  # 1个特征
        
        # 4. GC含量特征（大肠杆菌专用）
        if len(codon) == 3:
            gc_content = (codon.count('G') + codon.count('C')) / 3.0
            features.append(gc_content)  # 1个特征
        else:
            features.append(0.0)
        
        # 5. 密码子位置特异性
        codon_position_bias = []
        for i, nucleotide in enumerate(codon):
            if nucleotide in 'ATCG':
                # 简化的位置偏好评分
                position_score = {'A': 0.25, 'T': 0.25, 'C': 0.25, 'G': 0.25}[nucleotide]
                codon_position_bias.append(position_score)
            else:
                codon_position_bias.append(0.0)
        features.extend(codon_position_bias)  # 3个特征
        
        return features  # 总共7个CodonTransformer特征
    
    def extract_sequence_context_features(self, protein_seq, nucleotide_seq, position):
        """提取序列级别上下文特征"""
        features = []
        
        # 1. 局部密码子使用模式（窗口大小=5）
        window_size = 5
        start_pos = max(0, position - window_size // 2)
        end_pos = min(len(protein_seq), position + window_size // 2 + 1)
        
        local_codons = []
        for pos in range(start_pos, end_pos):
            codon = nucleotide_seq[pos*3:(pos+1)*3] if pos*3+3 <= len(nucleotide_seq) else 'NNN'
            local_codons.append(codon)
        
        # 局部GC含量
        local_gc = 0.0
        valid_codons = 0
        for codon in local_codons:
            if len(codon) == 3:
                local_gc += (codon.count('G') + codon.count('C')) / 3.0
                valid_codons += 1
        
        if valid_codons > 0:
            local_gc /= valid_codons
        features.append(local_gc)  # 1个特征
        
        # 2. 局部密码子偏好评分
        local_preference = 0.0
        for codon in local_codons:
            local_preference += self.ecoli_codon_usage.get(codon, 0.0)
        
        if len(local_codons) > 0:
            local_preference /= len(local_codons)
        features.append(local_preference)  # 1个特征
        
        # 3. 氨基酸重复模式
        aa = protein_seq[position] if position < len(protein_seq) else '*'
        
        # 前后各3个位置的氨基酸重复计数
        repeat_count = 0
        for offset in range(-3, 4):
            if offset == 0:
                continue
            check_pos = position + offset
            if 0 <= check_pos < len(protein_seq):
                if protein_seq[check_pos] == aa:
                    repeat_count += 1
        
        features.append(repeat_count / 6.0)  # 1个特征，归一化
        
        return features  # 总共3个上下文特征
    
    def extract_all_features(self, protein_seq, nucleotide_seq, position):
        """提取所有特征"""
        # 传统特征（17个）
        traditional_features = self.extract_traditional_features(protein_seq, nucleotide_seq, position)
        
        # CodonTransformer特征（7个）
        codon_transformer_features = self.extract_codon_transformer_features(protein_seq, nucleotide_seq, position)
        
        # 序列上下文特征（3个）
        context_features = self.extract_sequence_context_features(protein_seq, nucleotide_seq, position)
        
        # 合并所有特征
        all_features = traditional_features + codon_transformer_features + context_features
        
        return all_features  # 总共27个特征


def create_enhanced_features(data_file='processed_BL21_data.csv'):
    """创建增强特征数据集"""
    print("🧬 创建增强特征数据集")
    print("融合传统特征 + CodonTransformer特性")
    print("=" * 60)
    
    # 加载数据
    print("📊 加载大肠杆菌数据...")
    df = pd.read_csv(data_file)
    print(f"原始样本数: {len(df)}")
    
    # 数据过滤
    df = df[df['protein_length'] <= 512]  # 限制序列长度
    df = df.dropna(subset=['protein_sequence', 'nucleotide_sequence'])
    print(f"过滤后样本数: {len(df)}")
    
    # 初始化特征提取器
    feature_extractor = EnhancedFeatureExtractor()
    
    # 提取特征
    print("\n🔧 提取增强特征...")
    X = []
    y = []
    
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="特征提取"):
        protein_seq = row['protein_sequence']
        nucleotide_seq = row['nucleotide_sequence']
        
        # 确保序列长度匹配
        min_len = min(len(protein_seq), len(nucleotide_seq) // 3)
        
        for position in range(min_len):
            # 提取特征
            features = feature_extractor.extract_all_features(protein_seq, nucleotide_seq, position)
            
            # 获取目标密码子
            codon = nucleotide_seq[position*3:(position+1)*3]
            if len(codon) == 3 and codon in feature_extractor.codon_to_idx:
                X.append(features)
                y.append(feature_extractor.codon_to_idx[codon])
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"\n✅ 特征提取完成!")
    print(f"   样本数: {len(X):,}")
    print(f"   特征数: {X.shape[1]}")
    print(f"   类别数: {len(np.unique(y))}")
    
    # 保存特征
    feature_data = {
        'X': X.tolist(),
        'y': y.tolist(),
        'feature_names': [
            # 传统特征 (17个)
            'AA_hydrophobicity', 'AA_polarity', 'AA_positive', 'AA_negative', 'AA_aromatic',
            'prev_hydrophobicity', 'prev_polarity', 'prev_positive', 'prev_negative', 'prev_aromatic',
            'next_hydrophobicity', 'next_polarity', 'next_positive', 'next_negative', 'next_aromatic',
            'rel_position', 'seq_length',
            # CodonTransformer特征 (7个)
            'aa_codon_hash', 'ecoli_preference', 'codon_rarity', 'gc_content',
            'codon_pos1_bias', 'codon_pos2_bias', 'codon_pos3_bias',
            # 上下文特征 (3个)
            'local_gc_content', 'local_preference', 'aa_repeat_pattern'
        ],
        'codon_mapping': feature_extractor.codon_to_idx
    }
    
    with open('enhanced_features.json', 'w') as f:
        json.dump(feature_data, f, indent=2)
    
    print(f"💾 特征已保存到: enhanced_features.json")
    
    return X, y, feature_extractor.codon_to_idx


def train_enhanced_xgboost(X=None, y=None, codon_mapping=None):
    """训练增强XGBoost模型"""
    print("\n🚀 增强XGBoost训练")
    print("目标: 从54.8%提升到80%+")
    print("=" * 50)

    # 加载或创建特征
    if X is None:
        try:
            print("📂 加载预处理特征...")
            with open('enhanced_features.json', 'r') as f:
                feature_data = json.load(f)

            X = np.array(feature_data['X'])
            y = np.array(feature_data['y'])
            codon_mapping = feature_data['codon_mapping']
            feature_names = feature_data['feature_names']

            print(f"✅ 特征加载成功: {X.shape}")

        except FileNotFoundError:
            print("❌ 预处理特征未找到，正在创建...")
            X, y, codon_mapping = create_enhanced_features()
            feature_names = [
                'AA_hydrophobicity', 'AA_polarity', 'AA_positive', 'AA_negative', 'AA_aromatic',
                'prev_hydrophobicity', 'prev_polarity', 'prev_positive', 'prev_negative', 'prev_aromatic',
                'next_hydrophobicity', 'next_polarity', 'next_positive', 'next_negative', 'next_aromatic',
                'rel_position', 'seq_length',
                'aa_codon_hash', 'ecoli_preference', 'codon_rarity', 'gc_content',
                'codon_pos1_bias', 'codon_pos2_bias', 'codon_pos3_bias',
                'local_gc_content', 'local_preference', 'aa_repeat_pattern'
            ]

    # 重新映射标签确保连续性
    from sklearn.preprocessing import LabelEncoder
    label_encoder = LabelEncoder()
    y_encoded = label_encoder.fit_transform(y)

    print(f"\n📊 数据集统计:")
    print(f"   样本数: {len(X):,}")
    print(f"   特征数: {X.shape[1]}")
    print(f"   原始类别数: {len(np.unique(y))}")
    print(f"   编码后类别数: {len(np.unique(y_encoded))}")
    print(f"   类别分布: {np.bincount(y_encoded)[:10]}... (前10个)")

    # 数据分割（与之前保持一致）
    X_train, X_test, y_train, y_test = train_test_split(
        X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
    )

    print(f"\n🔄 数据分割:")
    print(f"   训练集: {len(X_train):,} 样本")
    print(f"   测试集: {len(X_test):,} 样本")

    # 特征标准化
    print(f"\n⚙️ 特征标准化...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # 增强XGBoost参数（基于54.8%成功方法优化）
    enhanced_xgb_params = {
        'objective': 'multi:softmax',
        'num_class': len(np.unique(y_encoded)),
        'max_depth': 10,  # 增加深度以捕获复杂模式
        'learning_rate': 0.08,  # 稍微降低学习率
        'n_estimators': 800,  # 增加树的数量
        'subsample': 0.85,  # 增加子采样
        'colsample_bytree': 0.85,  # 增加特征采样
        'colsample_bylevel': 0.8,  # 添加层级特征采样
        'reg_alpha': 0.1,  # L1正则化
        'reg_lambda': 1.0,  # L2正则化
        'random_state': 42,
        'n_jobs': -1,
        'eval_metric': 'mlogloss',
        'early_stopping_rounds': 50,  # 移到这里
        'tree_method': 'gpu_hist',  # GPU加速
        'gpu_id': 0
    }

    print(f"\n🎯 增强XGBoost配置:")
    print(f"   树深度: {enhanced_xgb_params['max_depth']}")
    print(f"   学习率: {enhanced_xgb_params['learning_rate']}")
    print(f"   树数量: {enhanced_xgb_params['n_estimators']}")
    print(f"   GPU加速: 启用")
    print(f"   正则化: L1={enhanced_xgb_params['reg_alpha']}, L2={enhanced_xgb_params['reg_lambda']}")

    # 训练增强XGBoost
    print(f"\n🏃 开始训练增强XGBoost...")
    start_time = time.time()

    enhanced_xgb = xgb.XGBClassifier(**enhanced_xgb_params)
    enhanced_xgb.fit(
        X_train_scaled, y_train,
        eval_set=[(X_train_scaled, y_train), (X_test_scaled, y_test)],
        verbose=False
    )

    train_time = time.time() - start_time

    # 预测和评估
    y_pred = enhanced_xgb.predict(X_test_scaled)
    accuracy = accuracy_score(y_test, y_pred)

    print(f"\n🎉 增强XGBoost结果:")
    print(f"   训练时间: {train_time:.2f} 秒")
    print(f"   测试准确率: {accuracy:.4f} ({accuracy*100:.1f}%)")

    # 与之前结果对比
    previous_best = 0.548  # 54.8%
    improvement = accuracy - previous_best

    print(f"\n📈 性能对比:")
    print(f"   之前最佳 (传统方法): {previous_best*100:.1f}%")
    print(f"   增强XGBoost: {accuracy*100:.1f}%")
    if improvement > 0:
        print(f"   🎉 提升: +{improvement*100:.1f}%")
    else:
        print(f"   📉 下降: {improvement*100:.1f}%")

    # 交叉验证
    print(f"\n🔄 5折交叉验证...")
    cv_scores = cross_val_score(enhanced_xgb, X_train_scaled, y_train, cv=5, scoring='accuracy')
    print(f"   CV准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    print(f"   CV分数: {[f'{score:.3f}' for score in cv_scores]}")

    # 特征重要性分析
    print(f"\n🔍 特征重要性分析...")
    feature_importance = list(zip(feature_names, enhanced_xgb.feature_importances_))
    feature_importance.sort(key=lambda x: x[1], reverse=True)

    print(f"   前10个重要特征:")
    for i, (feature, importance) in enumerate(feature_importance[:10]):
        print(f"   {i+1:2d}. {feature:<20}: {importance:.4f}")

    # 分析CodonTransformer特征的贡献
    codon_transformer_features = [
        'aa_codon_hash', 'ecoli_preference', 'codon_rarity', 'gc_content',
        'codon_pos1_bias', 'codon_pos2_bias', 'codon_pos3_bias'
    ]

    ct_importance = sum([imp for name, imp in feature_importance if name in codon_transformer_features])
    traditional_importance = sum([imp for name, imp in feature_importance if name not in codon_transformer_features and 'local' not in name])
    context_importance = sum([imp for name, imp in feature_importance if 'local' in name or 'repeat' in name])

    print(f"\n🧬 特征类别贡献:")
    print(f"   传统特征 (17个): {traditional_importance:.4f} ({traditional_importance*100:.1f}%)")
    print(f"   CodonTransformer特征 (7个): {ct_importance:.4f} ({ct_importance*100:.1f}%)")
    print(f"   上下文特征 (3个): {context_importance:.4f} ({context_importance*100:.1f}%)")

    # 保存结果
    results = {
        'enhanced_xgboost_accuracy': float(accuracy),
        'previous_best_accuracy': float(previous_best),
        'improvement': float(improvement),
        'target_achieved': accuracy >= 0.8,
        'train_time': float(train_time),
        'cv_mean': float(cv_scores.mean()),
        'cv_std': float(cv_scores.std()),
        'feature_importance': feature_importance,
        'codon_transformer_contribution': float(ct_importance),
        'traditional_contribution': float(traditional_importance),
        'context_contribution': float(context_importance),
        'total_features': X.shape[1],
        'total_samples': len(X),
        'gpu_accelerated': True
    }

    with open('enhanced_xgboost_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\n💾 结果已保存到: enhanced_xgboost_results.json")

    # 目标达成分析
    if accuracy >= 0.8:
        print(f"\n🎉 SUCCESS: 达到80%目标准确率!")
        print(f"   实际准确率: {accuracy*100:.1f}%")
        print(f"   超出目标: +{(accuracy-0.8)*100:.1f}%")
    elif accuracy > previous_best:
        print(f"\n🎯 PROGRESS: 显著改进!")
        print(f"   提升幅度: +{improvement*100:.1f}%")
        print(f"   距离目标: {(0.8-accuracy)*100:.1f}%")

        # 建议进一步优化
        if ct_importance < 0.3:
            print(f"   💡 建议: CodonTransformer特征贡献较低，考虑增强")
        if context_importance < 0.1:
            print(f"   💡 建议: 上下文特征贡献较低，考虑扩展窗口")
    else:
        print(f"\n📊 ANALYSIS: 需要进一步优化")
        print(f"   当前准确率: {accuracy*100:.1f}%")
        print(f"   建议: 尝试集成方法或深度学习混合")

    return accuracy, enhanced_xgb, feature_importance


if __name__ == "__main__":
    # 创建增强特征并训练
    print("🧬 增强传统机器学习 - Enhanced Traditional ML")
    print("基于54.8%成功方法 + CodonTransformer核心特性")
    print("目标: 达到80%准确率")
    print("=" * 70)

    # 步骤1: 创建增强特征
    X, y, codon_mapping = create_enhanced_features()

    # 步骤2: 训练增强XGBoost
    accuracy, model, feature_importance = train_enhanced_xgboost(X, y, codon_mapping)

    print(f"\n🏁 增强传统机器学习完成!")
    print(f"最终准确率: {accuracy*100:.1f}%")

    if accuracy >= 0.8:
        print("🎉 成功达到80%目标!")
    else:
        print(f"距离目标还需提升: {(0.8-accuracy)*100:.1f}%")
