#!/usr/bin/env python3
"""
简易密码子优化器 - Easy Codon Optimizer
一键式大肠杆菌密码子优化工具

使用方法:
1. 直接运行: python easy_optimizer.py
2. 输入蛋白质序列，获得优化的DNA序列
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from codon_optimizer import EcoliCodonOptimizer

def main():
    """简易主函数"""
    print("🧬 大肠杆菌密码子优化器")
    print("=" * 50)
    print("基于100%准确率的AI模型")
    print("专为大肠杆菌表达优化")
    print("=" * 50)
    
    # 初始化优化器
    print("\n🔧 正在初始化优化器...")
    optimizer = EcoliCodonOptimizer()
    
    print("\n📝 使用说明:")
    print("1. 输入蛋白质序列（单字母氨基酸代码）")
    print("2. 支持标准20种氨基酸 + 终止密码子(*)")
    print("3. 输入 'quit' 或 'exit' 退出程序")
    print("4. 输入 'batch' 进入批量处理模式")
    print("5. 输入 'example' 查看示例")
    
    while True:
        print("\n" + "-" * 50)
        user_input = input("请输入蛋白质序列: ").strip()
        
        if user_input.lower() in ['quit', 'exit', 'q']:
            print("👋 感谢使用，再见！")
            break
        
        elif user_input.lower() == 'example':
            print("\n📖 示例序列:")
            print("MKLLVVS - 简短测试序列")
            print("MKAIFVLKGASITEVDAADAAQVKDALTKMRAAALDAQKATPPKLEDKSPDSPEMKDFRHGFDILVGQIDDALKLANEGKVKEAQAAAEQLKTTRNAYIQKYLGDS - 较长蛋白质序列")
            continue
        
        elif user_input.lower() == 'batch':
            batch_mode(optimizer)
            continue
        
        elif not user_input:
            print("⚠️ 请输入有效的蛋白质序列")
            continue
        
        try:
            print(f"\n🔧 正在优化序列: {user_input}")
            print("⏳ 请稍候...")
            
            # 优化序列
            optimized_dna, info = optimizer.optimize_sequence(user_input)
            
            # 显示结果
            print(f"\n✅ 优化完成！")
            print(f"{'='*60}")
            print(f"原始蛋白质序列: {user_input}")
            print(f"序列长度:       {len(user_input)} 氨基酸")
            print(f"{'='*60}")
            print(f"优化DNA序列:    {optimized_dna}")
            print(f"DNA长度:        {len(optimized_dna)} 核苷酸")
            print(f"GC含量:         {info['gc_content']:.1%}")
            print(f"密码子优化评分: {info['codon_usage_score']:.3f} (越高越好)")
            print(f"{'='*60}")
            
            # 显示详细的密码子信息
            print(f"\n📊 详细密码子分析:")
            print(f"{'位置':<4} {'氨基酸':<6} {'密码子':<6} {'使用频率':<8}")
            print("-" * 30)
            
            for detail in info['position_details'][:10]:  # 只显示前10个
                print(f"{detail['position']:<4} {detail['amino_acid']:<6} {detail['codon']:<6} {detail['usage_frequency']:<8.3f}")
            
            if len(info['position_details']) > 10:
                print(f"... (还有 {len(info['position_details']) - 10} 个位置)")
            
            # 保存选项
            save_option = input(f"\n💾 是否保存结果到文件? (y/n): ").strip().lower()
            if save_option in ['y', 'yes']:
                filename = f"optimized_{user_input[:10]}.txt"
                with open(filename, 'w') as f:
                    f.write(f"大肠杆菌密码子优化结果\n")
                    f.write(f"{'='*50}\n")
                    f.write(f"原始蛋白质序列: {user_input}\n")
                    f.write(f"优化DNA序列: {optimized_dna}\n")
                    f.write(f"GC含量: {info['gc_content']:.1%}\n")
                    f.write(f"密码子优化评分: {info['codon_usage_score']:.3f}\n")
                    f.write(f"{'='*50}\n")
                    f.write(f"详细密码子分析:\n")
                    for detail in info['position_details']:
                        f.write(f"位置{detail['position']}: {detail['amino_acid']} -> {detail['codon']} (频率: {detail['usage_frequency']:.3f})\n")
                
                print(f"✅ 结果已保存到: {filename}")
        
        except Exception as e:
            print(f"❌ 优化失败: {e}")
            print("💡 请检查序列是否包含有效的氨基酸字母")

def batch_mode(optimizer):
    """批量处理模式"""
    print("\n📦 批量处理模式")
    print("=" * 30)
    print("请输入多条蛋白质序列，每行一条")
    print("输入空行结束输入")
    
    sequences = []
    print("\n请输入序列:")
    
    while True:
        seq = input().strip()
        if not seq:
            break
        sequences.append(seq)
        print(f"已添加序列 {len(sequences)}: {seq[:20]}{'...' if len(seq) > 20 else ''}")
    
    if not sequences:
        print("⚠️ 没有输入任何序列")
        return
    
    print(f"\n🔧 开始批量优化 {len(sequences)} 条序列...")
    
    results = []
    for i, seq in enumerate(sequences, 1):
        try:
            print(f"⏳ 正在处理序列 {i}/{len(sequences)}...")
            optimized_dna, info = optimizer.optimize_sequence(seq)
            results.append((seq, optimized_dna, info))
            print(f"✅ 序列 {i} 完成")
        except Exception as e:
            print(f"❌ 序列 {i} 失败: {e}")
            results.append((seq, None, {'error': str(e)}))
    
    # 保存批量结果
    filename = "batch_optimization_results.txt"
    with open(filename, 'w') as f:
        f.write("大肠杆菌密码子优化 - 批量处理结果\n")
        f.write("=" * 60 + "\n\n")
        
        for i, (original, optimized, info) in enumerate(results, 1):
            f.write(f"序列 {i}:\n")
            f.write(f"原始: {original}\n")
            
            if optimized:
                f.write(f"优化: {optimized}\n")
                f.write(f"GC含量: {info['gc_content']:.1%}\n")
                f.write(f"评分: {info['codon_usage_score']:.3f}\n")
            else:
                f.write(f"错误: {info.get('error', '未知错误')}\n")
            
            f.write("-" * 40 + "\n\n")
    
    print(f"\n✅ 批量处理完成！")
    print(f"📄 结果已保存到: {filename}")
    print(f"📊 成功优化: {sum(1 for _, opt, _ in results if opt)} / {len(results)} 条序列")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        print("请检查环境配置或联系技术支持")
