#!/usr/bin/env python3
"""
简化版大肠杆菌密码子优化器
基于规则的高效密码子优化，无需复杂依赖

使用方法:
1. 单条序列: python simple_codon_optimizer.py --sequence "MKLLVVS"
2. 交互模式: python simple_codon_optimizer.py
3. 批量处理: python simple_codon_optimizer.py --input sequences.txt
"""

import argparse
import json
import os
import sys
from typing import List, Dict, Tuple

class SimpleEcoliCodonOptimizer:
    """简化版大肠杆菌密码子优化器"""
    
    def __init__(self):
        """初始化优化器"""
        
        # 大肠杆菌密码子使用频率表（基于实验数据）
        self.ecoli_codon_usage = {
            'TTT': 0.58, 'TTC': 0.42,  # F
            'TTA': 0.14, 'TTG': 0.13, 'CTT': 0.12, 'CTC': 0.10, 'CTA': 0.04, 'CTG': 0.47,  # L
            'TCT': 0.17, 'TCC': 0.15, 'TCA': 0.14, 'TCG': 0.14, 'AGT': 0.16, 'AGC': 0.25,  # S
            'TAT': 0.59, 'TAC': 0.41,  # Y
            'TGT': 0.46, 'TGC': 0.54,  # C
            'TGG': 1.00,  # W
            'CCT': 0.18, 'CCC': 0.13, 'CCA': 0.20, 'CCG': 0.49,  # P
            'CAT': 0.57, 'CAC': 0.43,  # H
            'CAA': 0.34, 'CAG': 0.66,  # Q
            'CGT': 0.36, 'CGC': 0.36, 'CGA': 0.07, 'CGG': 0.11, 'AGA': 0.07, 'AGG': 0.04,  # R
            'ATT': 0.49, 'ATC': 0.39, 'ATA': 0.11,  # I
            'ATG': 1.00,  # M
            'ACT': 0.19, 'ACC': 0.40, 'ACA': 0.17, 'ACG': 0.25,  # T
            'AAT': 0.49, 'AAC': 0.51,  # N
            'AAA': 0.74, 'AAG': 0.26,  # K
            'GTT': 0.28, 'GTC': 0.20, 'GTA': 0.17, 'GTG': 0.35,  # V
            'GCT': 0.18, 'GCC': 0.26, 'GCA': 0.23, 'GCG': 0.33,  # A
            'GAT': 0.63, 'GAC': 0.37,  # D
            'GAA': 0.68, 'GAG': 0.32,  # E
            'GGT': 0.35, 'GGC': 0.37, 'GGA': 0.13, 'GGG': 0.15,  # G
            'TAA': 0.61, 'TAG': 0.09, 'TGA': 0.30  # *
        }
        
        # 遗传密码表
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G',
            'TAA': '*', 'TAG': '*', 'TGA': '*'
        }
        
        # 反向遗传密码表（氨基酸到密码子）
        self.aa_to_codons = {}
        for codon, aa in self.genetic_code.items():
            if aa not in self.aa_to_codons:
                self.aa_to_codons[aa] = []
            self.aa_to_codons[aa].append(codon)
        
        # 为每个氨基酸预计算最优密码子
        self.optimal_codons = {}
        for aa, codons in self.aa_to_codons.items():
            # 选择使用频率最高的密码子
            best_codon = max(codons, key=lambda c: self.ecoli_codon_usage.get(c, 0))
            self.optimal_codons[aa] = best_codon
        
        print("🧬 简化版大肠杆菌密码子优化器初始化完成")
        print("   基于大肠杆菌密码子使用频率的规则优化")
        print("   支持20种标准氨基酸 + 终止密码子")
    
    def optimize_sequence(self, protein_sequence: str) -> Tuple[str, Dict]:
        """优化单条蛋白质序列"""
        protein_sequence = protein_sequence.upper().strip()
        
        # 验证序列
        valid_aa = set('ACDEFGHIKLMNPQRSTVWY*')
        if not all(aa in valid_aa for aa in protein_sequence):
            invalid_aa = set(protein_sequence) - valid_aa
            raise ValueError(f"无效的氨基酸: {invalid_aa}")
        
        optimized_codons = []
        optimization_info = {
            'original_length': len(protein_sequence),
            'optimized_length': len(protein_sequence) * 3,
            'gc_content': 0.0,
            'codon_usage_score': 0.0,
            'position_details': []
        }
        
        # 为每个氨基酸选择最优密码子
        for position, aa in enumerate(protein_sequence):
            if aa in self.optimal_codons:
                best_codon = self.optimal_codons[aa]
                codon_usage = self.ecoli_codon_usage.get(best_codon, 0.0)
            else:
                # 未知氨基酸，使用默认
                best_codon = 'NNN'
                codon_usage = 0.0
            
            optimized_codons.append(best_codon)
            
            # 记录详细信息
            optimization_info['position_details'].append({
                'position': position + 1,
                'amino_acid': aa,
                'codon': best_codon,
                'usage_frequency': codon_usage,
                'alternatives': self.aa_to_codons.get(aa, [])
            })
        
        # 计算整体统计
        optimized_dna = ''.join(optimized_codons)
        
        if optimized_dna:
            gc_count = optimized_dna.count('G') + optimized_dna.count('C')
            optimization_info['gc_content'] = gc_count / len(optimized_dna)
            
            usage_scores = [detail['usage_frequency'] for detail in optimization_info['position_details']]
            optimization_info['codon_usage_score'] = sum(usage_scores) / len(usage_scores) if usage_scores else 0
        
        return optimized_dna, optimization_info
    
    def optimize_batch(self, sequences: List[str]) -> List[Tuple[str, Dict]]:
        """批量优化序列"""
        results = []
        total = len(sequences)
        
        print(f"📦 开始批量优化 {total} 条序列...")
        
        for i, seq in enumerate(sequences, 1):
            try:
                optimized_dna, info = self.optimize_sequence(seq)
                results.append((optimized_dna, info))
                print(f"✅ 完成 {i}/{total}: {len(seq)} aa -> {len(optimized_dna)} nt")
            except Exception as e:
                print(f"❌ 序列 {i} 优化失败: {e}")
                results.append((None, {'error': str(e)}))
        
        return results
    
    def get_codon_alternatives(self, amino_acid: str) -> Dict:
        """获取氨基酸的所有密码子选择"""
        aa = amino_acid.upper()
        if aa not in self.aa_to_codons:
            return {'error': f'未知氨基酸: {aa}'}
        
        codons = self.aa_to_codons[aa]
        alternatives = []
        
        for codon in codons:
            usage_freq = self.ecoli_codon_usage.get(codon, 0.0)
            alternatives.append({
                'codon': codon,
                'usage_frequency': usage_freq,
                'is_optimal': codon == self.optimal_codons.get(aa)
            })
        
        # 按使用频率排序
        alternatives.sort(key=lambda x: x['usage_frequency'], reverse=True)
        
        return {
            'amino_acid': aa,
            'alternatives': alternatives,
            'optimal_codon': self.optimal_codons.get(aa)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="简化版大肠杆菌密码子优化器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  单条序列:
    python simple_codon_optimizer.py --sequence "MKLLVVS"
    
  批量处理:
    python simple_codon_optimizer.py --input sequences.txt --output optimized.txt
    
  交互模式:
    python simple_codon_optimizer.py --interactive
    
  查看密码子选择:
    python simple_codon_optimizer.py --alternatives L
        """
    )
    
    parser.add_argument('--sequence', '-s', type=str, help='单条蛋白质序列')
    parser.add_argument('--input', '-i', type=str, help='输入文件（每行一条序列）')
    parser.add_argument('--output', '-o', type=str, help='输出文件')
    parser.add_argument('--interactive', action='store_true', help='交互模式')
    parser.add_argument('--alternatives', '-a', type=str, help='查看指定氨基酸的密码子选择')
    parser.add_argument('--format', choices=['fasta', 'txt'], default='txt', help='输出格式')
    
    args = parser.parse_args()
    
    # 初始化优化器
    optimizer = SimpleEcoliCodonOptimizer()
    
    try:
        if args.alternatives:
            # 查看密码子选择
            result = optimizer.get_codon_alternatives(args.alternatives)
            if 'error' in result:
                print(f"❌ {result['error']}")
            else:
                print(f"\n🧬 氨基酸 {result['amino_acid']} 的密码子选择:")
                print(f"{'密码子':<8} {'使用频率':<10} {'状态':<8}")
                print("-" * 30)
                for alt in result['alternatives']:
                    status = "最优" if alt['is_optimal'] else ""
                    print(f"{alt['codon']:<8} {alt['usage_frequency']:<10.3f} {status:<8}")
        
        elif args.interactive:
            # 交互模式
            print("\n🧬 简化版大肠杆菌密码子优化器 - 交互模式")
            print("输入 'quit' 退出，输入 'help' 查看帮助")
            
            while True:
                seq = input("\n请输入蛋白质序列: ").strip()
                
                if seq.lower() == 'quit':
                    break
                elif seq.lower() == 'help':
                    print("\n📖 帮助信息:")
                    print("- 输入标准20种氨基酸序列（单字母代码）")
                    print("- 支持终止密码子 (*)")
                    print("- 示例: MKLLVVS")
                    print("- 输入 'quit' 退出")
                    continue
                elif not seq:
                    print("⚠️ 请输入有效的蛋白质序列")
                    continue
                
                try:
                    optimized_dna, info = optimizer.optimize_sequence(seq)
                    print(f"\n✅ 优化结果:")
                    print(f"原始序列: {seq}")
                    print(f"优化DNA:  {optimized_dna}")
                    print(f"GC含量:   {info['gc_content']:.1%}")
                    print(f"密码子评分: {info['codon_usage_score']:.3f}")
                    
                    # 显示前几个位置的详细信息
                    print(f"\n前5个位置详情:")
                    for detail in info['position_details'][:5]:
                        print(f"  {detail['position']}: {detail['amino_acid']} -> {detail['codon']} (频率: {detail['usage_frequency']:.3f})")
                    
                except Exception as e:
                    print(f"❌ 优化失败: {e}")
        
        elif args.sequence:
            # 单条序列模式
            optimized_dna, info = optimizer.optimize_sequence(args.sequence)
            print(f"\n✅ 优化结果:")
            print(f"原始序列: {args.sequence}")
            print(f"优化DNA:  {optimized_dna}")
            print(f"GC含量:   {info['gc_content']:.1%}")
            print(f"密码子评分: {info['codon_usage_score']:.3f}")
            
        elif args.input:
            # 批量处理模式
            if not os.path.exists(args.input):
                print(f"❌ 输入文件不存在: {args.input}")
                return
            
            # 读取序列
            sequences = []
            with open(args.input, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        sequences.append(line)
            
            if not sequences:
                print("❌ 输入文件中没有找到有效序列")
                return
            
            # 批量优化
            results = optimizer.optimize_batch(sequences)
            
            # 保存结果
            output_file = args.output or 'optimized_sequences.txt'
            with open(output_file, 'w') as f:
                f.write("# 简化版大肠杆菌密码子优化结果\n")
                f.write("# 基于大肠杆菌密码子使用频率优化\n\n")
                
                for i, (optimized_dna, info) in enumerate(results):
                    if optimized_dna:
                        if args.format == 'fasta':
                            f.write(f">Sequence_{i+1}_optimized_ecoli\n")
                            f.write(f"{optimized_dna}\n")
                        else:
                            f.write(f"# 序列 {i+1}\n")
                            f.write(f"原始序列: {sequences[i]}\n")
                            f.write(f"优化DNA:  {optimized_dna}\n")
                            f.write(f"GC含量:   {info['gc_content']:.1%}\n")
                            f.write(f"密码子评分: {info['codon_usage_score']:.3f}\n\n")
                    else:
                        f.write(f"# 序列 {i+1} - 错误: {info.get('error', '未知错误')}\n\n")
            
            print(f"✅ 批量优化完成，结果保存到: {output_file}")
            
            # 统计信息
            success_count = sum(1 for dna, _ in results if dna is not None)
            print(f"📊 成功优化: {success_count}/{len(results)} 条序列")
        
        else:
            # 默认交互模式
            print("\n🧬 简化版大肠杆菌密码子优化器")
            print("=" * 50)
            print("基于大肠杆菌密码子使用频率的高效优化")
            print("无需复杂依赖，即开即用")
            print("=" * 50)
            
            # 显示示例
            example_seq = "MKLLVVS"
            optimized_dna, info = optimizer.optimize_sequence(example_seq)
            
            print(f"\n📖 示例:")
            print(f"输入: {example_seq}")
            print(f"输出: {optimized_dna}")
            print(f"GC含量: {info['gc_content']:.1%}")
            print(f"评分: {info['codon_usage_score']:.3f}")
            
            print(f"\n💡 使用提示:")
            print("python simple_codon_optimizer.py --interactive  # 交互模式")
            print("python simple_codon_optimizer.py --sequence MKLLVVS  # 单条序列")
            print("python simple_codon_optimizer.py --help  # 查看帮助")
    
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"❌ 程序错误: {e}")


if __name__ == "__main__":
    main()
