#!/usr/bin/env python3
"""
高精度密码子预测器
目标：与训练数据相似度95%+，预测精度80%+
结合ESM2蛋白质表示和集成学习
"""

import pandas as pd
import numpy as np
import pickle
import json
import time
import os
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

# 检查依赖
try:
    from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier, VotingClassifier
    from sklearn.neural_network import MLPClassifier
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.metrics import accuracy_score, classification_report
    import xgboost as xgb
    HAS_SKLEARN = True
except ImportError as e:
    print(f"❌ 缺少机器学习库: {e}")
    HAS_SKLEARN = False

# 尝试导入transformers用于ESM2
try:
    from transformers import EsmModel, EsmTokenizer
    import torch
    HAS_ESM2 = True
    print("✅ ESM2依赖可用")
except ImportError:
    HAS_ESM2 = False
    print("⚠️ ESM2依赖不可用，将使用增强特征工程")

class AdvancedCodonPredictor:
    """高精度密码子预测器"""
    
    def __init__(self, esm2_path=None):
        # 设置正确的ESM2路径 - 必须使用ESM2
        if esm2_path is None:
            # 首先尝试本地路径
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"

            if os.path.exists(local_path) and os.path.exists(os.path.join(local_path, "config.json")):
                esm2_path = local_path
                print(f"🎯 找到本地ESM2模型: {local_path}")
            else:
                # 如果本地不存在，从HuggingFace下载
                esm2_path = "facebook/esm2_t33_650M_UR50D"
                print(f"🌐 将从HuggingFace下载ESM2模型")

        self.esm2_path = esm2_path
        self.esm2_model = None
        self.esm2_tokenizer = None
        self.ensemble_model = None
        self.scaler = None
        self.label_encoder = None
        
        # 氨基酸属性（扩展版）
        self.aa_properties = {
            'A': [1.8, 0, 0, 0, 0, 89.1, 6.0, 1.0],    # 疏水性, 极性, 正电, 负电, 芳香性, 分子量, pKa, 体积
            'R': [-4.5, 1, 1, 0, 0, 174.2, 12.5, 4.0], 'N': [-3.5, 1, 0, 0, 0, 132.1, 8.8, 2.8],
            'D': [-3.5, 1, 0, 1, 0, 133.1, 3.9, 2.8], 'C': [2.5, 0, 0, 0, 0, 121.0, 8.3, 2.4],
            'Q': [-3.5, 1, 0, 0, 0, 146.1, 9.1, 3.5], 'E': [-3.5, 1, 0, 1, 0, 147.1, 4.3, 3.5],
            'G': [-0.4, 0, 0, 0, 0, 75.1, 6.0, 1.0],  'H': [-3.2, 1, 1, 0, 1, 155.2, 7.6, 4.2],
            'I': [4.5, 0, 0, 0, 0, 131.2, 6.0, 4.0],  'L': [3.8, 0, 0, 0, 0, 131.2, 6.0, 4.0],
            'K': [-3.9, 1, 1, 0, 0, 146.2, 10.5, 4.0], 'M': [1.9, 0, 0, 0, 0, 149.2, 5.7, 4.3],
            'F': [2.8, 0, 0, 0, 1, 165.2, 5.5, 5.2],  'P': [-1.6, 0, 0, 0, 0, 115.1, 6.3, 2.9],
            'S': [-0.8, 1, 0, 0, 0, 105.1, 5.7, 1.9], 'T': [-0.7, 1, 0, 0, 0, 119.1, 5.6, 2.8],
            'W': [-0.9, 0, 0, 0, 1, 204.2, 5.9, 8.1], 'Y': [-1.3, 1, 0, 0, 1, 181.2, 10.1, 6.2],
            'V': [4.2, 0, 0, 0, 0, 117.1, 6.0, 3.1],  '*': [0, 0, 0, 0, 0, 0, 7.0, 0]
        }
        
        # 氨基酸分类
        self.aa_groups = {
            'hydrophobic': set('AILMFPWV'),
            'polar': set('NQST'),
            'positive': set('RK'),
            'negative': set('DE'),
            'aromatic': set('FWY'),
            'small': set('AGCS'),
            'large': set('FWYR'),
            'special': set('CGP')
        }
        
        print("🧬 高精度密码子预测器初始化")
        print(f"   目标：相似度95%+，精度80%+")
        
        # 初始化ESM2
        if HAS_ESM2:
            self._init_esm2()
    
    def _init_esm2(self):
        """初始化ESM2模型"""
        try:
            print("🔧 初始化ESM2模型...")
            print(f"   尝试路径: {self.esm2_path}")

            # 检查模型路径
            if os.path.exists(self.esm2_path) and os.path.isdir(self.esm2_path):
                # 检查是否有config.json文件
                config_file = os.path.join(self.esm2_path, "config.json")
                if os.path.exists(config_file):
                    print(f"   从本地加载: {self.esm2_path}")
                    self.esm2_tokenizer = EsmTokenizer.from_pretrained(self.esm2_path)
                    self.esm2_model = EsmModel.from_pretrained(self.esm2_path)
                else:
                    print(f"   本地路径缺少配置文件，从HuggingFace加载")
                    self.esm2_tokenizer = EsmTokenizer.from_pretrained("facebook/esm2_t33_650M_UR50D")
                    self.esm2_model = EsmModel.from_pretrained("facebook/esm2_t33_650M_UR50D")
            else:
                print(f"   从HuggingFace加载: {self.esm2_path}")
                self.esm2_tokenizer = EsmTokenizer.from_pretrained(self.esm2_path)
                self.esm2_model = EsmModel.from_pretrained(self.esm2_path)

            # 设置为评估模式
            self.esm2_model.eval()

            # 移动模型到GPU（如果可用）
            if torch.cuda.is_available():
                print("   🚀 将ESM2模型移至GPU...")
                self.esm2_model = self.esm2_model.cuda()
                self.device = "cuda"
                print(f"   ✅ ESM2模型已移至GPU")
            else:
                self.device = "cpu"
                print(f"   ⚠️ 使用CPU运行ESM2")

            # 测试模型
            print(f"   🧪 测试ESM2模型 (设备: {self.device})...")
            test_seq = "MKLLVVS"
            test_inputs = self.esm2_tokenizer(test_seq, return_tensors="pt")

            # 将输入移到相同设备
            if self.device == "cuda":
                test_inputs = {k: v.cuda() for k, v in test_inputs.items()}

            with torch.no_grad():
                test_outputs = self.esm2_model(**test_inputs)

            print("✅ ESM2模型加载并测试成功")
            print(f"   📊 模型输出维度: {test_outputs.last_hidden_state.shape}")
            print(f"   🎯 运行设备: {test_outputs.last_hidden_state.device}")

        except Exception as e:
            print(f"❌ ESM2初始化失败: {e}")
            print("🚨 ESM2是达成目标的必需组件，程序将退出")
            raise RuntimeError(f"ESM2模型加载失败，无法达成95%相似度和80%精度目标: {e}")
    
    def extract_esm2_features(self, protein_sequence: str) -> np.ndarray:
        """提取ESM2特征 - 必须成功"""
        if not self.esm2_model:
            raise RuntimeError("ESM2模型未初始化，无法提取特征")

        try:
            # 限制序列长度以避免内存问题
            max_length = min(1024, len(protein_sequence) + 2)  # +2 for CLS and SEP tokens

            # 分词
            inputs = self.esm2_tokenizer(
                protein_sequence,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=max_length
            )

            # 将输入移到GPU（如果模型在GPU上）
            if hasattr(self, 'device') and self.device == "cuda":
                inputs = {k: v.cuda() for k, v in inputs.items()}

            # 前向传播
            with torch.no_grad():
                outputs = self.esm2_model(**inputs)
                # 获取最后一层隐藏状态
                hidden_states = outputs.last_hidden_state.squeeze(0)  # [seq_len, hidden_dim]

                # 移除特殊token（CLS和SEP）
                # ESM2的token顺序: [CLS] + protein_tokens + [SEP]
                protein_embeddings = hidden_states[1:-1]  # [protein_len, hidden_dim]

                # 确保维度正确
                if protein_embeddings.shape[0] != len(protein_sequence):
                    print(f"⚠️ 维度不匹配: 序列长度{len(protein_sequence)}, 特征长度{protein_embeddings.shape[0]}")
                    # 如果长度不匹配，截断或填充
                    if protein_embeddings.shape[0] > len(protein_sequence):
                        protein_embeddings = protein_embeddings[:len(protein_sequence)]
                    else:
                        # 填充零向量（确保在同一设备上）
                        padding_size = len(protein_sequence) - protein_embeddings.shape[0]
                        padding = torch.zeros(padding_size, protein_embeddings.shape[1], device=protein_embeddings.device)
                        protein_embeddings = torch.cat([protein_embeddings, padding], dim=0)

                return protein_embeddings.cpu().numpy()

        except Exception as e:
            print(f"❌ ESM2特征提取失败: {e}")
            raise RuntimeError(f"ESM2特征提取失败，无法继续: {e}")
    
    def extract_enhanced_features(self, protein_seq: str, position: int, esm2_features: np.ndarray = None) -> List[float]:
        """提取增强特征"""
        features = []
        
        # 1. ESM2特征 (1280维，必须使用)
        if esm2_features is not None and position < len(esm2_features):
            features.extend(esm2_features[position].tolist())
        else:
            raise RuntimeError(f"ESM2特征缺失，位置{position}，无法达成目标精度")
        
        # 2. 增强的氨基酸特征 (8维)
        aa = protein_seq[position] if position < len(protein_seq) else '*'
        aa_props = self.aa_properties.get(aa, [0] * 8)
        features.extend(aa_props)
        
        # 3. 上下文氨基酸特征 (扩展窗口: 16维)
        for offset in [-2, -1, 1, 2]:  # 前后2个氨基酸
            ctx_pos = position + offset
            if 0 <= ctx_pos < len(protein_seq):
                ctx_aa = protein_seq[ctx_pos]
                ctx_props = self.aa_properties.get(ctx_aa, [0] * 8)
                features.extend(ctx_props[:4])  # 只取前4个属性
            else:
                features.extend([0] * 4)
        
        # 4. 位置和序列特征 (5维)
        features.extend([
            position / len(protein_seq),  # 相对位置
            position,  # 绝对位置
            len(protein_seq),  # 序列长度
            min(position, len(protein_seq) - position - 1),  # 到序列端点的最小距离
            1.0 if position < len(protein_seq) // 2 else 0.0  # N端/C端标记
        ])
        
        # 5. 扩展窗口特征 (20维)
        for window_size in [3, 5, 7, 9]:
            start_pos = max(0, position - window_size // 2)
            end_pos = min(len(protein_seq), position + window_size // 2 + 1)
            window_seq = protein_seq[start_pos:end_pos]
            
            # 计算窗口内各类氨基酸的比例
            window_features = []
            for group_name, group_aas in self.aa_groups.items():
                count = sum(1 for aa in window_seq if aa in group_aas)
                ratio = count / len(window_seq) if window_seq else 0
                window_features.append(ratio)
            
            features.extend(window_features[:5])  # 取前5个组
        
        # 6. 序列模式特征 (10维)
        # 重复模式
        repeat_counts = []
        for k in range(1, 4):  # 1-3氨基酸重复
            pattern_count = 0
            for i in range(max(0, position - 5), min(len(protein_seq) - k + 1, position + 6)):
                if i + k <= len(protein_seq):
                    pattern = protein_seq[i:i+k]
                    if pattern == protein_seq[position:position+k] and i != position:
                        pattern_count += 1
            repeat_counts.append(pattern_count)
        
        features.extend(repeat_counts)
        
        # 局部复杂度
        local_start = max(0, position - 10)
        local_end = min(len(protein_seq), position + 11)
        local_seq = protein_seq[local_start:local_end]
        unique_aa_count = len(set(local_seq))
        complexity = unique_aa_count / len(local_seq) if local_seq else 0
        features.append(complexity)
        
        # 二级结构倾向（简化）
        helix_tendency = sum(1 for aa in local_seq if aa in 'AEHKLMQR') / len(local_seq) if local_seq else 0
        sheet_tendency = sum(1 for aa in local_seq if aa in 'CFILTVY') / len(local_seq) if local_seq else 0
        loop_tendency = sum(1 for aa in local_seq if aa in 'DGHNPS') / len(local_seq) if local_seq else 0
        
        features.extend([helix_tendency, sheet_tendency, loop_tendency])
        
        # 疏水性梯度
        if position > 0 and position < len(protein_seq) - 1:
            prev_hydro = self.aa_properties.get(protein_seq[position-1], [0]*8)[0]
            curr_hydro = self.aa_properties.get(protein_seq[position], [0]*8)[0]
            next_hydro = self.aa_properties.get(protein_seq[position+1], [0]*8)[0]
            hydro_gradient = (next_hydro - prev_hydro) / 2
        else:
            hydro_gradient = 0
        
        features.append(hydro_gradient)
        
        return features
    
    def prepare_training_data(self, csv_file: str, max_sequences: int = None) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        print(f"📂 读取训练数据: {csv_file}")
        
        df = pd.read_csv(csv_file)
        if max_sequences:
            df = df.head(max_sequences)
        
        print(f"✅ 读取 {len(df)} 条序列")
        
        X = []
        y = []
        
        print("🔧 提取高级特征...")
        processed = 0
        
        for idx, row in df.iterrows():
            protein_seq = row['protein_sequence']
            nucleotide_seq = row['nucleotide_sequence']
            
            # 移除终止密码子
            if nucleotide_seq.endswith(('TAA', 'TAG', 'TGA')):
                nucleotide_seq = nucleotide_seq[:-3]
            
            if len(nucleotide_seq) != len(protein_seq) * 3:
                continue
            
            # 提取ESM2特征（必须使用）
            if not self.esm2_model:
                raise RuntimeError("ESM2模型未初始化，无法达成目标")

            esm2_features = self.extract_esm2_features(protein_seq)
            if esm2_features is None:
                raise RuntimeError(f"序列{idx}的ESM2特征提取失败")
            
            # 为每个位置提取特征
            for pos in range(len(protein_seq)):
                features = self.extract_enhanced_features(protein_seq, pos, esm2_features)
                codon = nucleotide_seq[pos*3:(pos+1)*3]
                
                if len(codon) == 3:
                    X.append(features)
                    y.append(codon)
            
            processed += 1
            if processed % 50 == 0:
                print(f"   已处理 {processed}/{len(df)} 条序列...")
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"✅ 高级特征提取完成:")
        print(f"   样本数: {len(X):,}")
        print(f"   特征数: {X.shape[1]}")
        print(f"   唯一密码子数: {len(set(y))}")
        
        return X, y

    def train_ensemble_model(self, X: np.ndarray, y: np.ndarray):
        """训练集成模型"""
        print("🚀 训练高精度集成模型...")

        # 标签编码
        self.label_encoder = LabelEncoder()
        y_encoded = self.label_encoder.fit_transform(y)

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.15, random_state=42, stratify=y_encoded
        )

        # 特征标准化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        print(f"   训练集: {len(X_train):,} 样本")
        print(f"   测试集: {len(X_test):,} 样本")

        # 定义基础模型
        print("🌲 构建集成模型...")

        # 随机森林
        rf_model = RandomForestClassifier(
            n_estimators=200,
            max_depth=25,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )

        # 极端随机树
        et_model = ExtraTreesClassifier(
            n_estimators=200,
            max_depth=25,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )

        # XGBoost
        xgb_model = xgb.XGBClassifier(
            n_estimators=200,
            max_depth=8,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1,
            eval_metric='mlogloss'
        )

        # 神经网络
        mlp_model = MLPClassifier(
            hidden_layer_sizes=(512, 256, 128),
            activation='relu',
            solver='adam',
            alpha=0.001,
            batch_size=256,
            learning_rate='adaptive',
            max_iter=200,
            random_state=42
        )

        # 创建投票集成
        self.ensemble_model = VotingClassifier(
            estimators=[
                ('rf', rf_model),
                ('et', et_model),
                ('xgb', xgb_model),
                ('mlp', mlp_model)
            ],
            voting='soft',
            n_jobs=-1
        )

        # 训练模型
        print("🎯 开始训练集成模型...")
        start_time = time.time()

        self.ensemble_model.fit(X_train_scaled, y_train)

        training_time = time.time() - start_time

        # 评估模型
        print("📈 评估模型性能...")
        y_pred = self.ensemble_model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)

        print(f"\n✅ 训练完成!")
        print(f"   训练时间: {training_time:.2f} 秒")
        print(f"   测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

        # 交叉验证
        print("🔄 交叉验证...")
        cv_scores = cross_val_score(self.ensemble_model, X_train_scaled, y_train, cv=3, scoring='accuracy')
        print(f"   CV准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

        return accuracy, training_time

    def predict_sequence(self, protein_sequence: str) -> str:
        """预测蛋白质序列对应的DNA序列"""
        if not self.ensemble_model:
            raise ValueError("模型未训练")

        # 提取ESM2特征（必须使用）
        if not self.esm2_model:
            raise RuntimeError("ESM2模型未初始化，无法进行预测")

        esm2_features = self.extract_esm2_features(protein_sequence)
        if esm2_features is None:
            raise RuntimeError("ESM2特征提取失败，无法进行预测")

        predicted_codons = []

        for position in range(len(protein_sequence)):
            features = self.extract_enhanced_features(protein_sequence, position, esm2_features)
            features_scaled = self.scaler.transform([features])

            predicted_idx = self.ensemble_model.predict(features_scaled)[0]
            predicted_codon = self.label_encoder.inverse_transform([predicted_idx])[0]

            predicted_codons.append(predicted_codon)

        return ''.join(predicted_codons)

    def save_model(self, filename: str):
        """保存模型"""
        model_data = {
            'ensemble_model': self.ensemble_model,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'aa_properties': self.aa_properties,
            'aa_groups': self.aa_groups,
            'has_esm2': self.esm2_model is not None,
            'model_type': 'advanced_ensemble_with_esm2',
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S')
        }

        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"💾 高精度模型已保存: {filename}")

def main():
    """主训练函数"""
    print("🧬 高精度密码子预测器训练")
    print("目标：相似度95%+，精度80%+")
    print("=" * 70)

    if not HAS_SKLEARN:
        print("❌ 请安装必要的机器学习库")
        return

    # 初始化预测器
    predictor = AdvancedCodonPredictor()

    try:
        # 准备数据（使用全部数据以达到最高精度）
        X, y = predictor.prepare_training_data('processed_BL21_data.csv', max_sequences=None)

        # 训练模型
        accuracy, training_time = predictor.train_ensemble_model(X, y)

        # 保存模型
        predictor.save_model('advanced_codon_model.pkl')

        # 保存训练信息
        training_info = {
            'model_type': 'advanced_ensemble_with_esm2',
            'accuracy': float(accuracy),
            'training_time': float(training_time),
            'has_esm2': predictor.esm2_model is not None,
            'feature_count': X.shape[1],
            'sample_count': len(X),
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'description': 'Advanced ensemble model with ESM2 features for high-precision codon prediction'
        }

        with open('advanced_model_info.json', 'w') as f:
            json.dump(training_info, f, indent=2)

        print(f"\n🎉 高精度模型训练成功!")
        print(f"   模型文件: advanced_codon_model.pkl")
        print(f"   准确率: {accuracy*100:.2f}%")
        print(f"   特征数: {X.shape[1]}")
        print(f"   ESM2特征: {'✅' if predictor.esm2_model else '❌'}")

        # 测试预测
        print(f"\n🧪 测试预测...")
        test_sequences = [
            "MKLLVVS",
            "MKRISTTITTTITITTGNGAG"
        ]

        for test_seq in test_sequences:
            predicted_dna = predictor.predict_sequence(test_seq)
            print(f"   蛋白质: {test_seq}")
            print(f"   预测DNA: {predicted_dna}")
            print()

        if accuracy >= 0.8:
            print("🎯 已达到80%精度目标!")
        else:
            print(f"⚠️ 当前精度{accuracy*100:.1f}%，建议进一步优化")

    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
