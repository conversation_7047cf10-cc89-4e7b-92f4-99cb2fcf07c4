#!/usr/bin/env python3
"""
完整ESM2+GPU训练脚本
- 使用全部训练样本
- GPU加速
- 显示训练进度
- 及时保存模型检查点
- 支持中断后继续训练
"""

import os
import sys
import time
import json
import pickle
import numpy as np
import pandas as pd
from typing import Dict, Any
import warnings
warnings.filterwarnings('ignore')

# 检查GPU
import torch
print(f"🔧 GPU检查:")
print(f"   CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"   GPU设备: {torch.cuda.get_device_name()}")
    print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    # 设置GPU内存增长
    torch.cuda.empty_cache()

from advanced_codon_predictor import AdvancedCodonPredictor

class ProgressiveTrainer:
    """渐进式训练器，支持检查点和GPU加速"""
    
    def __init__(self):
        self.checkpoint_dir = "checkpoints"
        self.ensure_checkpoint_dir()
        
    def ensure_checkpoint_dir(self):
        """确保检查点目录存在"""
        if not os.path.exists(self.checkpoint_dir):
            os.makedirs(self.checkpoint_dir)
            print(f"📁 创建检查点目录: {self.checkpoint_dir}")
    
    def save_checkpoint(self, predictor, stage: str, accuracy: float, additional_info: Dict[str, Any] = None):
        """保存训练检查点"""
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        checkpoint_name = f"esm2_checkpoint_{stage}_{timestamp}_acc{accuracy:.3f}.pkl"
        checkpoint_path = os.path.join(self.checkpoint_dir, checkpoint_name)
        
        checkpoint_data = {
            'predictor': predictor,
            'stage': stage,
            'accuracy': accuracy,
            'timestamp': timestamp,
            'additional_info': additional_info or {}
        }
        
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint_data, f)
        
        print(f"💾 检查点已保存: {checkpoint_path}")
        return checkpoint_path
    
    def load_latest_checkpoint(self) -> tuple:
        """加载最新的检查点"""
        checkpoint_files = [f for f in os.listdir(self.checkpoint_dir) if f.startswith('esm2_checkpoint_')]
        
        if not checkpoint_files:
            return None, None
        
        # 按时间排序，获取最新的
        checkpoint_files.sort(reverse=True)
        latest_checkpoint = os.path.join(self.checkpoint_dir, checkpoint_files[0])
        
        print(f"📂 加载检查点: {latest_checkpoint}")
        
        with open(latest_checkpoint, 'rb') as f:
            checkpoint_data = pickle.load(f)
        
        return checkpoint_data['predictor'], checkpoint_data
    
    def train_with_progress(self, csv_file: str, resume_from_checkpoint: bool = True):
        """带进度显示的完整训练"""
        print("🚀 开始完整ESM2训练")
        print("=" * 70)
        
        # 检查是否从检查点恢复
        predictor = None
        if resume_from_checkpoint:
            predictor, checkpoint_info = self.load_latest_checkpoint()
            if predictor:
                print(f"✅ 从检查点恢复训练")
                print(f"   阶段: {checkpoint_info['stage']}")
                print(f"   精度: {checkpoint_info['accuracy']:.3f}")
        
        # 如果没有检查点，创建新的预测器
        if predictor is None:
            print("🆕 创建新的ESM2预测器...")
            predictor = AdvancedCodonPredictor()
            
            # 验证ESM2
            if not predictor.esm2_model:
                raise RuntimeError("ESM2模型未成功加载")
            
            print("✅ ESM2模型验证成功")
        
        # 阶段1：数据准备
        print("\n📊 阶段1：数据准备")
        print("-" * 40)
        
        start_time = time.time()
        print("📂 读取完整训练数据...")
        
        # 使用全部数据
        X, y = predictor.prepare_training_data(csv_file, max_sequences=None)
        
        data_prep_time = time.time() - start_time
        
        print(f"✅ 数据准备完成:")
        print(f"   样本数: {len(X):,}")
        print(f"   特征数: {X.shape[1]}")
        print(f"   ESM2特征维度: 1280")
        print(f"   数据准备时间: {data_prep_time:.2f} 秒")
        
        # 保存数据准备检查点
        self.save_checkpoint(predictor, "data_prepared", 0.0, {
            'sample_count': len(X),
            'feature_count': X.shape[1],
            'data_prep_time': data_prep_time
        })
        
        # 阶段2：模型训练
        print("\n🎯 阶段2：模型训练")
        print("-" * 40)
        
        # GPU状态检查（ESM2应该已经在GPU上）
        if torch.cuda.is_available():
            print("🚀 GPU加速状态检查...")
            if predictor.esm2_model and hasattr(predictor, 'device'):
                print(f"   ESM2模型设备: {predictor.device}")
                if predictor.device == "cuda":
                    print("   ✅ ESM2模型已在GPU上运行")
                else:
                    print("   ⚠️ ESM2模型在CPU上运行")
            else:
                print("   ⚠️ 无法确定ESM2设备状态")
        
        train_start_time = time.time()
        
        try:
            # 训练集成模型
            accuracy, training_time = predictor.train_ensemble_model(X, y)
            
            total_training_time = time.time() - train_start_time
            
            print(f"\n✅ 训练完成!")
            print(f"   训练精度: {accuracy:.4f} ({accuracy*100:.2f}%)")
            print(f"   训练时间: {training_time:.2f} 秒")
            print(f"   总时间: {total_training_time:.2f} 秒")
            
            # 保存训练完成检查点
            self.save_checkpoint(predictor, "training_completed", accuracy, {
                'training_time': training_time,
                'total_time': total_training_time,
                'sample_count': len(X),
                'feature_count': X.shape[1]
            })
            
        except Exception as e:
            print(f"❌ 训练过程中出错: {e}")
            # 保存错误状态检查点
            self.save_checkpoint(predictor, "training_error", 0.0, {
                'error': str(e),
                'sample_count': len(X)
            })
            raise
        
        # 阶段3：模型保存和验证
        print("\n💾 阶段3：模型保存和验证")
        print("-" * 40)
        
        # 保存最终模型
        final_model_name = f'esm2_final_model_acc{accuracy:.3f}.pkl'
        predictor.save_model(final_model_name)
        
        # 保存训练信息
        training_info = {
            'model_type': 'esm2_ensemble_full_training',
            'accuracy': float(accuracy),
            'training_time': float(training_time),
            'total_time': float(total_training_time),
            'data_prep_time': float(data_prep_time),
            'feature_count': X.shape[1],
            'sample_count': len(X),
            'esm2_enabled': True,
            'gpu_used': torch.cuda.is_available(),
            'training_sequences': len(pd.read_csv(csv_file)),
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'description': 'Full ESM2-powered ensemble model with GPU acceleration'
        }
        
        info_filename = f'esm2_full_training_info_acc{accuracy:.3f}.json'
        with open(info_filename, 'w') as f:
            json.dump(training_info, f, indent=2)
        
        print(f"📋 训练信息已保存: {info_filename}")
        
        # 快速验证
        print("\n🧪 快速验证...")
        test_sequences = ["MKLLVVS", "MKRISTTITTTITITTGNGAG"]
        
        for i, test_seq in enumerate(test_sequences, 1):
            try:
                pred_start = time.time()
                predicted_dna = predictor.predict_sequence(test_seq)
                pred_time = time.time() - pred_start
                
                print(f"   测试{i}: {test_seq}")
                print(f"   预测: {predicted_dna}")
                print(f"   时间: {pred_time:.3f}s")
                print(f"   长度: ✅" if len(predicted_dna) == len(test_seq) * 3 else "❌")
                
            except Exception as e:
                print(f"   测试{i}失败: {e}")
        
        # 最终评估
        print(f"\n🎯 最终评估:")
        print("=" * 50)
        
        if accuracy >= 0.8:
            print(f"🎉 精度目标达成: {accuracy*100:.1f}% ≥ 80%")
            status = "SUCCESS"
        else:
            print(f"⚠️ 精度目标未达成: {accuracy*100:.1f}% < 80%")
            status = "PARTIAL_SUCCESS"
        
        print(f"📊 训练统计:")
        print(f"   最终模型: {final_model_name}")
        print(f"   训练样本: {len(X):,}")
        print(f"   特征维度: {X.shape[1]}")
        print(f"   ESM2特征: ✅ 1280维")
        print(f"   GPU加速: {'✅' if torch.cuda.is_available() else '❌'}")
        print(f"   总训练时间: {total_training_time:.1f}秒")
        
        print(f"\n📋 下一步:")
        print(f"   1. 运行相似度测试: python test_advanced_model.py")
        print(f"   2. 如需继续优化，可调整模型参数重新训练")
        
        # 保存最终检查点
        final_checkpoint = self.save_checkpoint(predictor, "final", accuracy, {
            'status': status,
            'final_model': final_model_name,
            'training_info': info_filename
        })
        
        return predictor, accuracy, final_model_name

def main():
    """主函数"""
    print("🧬 ESM2完整训练 - GPU加速版")
    print("目标：95%相似度，80%精度")
    print("=" * 70)
    
    trainer = ProgressiveTrainer()
    
    try:
        # 开始训练
        predictor, accuracy, model_file = trainer.train_with_progress(
            'processed_BL21_data.csv',
            resume_from_checkpoint=True
        )
        
        if accuracy >= 0.8:
            print(f"\n🎉 训练成功完成！")
            print(f"   最终精度: {accuracy*100:.1f}%")
            print(f"   模型文件: {model_file}")
            return 0
        else:
            print(f"\n⚠️ 训练完成但精度不足")
            print(f"   当前精度: {accuracy*100:.1f}%")
            print(f"   建议调整参数重新训练")
            return 1
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 训练被用户中断")
        print(f"   检查点已保存，可使用resume_from_checkpoint=True恢复")
        return 2
        
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return 3

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
