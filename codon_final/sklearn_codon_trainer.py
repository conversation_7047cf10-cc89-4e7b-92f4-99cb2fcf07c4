#!/usr/bin/env python3
"""
使用sklearn训练真正的机器学习密码子预测模型
目标：让预测序列与训练数据完全一致
"""

import pandas as pd
import numpy as np
import pickle
import json
import time
from typing import List, Tuple
import warnings
warnings.filterwarnings('ignore')

from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from sklearn.model_selection import cross_val_score

class SklearnCodonPredictor:
    """基于sklearn的密码子预测器"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.label_encoder = None
        
        # 氨基酸属性字典
        self.aa_properties = {
            'A': [1.8, 0, 0, 0, 0],   'R': [-4.5, 1, 1, 0, 0], 'N': [-3.5, 1, 0, 0, 0],
            'D': [-3.5, 1, 0, 1, 0], 'C': [2.5, 0, 0, 0, 0],  'Q': [-3.5, 1, 0, 0, 0],
            'E': [-3.5, 1, 0, 1, 0], 'G': [-0.4, 0, 0, 0, 0], 'H': [-3.2, 1, 1, 0, 1],
            'I': [4.5, 0, 0, 0, 0],  'L': [3.8, 0, 0, 0, 0],  'K': [-3.9, 1, 1, 0, 0],
            'M': [1.9, 0, 0, 0, 0],  'F': [2.8, 0, 0, 0, 1],  'P': [-1.6, 0, 0, 0, 0],
            'S': [-0.8, 1, 0, 0, 0], 'T': [-0.7, 1, 0, 0, 0], 'W': [-0.9, 0, 0, 0, 1],
            'Y': [-1.3, 1, 0, 0, 1], 'V': [4.2, 0, 0, 0, 0],  '*': [0, 0, 0, 0, 0]
        }
        
        # 氨基酸到数字的映射
        self.aa_to_num = {aa: i for i, aa in enumerate('ACDEFGHIKLMNPQRSTVWY*')}
        
        print("🧬 sklearn密码子预测器初始化完成")
    
    def extract_features(self, protein_seq: str, position: int) -> List[float]:
        """提取特征向量"""
        features = []
        
        # 1. 当前氨基酸特征 (5个)
        aa = protein_seq[position] if position < len(protein_seq) else '*'
        aa_props = self.aa_properties.get(aa, [0] * 5)
        features.extend(aa_props)
        
        # 2. 当前氨基酸编码 (1个)
        aa_num = self.aa_to_num.get(aa, 20)
        features.append(aa_num)
        
        # 3. 前一个氨基酸特征 (6个)
        prev_aa = protein_seq[position - 1] if position > 0 else '*'
        prev_props = self.aa_properties.get(prev_aa, [0] * 5)
        features.extend(prev_props)
        features.append(self.aa_to_num.get(prev_aa, 20))
        
        # 4. 后一个氨基酸特征 (6个)
        next_aa = protein_seq[position + 1] if position + 1 < len(protein_seq) else '*'
        next_props = self.aa_properties.get(next_aa, [0] * 5)
        features.extend(next_props)
        features.append(self.aa_to_num.get(next_aa, 20))
        
        # 5. 位置特征 (3个)
        rel_position = position / len(protein_seq)
        features.append(rel_position)
        features.append(position)  # 绝对位置
        features.append(len(protein_seq))  # 序列长度
        
        # 6. 上下文窗口特征 (10个)
        window_size = 5
        start_pos = max(0, position - window_size // 2)
        end_pos = min(len(protein_seq), position + window_size // 2 + 1)
        window_seq = protein_seq[start_pos:end_pos]
        
        # 窗口内氨基酸类型统计
        hydrophobic = sum(1 for a in window_seq if a in 'AILMFPWV')
        polar = sum(1 for a in window_seq if a in 'NQST')
        positive = sum(1 for a in window_seq if a in 'RK')
        negative = sum(1 for a in window_seq if a in 'DE')
        aromatic = sum(1 for a in window_seq if a in 'FWY')
        
        window_len = len(window_seq)
        features.extend([
            hydrophobic / window_len if window_len > 0 else 0,
            polar / window_len if window_len > 0 else 0,
            positive / window_len if window_len > 0 else 0,
            negative / window_len if window_len > 0 else 0,
            aromatic / window_len if window_len > 0 else 0,
            window_len,
            start_pos,
            end_pos,
            position - start_pos,  # 在窗口中的相对位置
            end_pos - position     # 到窗口末尾的距离
        ])
        
        return features
    
    def prepare_data(self, csv_file: str, max_sequences: int = None) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        print(f"📂 读取训练数据: {csv_file}")
        
        df = pd.read_csv(csv_file)
        if max_sequences:
            df = df.head(max_sequences)
            print(f"📊 限制处理序列数: {max_sequences}")
        
        print(f"✅ 读取 {len(df)} 条序列")
        
        X = []
        y = []
        
        print("🔧 提取特征和标签...")
        processed = 0
        
        for idx, row in df.iterrows():
            protein_seq = row['protein_sequence']
            nucleotide_seq = row['nucleotide_sequence']
            
            # 移除终止密码子
            if nucleotide_seq.endswith(('TAA', 'TAG', 'TGA')):
                nucleotide_seq = nucleotide_seq[:-3]
            
            # 验证长度
            if len(nucleotide_seq) != len(protein_seq) * 3:
                continue
            
            # 为每个位置提取特征
            for pos in range(len(protein_seq)):
                features = self.extract_features(protein_seq, pos)
                codon = nucleotide_seq[pos*3:(pos+1)*3]
                
                if len(codon) == 3:
                    X.append(features)
                    y.append(codon)
            
            processed += 1
            if processed % 100 == 0:
                print(f"   已处理 {processed}/{len(df)} 条序列...")
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"✅ 数据准备完成:")
        print(f"   样本数: {len(X):,}")
        print(f"   特征数: {X.shape[1]}")
        print(f"   唯一密码子数: {len(set(y))}")
        
        return X, y
    
    def train(self, csv_file: str, max_sequences: int = 1000):
        """训练模型"""
        print("🚀 开始训练sklearn密码子预测模型")
        print("=" * 60)
        
        # 准备数据
        X, y = self.prepare_data(csv_file, max_sequences)
        
        # 标签编码
        print("🔧 编码密码子标签...")
        self.label_encoder = LabelEncoder()
        y_encoded = self.label_encoder.fit_transform(y)
        
        print(f"   密码子类别: {len(self.label_encoder.classes_)}")
        print(f"   密码子列表: {list(self.label_encoder.classes_)[:10]}...")
        
        # 数据分割
        print("📊 分割训练/测试数据...")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        print(f"   训练集: {len(X_train):,} 样本")
        print(f"   测试集: {len(X_test):,} 样本")
        
        # 特征标准化
        print("🔧 标准化特征...")
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 训练随机森林模型
        print("🌲 训练随机森林模型...")
        start_time = time.time()
        
        self.model = RandomForestClassifier(
            n_estimators=100,  # 减少树的数量以加快训练
            max_depth=15,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42,
            n_jobs=-1,
            verbose=1
        )
        
        self.model.fit(X_train_scaled, y_train)
        
        training_time = time.time() - start_time
        
        # 评估模型
        print("📈 评估模型性能...")
        y_pred = self.model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"\n✅ 训练完成!")
        print(f"   训练时间: {training_time:.2f} 秒")
        print(f"   测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        # 交叉验证
        print("🔄 进行交叉验证...")
        cv_scores = cross_val_score(self.model, X_train_scaled, y_train, cv=3, scoring='accuracy')
        print(f"   交叉验证准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        return accuracy, training_time
    
    def predict_sequence(self, protein_sequence: str) -> str:
        """预测蛋白质序列对应的DNA序列"""
        if not self.model:
            raise ValueError("模型未训练")
        
        predicted_codons = []
        
        for position in range(len(protein_sequence)):
            features = self.extract_features(protein_sequence, position)
            features_scaled = self.scaler.transform([features])
            
            predicted_idx = self.model.predict(features_scaled)[0]
            predicted_codon = self.label_encoder.inverse_transform([predicted_idx])[0]
            
            predicted_codons.append(predicted_codon)
        
        return ''.join(predicted_codons)
    
    def save_model(self, filename: str):
        """保存模型"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'aa_properties': self.aa_properties,
            'aa_to_num': self.aa_to_num,
            'model_type': 'sklearn_random_forest',
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"💾 模型已保存: {filename}")

def main():
    """主函数"""
    print("🧬 使用sklearn训练密码子预测模型")
    print("目标: 让预测序列与训练数据一致")
    print("=" * 70)
    
    # 初始化预测器
    predictor = SklearnCodonPredictor()
    
    try:
        # 训练模型（先用较小的数据集测试）
        accuracy, training_time = predictor.train('processed_BL21_data.csv', max_sequences=500)
        
        # 保存模型
        predictor.save_model('sklearn_codon_model.pkl')
        
        # 保存训练信息
        training_info = {
            'model_type': 'sklearn_random_forest',
            'accuracy': float(accuracy),
            'training_time': float(training_time),
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'description': 'sklearn RandomForest model trained to match training data'
        }
        
        with open('sklearn_model_info.json', 'w') as f:
            json.dump(training_info, f, indent=2)
        
        print(f"\n🎉 训练成功!")
        print(f"   模型文件: sklearn_codon_model.pkl")
        print(f"   准确率: {accuracy*100:.2f}%")
        
        # 测试预测
        print(f"\n🧪 测试预测...")
        test_sequences = [
            "MKLLVVS",
            "MKRISTTITTTITITTGNGAG"
        ]
        
        for test_seq in test_sequences:
            predicted_dna = predictor.predict_sequence(test_seq)
            print(f"   蛋白质: {test_seq}")
            print(f"   预测DNA: {predicted_dna}")
            print()
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
