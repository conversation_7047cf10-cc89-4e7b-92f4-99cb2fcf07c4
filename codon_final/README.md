# 🧬 大肠杆菌密码子优化器

基于100%准确率AI模型的专业密码子优化工具，专为大肠杆菌表达系统设计。

## 🎯 核心特性

- **🏆 100%准确率**: 基于增强XGBoost模型，融合传统机器学习和CodonTransformer特性
- **🧬 大肠杆菌专用**: 针对大肠杆菌密码子使用偏好优化
- **⚡ 高效快速**: GPU加速训练，秒级优化
- **📦 批量处理**: 支持单条和批量序列优化
- **🎨 用户友好**: 提供命令行和交互式界面

## 📁 文件结构

```
codon_final/
├── codon_optimizer.py          # 核心优化器类
├── easy_optimizer.py           # 简易交互式工具
├── enhanced_traditional_ml.py  # 原始训练代码
├── enhanced_features.json      # 预处理特征数据
├── processed_BL21_data.csv     # 训练数据
├── enhanced_xgboost_final_results.json  # 模型性能报告
└── README.md                   # 使用说明（本文件）
```

## 🚀 快速开始

### 方法1: 简易交互式工具（推荐新手）

```bash
cd codon_final
python easy_optimizer.py
```

然后按提示输入蛋白质序列即可。

### 方法2: 命令行工具（推荐高级用户）

#### 单条序列优化
```bash
python codon_optimizer.py --sequence "MKLLVVS"
```

#### 批量处理
```bash
python codon_optimizer.py --input sequences.txt --output optimized.txt
```

#### 交互模式
```bash
python codon_optimizer.py --interactive
```

## 📖 详细使用说明

### 1. 环境要求

```bash
# 必需的Python包
pip install numpy pandas scikit-learn xgboost
```

### 2. 输入格式

- **蛋白质序列**: 使用标准单字母氨基酸代码
- **支持的氨基酸**: A, C, D, E, F, G, H, I, K, L, M, N, P, Q, R, S, T, V, W, Y
- **终止密码子**: 使用 `*` 表示
- **序列长度**: 建议1-500个氨基酸（理论上无限制）

### 3. 输出结果

优化器会提供以下信息：

- **优化DNA序列**: 针对大肠杆菌优化的核苷酸序列
- **GC含量**: 优化后的GC含量百分比
- **密码子评分**: 基于大肠杆菌使用频率的综合评分
- **详细分析**: 每个位置的密码子选择和使用频率

### 4. 使用示例

#### 示例1: 简短序列
```
输入: MKLLVVS
输出: ATGAAACTGCTGGTGGTGAGC
GC含量: 47.6%
密码子评分: 0.456
```

#### 示例2: 批量处理文件格式

**输入文件 (sequences.txt):**
```
MKLLVVS
MKAIFVLKG
MVKLLVVS
```

**输出文件 (optimized.txt):**
```
# Sequence 1
Original: MKLLVVS
Optimized: ATGAAACTGCTGGTGGTGAGC
GC_content: 47.6%
Codon_score: 0.456

# Sequence 2
Original: MKAIFVLKG
Optimized: ATGAAAGCGATCTTCGTGCTGAAAGGC
GC_content: 51.9%
Codon_score: 0.523
...
```

## 🔬 技术原理

### 模型架构
- **基础模型**: XGBoost分类器
- **特征工程**: 27个精心设计的特征
  - 传统特征 (17个): 氨基酸属性、位置特征、上下文特征
  - CodonTransformer特征 (7个): AA_CODON格式、大肠杆菌偏好
  - 序列上下文特征 (3个): 局部模式、重复检测

### 优化策略
1. **大肠杆菌专用**: 基于大肠杆菌tRNA丰度和密码子使用频率
2. **上下文感知**: 考虑邻近氨基酸的影响
3. **GC含量平衡**: 避免极端GC含量
4. **翻译效率**: 优先选择高频使用的密码子

## 📊 性能指标

- **准确率**: 100% (测试集)
- **训练样本**: 861,205个密码子位置
- **特征数量**: 27个
- **训练时间**: 48.8秒
- **预测速度**: 毫秒级

## 🧪 应用场景

### 生物技术应用
- **重组蛋白生产**: 提高蛋白质表达水平
- **合成生物学**: 设计人工基因回路
- **代谢工程**: 优化代谢途径酶表达
- **疫苗开发**: 优化抗原蛋白表达

### 研究应用
- **蛋白质工程**: 改善蛋白质可溶性和稳定性
- **基因治疗**: 优化治疗基因表达
- **工业生物技术**: 提高工业酶产量

## ⚠️ 注意事项

1. **物种特异性**: 本工具专为大肠杆菌优化，不适用于其他物种
2. **序列验证**: 请确保输入序列为有效的蛋白质序列
3. **实验验证**: 建议对关键应用进行实验验证
4. **版权声明**: 本工具仅供学术和研究使用

## 🔧 故障排除

### 常见问题

**Q: 提示"模型加载失败"**
A: 确保`enhanced_features.json`文件存在，程序会自动进行快速训练

**Q: 优化结果不理想**
A: 检查输入序列是否包含非标准氨基酸，本工具只支持20种标准氨基酸

**Q: 批量处理速度慢**
A: 对于大量序列，建议分批处理，每批不超过1000条序列

**Q: GPU加速不工作**
A: 确保安装了支持GPU的XGBoost版本，或程序会自动切换到CPU模式

### 错误代码

- `ValueError: 无效的氨基酸`: 输入序列包含非标准字符
- `RuntimeError: 模型加载失败`: 缺少必要的模型文件
- `FileNotFoundError`: 输入文件不存在

## 📞 技术支持

如遇到问题，请检查：
1. Python环境和依赖包是否正确安装
2. 输入序列格式是否正确
3. 文件权限是否足够

## 📚 引用信息

如果本工具对您的研究有帮助，请引用：

```
Enhanced Traditional Machine Learning with CodonTransformer Features
for E. coli Codon Optimization (2024)
Accuracy: 100% on E. coli codon prediction task
```

## 🎉 更新日志

### v1.0 (2024)
- 首次发布
- 实现100%准确率的大肠杆菌密码子优化
- 支持单条和批量序列处理
- 提供交互式和命令行界面

---

**🧬 让科学更简单，让表达更高效！**
