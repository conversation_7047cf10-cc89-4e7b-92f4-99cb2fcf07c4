#!/usr/bin/env python3
"""
使用ESM2训练高精度密码子预测模型
目标：95%相似度，80%精度
"""

import os
import sys
import time
from advanced_codon_predictor import AdvancedCodonPredictor

def main():
    """主训练函数"""
    print("🧬 ESM2高精度密码子预测器训练")
    print("目标：相似度95%+，精度80%+")
    print("强制使用ESM2特征")
    print("=" * 70)
    
    try:
        # 初始化预测器（强制使用ESM2）
        print("🚀 初始化高精度预测器...")
        predictor = AdvancedCodonPredictor()
        
        # 验证ESM2已加载
        if not predictor.esm2_model:
            raise RuntimeError("ESM2模型未成功加载，无法继续")
        
        print("✅ ESM2模型验证成功")
        
        # 先用较小的数据集进行测试（100条序列）
        print("\n📊 开始训练（使用100条序列进行快速测试）...")
        X, y = predictor.prepare_training_data('processed_BL21_data.csv', max_sequences=100)
        
        print(f"✅ 数据准备完成:")
        print(f"   样本数: {len(X):,}")
        print(f"   特征数: {X.shape[1]}")
        print(f"   ESM2特征维度: 1280")
        
        # 训练模型
        print("\n🎯 开始训练集成模型...")
        accuracy, training_time = predictor.train_ensemble_model(X, y)
        
        # 保存模型
        model_filename = 'esm2_codon_model.pkl'
        predictor.save_model(model_filename)
        
        # 保存训练信息
        import json
        training_info = {
            'model_type': 'esm2_ensemble_codon_predictor',
            'accuracy': float(accuracy),
            'training_time': float(training_time),
            'feature_count': X.shape[1],
            'sample_count': len(X),
            'esm2_enabled': True,
            'training_sequences': 100,
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'description': 'ESM2-powered ensemble model for high-precision codon prediction'
        }
        
        with open('esm2_model_info.json', 'w') as f:
            json.dump(training_info, f, indent=2)
        
        print(f"\n🎉 ESM2模型训练成功!")
        print(f"   模型文件: {model_filename}")
        print(f"   准确率: {accuracy*100:.2f}%")
        print(f"   特征数: {X.shape[1]}")
        print(f"   ESM2特征: ✅ 已启用")
        print(f"   训练时间: {training_time:.2f} 秒")
        
        # 快速测试预测
        print(f"\n🧪 快速测试预测...")
        test_sequences = [
            "MKLLVVS",
            "MKRISTTITTTITITTGNGAG"
        ]
        
        for test_seq in test_sequences:
            try:
                predicted_dna = predictor.predict_sequence(test_seq)
                print(f"   蛋白质: {test_seq}")
                print(f"   预测DNA: {predicted_dna}")
                print(f"   长度验证: {len(predicted_dna) == len(test_seq) * 3}")
                print()
            except Exception as e:
                print(f"   ❌ 预测失败: {e}")
        
        # 评估目标达成情况
        print(f"🎯 目标评估:")
        if accuracy >= 0.8:
            print(f"   ✅ 精度目标达成: {accuracy*100:.1f}% ≥ 80%")
        else:
            print(f"   ⚠️ 精度目标未达成: {accuracy*100:.1f}% < 80%")
            print(f"   💡 建议：增加训练数据量或调整模型参数")
        
        print(f"\n📋 下一步:")
        print(f"   1. 运行 'python test_advanced_model.py' 测试相似度")
        print(f"   2. 如果效果良好，可增加训练数据量到全部3418条序列")
        print(f"   3. 使用 'python train_full_esm2_model.py' 进行完整训练")
        
        return accuracy
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return 0.0

if __name__ == "__main__":
    accuracy = main()
    
    if accuracy >= 0.8:
        print(f"\n🎉 训练成功！精度达到 {accuracy*100:.1f}%")
        sys.exit(0)
    else:
        print(f"\n⚠️ 训练完成但精度不足：{accuracy*100:.1f}%")
        sys.exit(1)
