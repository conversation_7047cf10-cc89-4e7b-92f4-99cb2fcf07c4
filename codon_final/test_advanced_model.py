#!/usr/bin/env python3
"""
测试高精度密码子预测模型
验证是否达到95%相似度和80%精度目标
"""

import pickle
import pandas as pd
import numpy as np
import time
from typing import List, Tuple

class AdvancedModelTester:
    """高精度模型测试器"""
    
    def __init__(self, model_path="advanced_codon_model.pkl"):
        self.model_path = model_path
        self.predictor = None
        self.load_model()
    
    def load_model(self):
        """加载训练好的高精度模型"""
        try:
            with open(self.model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            # 重建预测器
            from advanced_codon_predictor import AdvancedCodonPredictor
            self.predictor = AdvancedCodonPredictor()
            self.predictor.ensemble_model = model_data['ensemble_model']
            self.predictor.scaler = model_data['scaler']
            self.predictor.label_encoder = model_data['label_encoder']
            self.predictor.aa_properties = model_data['aa_properties']
            self.predictor.aa_groups = model_data['aa_groups']
            
            print(f"✅ 高精度模型加载成功")
            print(f"   模型类型: {model_data.get('model_type', 'unknown')}")
            print(f"   训练日期: {model_data.get('training_date', 'unknown')}")
            print(f"   ESM2特征: {'✅' if model_data.get('has_esm2', False) else '❌'}")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def calculate_similarity(self, seq1: str, seq2: str) -> float:
        """计算两个序列的相似度"""
        if len(seq1) != len(seq2):
            return 0.0
        
        matches = sum(1 for a, b in zip(seq1, seq2) if a == b)
        return matches / len(seq1)
    
    def test_on_training_data(self, csv_file: str, sample_size: int = 100) -> Tuple[float, float, List[Dict]]:
        """在训练数据上测试模型性能"""
        print(f"🧪 在训练数据上测试模型性能")
        print(f"   测试样本数: {sample_size}")
        print("=" * 60)
        
        # 读取训练数据
        df = pd.read_csv(csv_file)
        test_df = df.sample(n=min(sample_size, len(df)), random_state=42)
        
        results = []
        total_similarity = 0
        total_accuracy = 0
        sequence_matches = 0
        
        print("🔍 逐序列测试...")
        
        for idx, row in test_df.iterrows():
            protein_seq = row['protein_sequence']
            original_dna = row['nucleotide_sequence']
            
            # 移除终止密码子
            if original_dna.endswith(('TAA', 'TAG', 'TGA')):
                original_dna = original_dna[:-3]
            
            try:
                # 预测
                start_time = time.time()
                predicted_dna = self.predictor.predict_sequence(protein_seq)
                prediction_time = time.time() - start_time
                
                # 计算相似度
                similarity = self.calculate_similarity(original_dna, predicted_dna)
                
                # 计算位置级准确率
                position_matches = 0
                total_positions = len(protein_seq)
                
                if len(original_dna) == len(predicted_dna):
                    for pos in range(0, len(original_dna), 3):
                        orig_codon = original_dna[pos:pos+3]
                        pred_codon = predicted_dna[pos:pos+3]
                        if orig_codon == pred_codon:
                            position_matches += 1
                
                position_accuracy = position_matches / total_positions if total_positions > 0 else 0
                
                # 序列完全匹配
                perfect_match = (original_dna == predicted_dna)
                if perfect_match:
                    sequence_matches += 1
                
                # 记录结果
                result = {
                    'sequence_id': idx,
                    'protein_length': len(protein_seq),
                    'similarity': similarity,
                    'position_accuracy': position_accuracy,
                    'perfect_match': perfect_match,
                    'prediction_time': prediction_time,
                    'protein_seq': protein_seq,
                    'original_dna': original_dna,
                    'predicted_dna': predicted_dna
                }
                results.append(result)
                
                total_similarity += similarity
                total_accuracy += position_accuracy
                
                # 显示进度
                if len(results) % 10 == 0:
                    avg_sim = total_similarity / len(results)
                    avg_acc = total_accuracy / len(results)
                    print(f"   已测试 {len(results)}/{len(test_df)} - 平均相似度: {avg_sim:.1%}, 平均精度: {avg_acc:.1%}")
                
            except Exception as e:
                print(f"❌ 序列 {idx} 预测失败: {e}")
                continue
        
        # 计算总体统计
        avg_similarity = total_similarity / len(results) if results else 0
        avg_accuracy = total_accuracy / len(results) if results else 0
        perfect_match_rate = sequence_matches / len(results) if results else 0
        
        return avg_similarity, avg_accuracy, results
    
    def analyze_results(self, results: List[Dict], similarity_threshold: float = 0.95, accuracy_threshold: float = 0.80):
        """分析测试结果"""
        print(f"\n📊 详细结果分析")
        print("=" * 60)
        
        if not results:
            print("❌ 没有有效的测试结果")
            return
        
        # 基本统计
        similarities = [r['similarity'] for r in results]
        accuracies = [r['position_accuracy'] for r in results]
        times = [r['prediction_time'] for r in results]
        
        avg_similarity = np.mean(similarities)
        avg_accuracy = np.mean(accuracies)
        avg_time = np.mean(times)
        
        print(f"📈 总体性能:")
        print(f"   测试序列数: {len(results)}")
        print(f"   平均相似度: {avg_similarity:.1%}")
        print(f"   平均精度: {avg_accuracy:.1%}")
        print(f"   完全匹配率: {sum(r['perfect_match'] for r in results) / len(results):.1%}")
        print(f"   平均预测时间: {avg_time:.3f} 秒")
        
        # 目标达成情况
        print(f"\n🎯 目标达成情况:")
        similarity_achieved = avg_similarity >= similarity_threshold
        accuracy_achieved = avg_accuracy >= accuracy_threshold
        
        print(f"   相似度目标 (≥{similarity_threshold:.0%}): {'✅ 达成' if similarity_achieved else '❌ 未达成'} ({avg_similarity:.1%})")
        print(f"   精度目标 (≥{accuracy_threshold:.0%}): {'✅ 达成' if accuracy_achieved else '❌ 未达成'} ({avg_accuracy:.1%})")
        
        # 分布分析
        print(f"\n📊 性能分布:")
        
        # 相似度分布
        sim_ranges = [(0.95, 1.0), (0.90, 0.95), (0.80, 0.90), (0.70, 0.80), (0.0, 0.70)]
        for min_sim, max_sim in sim_ranges:
            count = sum(1 for s in similarities if min_sim <= s < max_sim)
            percentage = count / len(similarities) * 100
            print(f"   相似度 {min_sim:.0%}-{max_sim:.0%}: {count} 序列 ({percentage:.1f}%)")
        
        # 精度分布
        acc_ranges = [(0.90, 1.0), (0.80, 0.90), (0.70, 0.80), (0.60, 0.70), (0.0, 0.60)]
        for min_acc, max_acc in acc_ranges:
            count = sum(1 for a in accuracies if min_acc <= a < max_acc)
            percentage = count / len(accuracies) * 100
            print(f"   精度 {min_acc:.0%}-{max_acc:.0%}: {count} 序列 ({percentage:.1f}%)")
        
        # 最佳和最差案例
        print(f"\n🏆 最佳案例 (相似度最高的3个):")
        best_cases = sorted(results, key=lambda x: x['similarity'], reverse=True)[:3]
        for i, case in enumerate(best_cases, 1):
            print(f"   {i}. 相似度: {case['similarity']:.1%}, 精度: {case['position_accuracy']:.1%}")
            print(f"      蛋白质: {case['protein_seq'][:30]}{'...' if len(case['protein_seq']) > 30 else ''}")
        
        print(f"\n⚠️ 最差案例 (相似度最低的3个):")
        worst_cases = sorted(results, key=lambda x: x['similarity'])[:3]
        for i, case in enumerate(worst_cases, 1):
            print(f"   {i}. 相似度: {case['similarity']:.1%}, 精度: {case['position_accuracy']:.1%}")
            print(f"      蛋白质: {case['protein_seq'][:30]}{'...' if len(case['protein_seq']) > 30 else ''}")
        
        # 改进建议
        print(f"\n💡 改进建议:")
        if avg_similarity < similarity_threshold:
            print(f"   - 相似度未达标，建议增加训练数据或改进特征工程")
        if avg_accuracy < accuracy_threshold:
            print(f"   - 精度未达标，建议使用更复杂的模型或调整超参数")
        if avg_time > 1.0:
            print(f"   - 预测速度较慢，建议优化模型结构或使用模型压缩")
        
        return avg_similarity, avg_accuracy

def main():
    """主测试函数"""
    print("🧬 高精度密码子预测模型测试")
    print("目标验证：相似度95%+，精度80%+")
    print("=" * 70)
    
    try:
        # 初始化测试器
        tester = AdvancedModelTester()
        
        # 在训练数据上测试
        similarity, accuracy, results = tester.test_on_training_data('processed_BL21_data.csv', sample_size=50)
        
        # 分析结果
        final_similarity, final_accuracy = tester.analyze_results(results)
        
        # 最终结论
        print(f"\n🎯 最终结论:")
        print("=" * 40)
        
        if final_similarity >= 0.95 and final_accuracy >= 0.80:
            print("🎉 恭喜！模型已达到所有目标:")
            print(f"   ✅ 相似度: {final_similarity:.1%} (≥95%)")
            print(f"   ✅ 精度: {final_accuracy:.1%} (≥80%)")
        elif final_similarity >= 0.95:
            print("⚠️ 部分达标:")
            print(f"   ✅ 相似度: {final_similarity:.1%} (≥95%)")
            print(f"   ❌ 精度: {final_accuracy:.1%} (<80%)")
        elif final_accuracy >= 0.80:
            print("⚠️ 部分达标:")
            print(f"   ❌ 相似度: {final_similarity:.1%} (<95%)")
            print(f"   ✅ 精度: {final_accuracy:.1%} (≥80%)")
        else:
            print("❌ 未达标，需要进一步优化:")
            print(f"   ❌ 相似度: {final_similarity:.1%} (<95%)")
            print(f"   ❌ 精度: {final_accuracy:.1%} (<80%)")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
