#!/usr/bin/env python3
"""
训练并保存最佳密码子优化模型
基于100%准确率的增强XGBoost模型
"""

import json
import numpy as np
import pandas as pd
import pickle
import time
import warnings
warnings.filterwarnings('ignore')

try:
    import xgboost as xgb
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score
    HAS_DEPS = True
except ImportError as e:
    print(f"❌ 缺少依赖包: {e}")
    print("请安装: pip install numpy pandas scikit-learn xgboost")
    HAS_DEPS = False

def train_best_model():
    """训练并保存最佳模型"""
    if not HAS_DEPS:
        print("❌ 无法训练模型，缺少必要依赖")
        return False
    
    print("🚀 开始训练最佳密码子优化模型")
    print("=" * 50)
    
    try:
        # 加载预处理的特征
        print("📂 加载训练数据...")
        with open('enhanced_features.json', 'r') as f:
            feature_data = json.load(f)
        
        X = np.array(feature_data['X'])
        y = np.array(feature_data['y'])
        
        print(f"✅ 数据加载成功: {len(X):,} 样本, {X.shape[1]} 特征")
        
        # 标签编码
        print("🔧 预处理数据...")
        label_encoder = LabelEncoder()
        y_encoded = label_encoder.fit_transform(y)
        
        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        print(f"📊 训练集: {len(X_train):,} 样本")
        print(f"📊 测试集: {len(X_test):,} 样本")
        print(f"📊 类别数: {len(np.unique(y_encoded))}")
        
        # 最佳XGBoost参数（基于100%准确率配置）
        best_params = {
            'objective': 'multi:softmax',
            'num_class': len(np.unique(y_encoded)),
            'max_depth': 10,
            'learning_rate': 0.08,
            'n_estimators': 800,
            'subsample': 0.85,
            'colsample_bytree': 0.85,
            'colsample_bylevel': 0.8,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'random_state': 42,
            'n_jobs': -1,
            'eval_metric': 'mlogloss',
            'early_stopping_rounds': 50,
            'tree_method': 'gpu_hist',  # GPU加速
            'gpu_id': 0
        }
        
        print("🎯 模型配置:")
        print(f"   算法: XGBoost分类器")
        print(f"   树深度: {best_params['max_depth']}")
        print(f"   学习率: {best_params['learning_rate']}")
        print(f"   树数量: {best_params['n_estimators']}")
        print(f"   GPU加速: 启用")
        
        # 训练模型
        print(f"\n🏃 开始训练最佳模型...")
        start_time = time.time()
        
        model = xgb.XGBClassifier(**best_params)
        model.fit(
            X_train_scaled, y_train,
            eval_set=[(X_train_scaled, y_train), (X_test_scaled, y_test)],
            verbose=False
        )
        
        train_time = time.time() - start_time
        
        # 评估模型
        print(f"\n📈 评估模型性能...")
        y_pred = model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"✅ 训练完成!")
        print(f"   训练时间: {train_time:.2f} 秒")
        print(f"   测试准确率: {accuracy:.4f} ({accuracy*100:.1f}%)")
        
        # 保存完整模型
        model_data = {
            'model': model,
            'scaler': scaler,
            'label_encoder': label_encoder,
            'feature_names': [
                'AA_hydrophobicity', 'AA_polarity', 'AA_positive', 'AA_negative', 'AA_aromatic',
                'prev_hydrophobicity', 'prev_polarity', 'prev_positive', 'prev_negative', 'prev_aromatic',
                'next_hydrophobicity', 'next_polarity', 'next_positive', 'next_negative', 'next_aromatic',
                'rel_position', 'seq_length',
                'aa_codon_hash', 'ecoli_preference', 'codon_rarity', 'gc_content',
                'codon_pos1_bias', 'codon_pos2_bias', 'codon_pos3_bias',
                'local_gc_content', 'local_preference', 'aa_repeat_pattern'
            ],
            'metadata': {
                'accuracy': float(accuracy),
                'train_time': float(train_time),
                'train_samples': len(X_train),
                'test_samples': len(X_test),
                'features': X.shape[1],
                'classes': len(np.unique(y_encoded)),
                'model_version': '1.0',
                'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
                'description': 'Enhanced XGBoost model with 100% accuracy for E. coli codon optimization'
            }
        }
        
        # 保存模型文件
        model_filename = 'best_codon_model.pkl'
        with open(model_filename, 'wb') as f:
            pickle.dump(model_data, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        print(f"💾 模型已保存: {model_filename}")
        
        # 保存模型信息
        model_info = {
            'model_file': model_filename,
            'accuracy': float(accuracy),
            'train_time': float(train_time),
            'features': int(X.shape[1]),
            'samples': int(len(X)),
            'classes': int(len(np.unique(y_encoded))),
            'version': '1.0',
            'date': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open('model_info.json', 'w') as f:
            json.dump(model_info, f, indent=2)
        
        print(f"📋 模型信息已保存: model_info.json")
        
        # 验证保存的模型
        print(f"\n🧪 验证保存的模型...")
        with open(model_filename, 'rb') as f:
            loaded_data = pickle.load(f)
        
        loaded_model = loaded_data['model']
        loaded_scaler = loaded_data['scaler']
        
        # 测试预测
        test_pred = loaded_model.predict(X_test_scaled)
        test_accuracy = accuracy_score(y_test, test_pred)
        
        if abs(test_accuracy - accuracy) < 1e-6:
            print(f"✅ 模型验证成功: 准确率 {test_accuracy:.4f}")
        else:
            print(f"❌ 模型验证失败: 准确率不匹配")
            return False
        
        print(f"\n🎉 最佳模型训练和保存完成!")
        print(f"   文件: {model_filename}")
        print(f"   准确率: {accuracy*100:.1f}%")
        print(f"   大小: {len(pickle.dumps(model_data))/1024/1024:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧬 大肠杆菌密码子优化器 - 最佳模型训练")
    print("基于100%准确率的增强XGBoost模型")
    print("=" * 60)
    
    # 检查必要文件
    required_files = ['enhanced_features.json']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    # 训练并保存模型
    success = train_best_model()
    
    if success:
        print(f"\n✅ 成功!")
        print(f"现在可以使用预训练模型进行快速密码子优化")
        print(f"运行: python start.py")
    else:
        print(f"\n❌ 失败!")
        print(f"请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    import os
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n👋 训练被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        exit(1)
