#!/usr/bin/env python3
"""
训练真正的机器学习模型
让预测序列与训练数据完全一致
"""

import pandas as pd
import numpy as np
import pickle
import json
import time
from typing import List, Dict, Tuple
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 尝试导入机器学习库
try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report
    HAS_SKLEARN = True
except ImportError:
    print("❌ 缺少scikit-learn，请安装: pip install scikit-learn")
    HAS_SKLEARN = False

class TrueMLCodonPredictor:
    """真正的机器学习密码子预测器"""
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.feature_names = None
        
        # 遗传密码表
        self.genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G',
            'TAA': '*', 'TAG': '*', 'TGA': '*'
        }
        
        # 氨基酸属性
        self.aa_properties = {
            'A': [1.8, 0, 0, 0, 0],   'R': [-4.5, 1, 1, 0, 0], 'N': [-3.5, 1, 0, 0, 0],
            'D': [-3.5, 1, 0, 1, 0], 'C': [2.5, 0, 0, 0, 0],  'Q': [-3.5, 1, 0, 0, 0],
            'E': [-3.5, 1, 0, 1, 0], 'G': [-0.4, 0, 0, 0, 0], 'H': [-3.2, 1, 1, 0, 1],
            'I': [4.5, 0, 0, 0, 0],  'L': [3.8, 0, 0, 0, 0],  'K': [-3.9, 1, 1, 0, 0],
            'M': [1.9, 0, 0, 0, 0],  'F': [2.8, 0, 0, 0, 1],  'P': [-1.6, 0, 0, 0, 0],
            'S': [-0.8, 1, 0, 0, 0], 'T': [-0.7, 1, 0, 0, 0], 'W': [-0.9, 0, 0, 0, 1],
            'Y': [-1.3, 1, 0, 0, 1], 'V': [4.2, 0, 0, 0, 0],  '*': [0, 0, 0, 0, 0]
        }
        
        print("🧬 真正的机器学习密码子预测器")
        print("   将从训练数据中学习实际的密码子使用模式")
    
    def extract_features(self, protein_seq: str, position: int) -> List[float]:
        """提取特征（与原始特征工程保持一致）"""
        features = []
        
        # 当前氨基酸属性 (5个特征)
        aa = protein_seq[position] if position < len(protein_seq) else '*'
        aa_props = self.aa_properties.get(aa, [0] * 5)
        features.extend(aa_props)
        
        # 前一个氨基酸属性 (5个特征)
        prev_aa = protein_seq[position - 1] if position > 0 else '*'
        prev_props = self.aa_properties.get(prev_aa, [0] * 5)
        features.extend(prev_props)
        
        # 后一个氨基酸属性 (5个特征)
        next_aa = protein_seq[position + 1] if position + 1 < len(protein_seq) else '*'
        next_props = self.aa_properties.get(next_aa, [0] * 5)
        features.extend(next_props)
        
        # 位置特征 (2个特征)
        rel_position = position / len(protein_seq)
        features.append(rel_position)
        features.append(len(protein_seq) / 1000.0)
        
        # 序列上下文特征 (8个特征)
        # 当前氨基酸的one-hot编码（简化版）
        aa_index = hash(aa) % 8  # 简化为8维
        aa_onehot = [0] * 8
        aa_onehot[aa_index] = 1
        features.extend(aa_onehot)
        
        # 局部序列特征 (5个特征)
        # 窗口内氨基酸类型统计
        window_size = 5
        start_pos = max(0, position - window_size // 2)
        end_pos = min(len(protein_seq), position + window_size // 2 + 1)
        window_seq = protein_seq[start_pos:end_pos]
        
        # 统计窗口内的氨基酸类型
        hydrophobic_count = sum(1 for a in window_seq if a in 'AILMFPWV')
        polar_count = sum(1 for a in window_seq if a in 'NQST')
        charged_count = sum(1 for a in window_seq if a in 'DEKR')
        aromatic_count = sum(1 for a in window_seq if a in 'FWY')
        special_count = sum(1 for a in window_seq if a in 'CGP')
        
        window_features = [
            hydrophobic_count / len(window_seq) if window_seq else 0,
            polar_count / len(window_seq) if window_seq else 0,
            charged_count / len(window_seq) if window_seq else 0,
            aromatic_count / len(window_seq) if window_seq else 0,
            special_count / len(window_seq) if window_seq else 0
        ]
        features.extend(window_features)
        
        return features
    
    def prepare_training_data(self, csv_file: str) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        print(f"📂 读取训练数据: {csv_file}")
        
        df = pd.read_csv(csv_file)
        print(f"✅ 读取 {len(df)} 条序列")
        
        X = []
        y = []
        
        print("🔧 提取特征和标签...")
        processed_sequences = 0
        
        for idx, row in df.iterrows():
            protein_seq = row['protein_sequence']
            nucleotide_seq = row['nucleotide_sequence']
            
            # 移除终止密码子
            if nucleotide_seq.endswith(('TAA', 'TAG', 'TGA')):
                nucleotide_seq = nucleotide_seq[:-3]
            
            # 确保长度匹配
            expected_length = len(protein_seq) * 3
            if len(nucleotide_seq) != expected_length:
                continue
            
            # 为每个氨基酸位置提取特征和标签
            for position in range(len(protein_seq)):
                features = self.extract_features(protein_seq, position)
                
                # 获取对应的密码子
                codon_start = position * 3
                codon = nucleotide_seq[codon_start:codon_start + 3]
                
                if len(codon) == 3:
                    X.append(features)
                    y.append(codon)
            
            processed_sequences += 1
            if processed_sequences % 100 == 0:
                print(f"   已处理 {processed_sequences}/{len(df)} 条序列...")
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"✅ 特征提取完成:")
        print(f"   样本数: {len(X):,}")
        print(f"   特征数: {X.shape[1]}")
        print(f"   唯一密码子数: {len(set(y))}")
        
        return X, y
    
    def train(self, csv_file: str):
        """训练模型"""
        if not HAS_SKLEARN:
            raise ImportError("需要安装scikit-learn")
        
        print("🚀 开始训练真正的机器学习模型")
        print("=" * 60)
        
        # 准备数据
        X, y = self.prepare_training_data(csv_file)
        
        # 编码标签
        print("🔧 编码标签...")
        self.label_encoder = LabelEncoder()
        y_encoded = self.label_encoder.fit_transform(y)
        
        print(f"   密码子类别数: {len(self.label_encoder.classes_)}")
        
        # 数据分割
        print("📊 分割数据...")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
        )
        
        print(f"   训练集: {len(X_train):,} 样本")
        print(f"   测试集: {len(X_test):,} 样本")
        
        # 特征标准化
        print("🔧 标准化特征...")
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 训练模型
        print("🎯 训练随机森林模型...")
        start_time = time.time()
        
        self.model = RandomForestClassifier(
            n_estimators=200,
            max_depth=20,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1,
            verbose=1
        )
        
        self.model.fit(X_train_scaled, y_train)
        
        training_time = time.time() - start_time
        
        # 评估模型
        print("📈 评估模型性能...")
        y_pred = self.model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"\n✅ 训练完成!")
        print(f"   训练时间: {training_time:.2f} 秒")
        print(f"   测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        # 特征重要性
        feature_importance = self.model.feature_importances_
        self.feature_names = [
            'aa_hydrophobicity', 'aa_polarity', 'aa_positive', 'aa_negative', 'aa_aromatic',
            'prev_hydrophobicity', 'prev_polarity', 'prev_positive', 'prev_negative', 'prev_aromatic',
            'next_hydrophobicity', 'next_polarity', 'next_positive', 'next_negative', 'next_aromatic',
            'rel_position', 'seq_length'
        ] + [f'aa_onehot_{i}' for i in range(8)] + [
            'window_hydrophobic', 'window_polar', 'window_charged', 'window_aromatic', 'window_special'
        ]
        
        # 显示最重要的特征
        feature_importance_pairs = list(zip(self.feature_names, feature_importance))
        feature_importance_pairs.sort(key=lambda x: x[1], reverse=True)
        
        print(f"\n📊 最重要的10个特征:")
        for i, (name, importance) in enumerate(feature_importance_pairs[:10]):
            print(f"   {i+1:2d}. {name}: {importance:.4f}")
        
        return accuracy, training_time
    
    def save_model(self, filename: str):
        """保存模型"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'feature_names': self.feature_names,
            'genetic_code': self.genetic_code,
            'aa_properties': self.aa_properties,
            'model_type': 'true_ml_predictor',
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        with open(filename, 'wb') as f:
            pickle.dump(model_data, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        print(f"💾 模型已保存: {filename}")
    
    def predict_sequence(self, protein_sequence: str) -> str:
        """预测蛋白质序列的DNA序列"""
        if not self.model:
            raise ValueError("模型未训练")
        
        predicted_codons = []
        
        for position in range(len(protein_sequence)):
            features = self.extract_features(protein_sequence, position)
            features_scaled = self.scaler.transform([features])
            
            predicted_idx = self.model.predict(features_scaled)[0]
            predicted_codon = self.label_encoder.inverse_transform([predicted_idx])[0]
            
            predicted_codons.append(predicted_codon)
        
        return ''.join(predicted_codons)

def main():
    """主训练函数"""
    print("🧬 训练真正的机器学习密码子预测模型")
    print("目标: 让预测序列与训练数据完全一致")
    print("=" * 70)
    
    if not HAS_SKLEARN:
        print("❌ 请先安装scikit-learn: pip install scikit-learn")
        return
    
    # 初始化预测器
    predictor = TrueMLCodonPredictor()
    
    # 训练模型
    try:
        accuracy, training_time = predictor.train('processed_BL21_data.csv')
        
        # 保存模型
        predictor.save_model('true_ml_codon_model.pkl')
        
        # 保存训练信息
        training_info = {
            'model_type': 'true_ml_predictor',
            'accuracy': float(accuracy),
            'training_time': float(training_time),
            'training_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'description': 'True ML model trained to match training data exactly'
        }
        
        with open('true_ml_model_info.json', 'w') as f:
            json.dump(training_info, f, indent=2)
        
        print(f"\n🎉 训练成功完成!")
        print(f"   模型文件: true_ml_codon_model.pkl")
        print(f"   信息文件: true_ml_model_info.json")
        print(f"   准确率: {accuracy*100:.2f}%")
        
        # 测试预测
        print(f"\n🧪 测试预测...")
        test_protein = "MKRISTTITTTITITTGNGAG"
        predicted_dna = predictor.predict_sequence(test_protein)
        print(f"   测试序列: {test_protein}")
        print(f"   预测DNA: {predicted_dna}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
