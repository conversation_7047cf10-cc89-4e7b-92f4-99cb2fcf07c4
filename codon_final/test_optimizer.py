#!/usr/bin/env python3
"""
密码子优化器测试脚本
快速验证工具功能和性能
"""

import time
import sys
import os
from codon_optimizer import EcoliCodonOptimizer

def test_single_sequence():
    """测试单条序列优化"""
    print("🧪 测试1: 单条序列优化")
    print("-" * 40)
    
    optimizer = EcoliCodonOptimizer()
    test_sequence = "MKLLVVS"
    
    print(f"测试序列: {test_sequence}")
    
    start_time = time.time()
    try:
        optimized_dna, info = optimizer.optimize_sequence(test_sequence)
        end_time = time.time()
        
        print(f"✅ 优化成功!")
        print(f"原始序列: {test_sequence}")
        print(f"优化DNA:  {optimized_dna}")
        print(f"GC含量:   {info['gc_content']:.1%}")
        print(f"密码子评分: {info['codon_usage_score']:.3f}")
        print(f"处理时间: {end_time - start_time:.3f} 秒")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_batch_processing():
    """测试批量处理"""
    print("\n🧪 测试2: 批量处理")
    print("-" * 40)
    
    optimizer = EcoliCodonOptimizer()
    test_sequences = [
        "MKLLVVS",
        "MKAIFVLKG",
        "GIVEQCCTSICSLYQLENYCN"
    ]
    
    print(f"测试序列数量: {len(test_sequences)}")
    
    start_time = time.time()
    try:
        results = optimizer.optimize_batch(test_sequences)
        end_time = time.time()
        
        success_count = sum(1 for dna, _ in results if dna is not None)
        
        print(f"✅ 批量处理完成!")
        print(f"成功优化: {success_count}/{len(test_sequences)} 条序列")
        print(f"总处理时间: {end_time - start_time:.3f} 秒")
        print(f"平均每条: {(end_time - start_time)/len(test_sequences):.3f} 秒")
        
        # 显示结果摘要
        for i, (dna, info) in enumerate(results):
            if dna:
                print(f"  序列{i+1}: {len(test_sequences[i])} aa -> {len(dna)} nt, GC={info['gc_content']:.1%}")
            else:
                print(f"  序列{i+1}: 失败 - {info.get('error', '未知错误')}")
        
        return success_count == len(test_sequences)
        
    except Exception as e:
        print(f"❌ 批量测试失败: {e}")
        return False

def test_file_processing():
    """测试文件处理"""
    print("\n🧪 测试3: 文件处理")
    print("-" * 40)
    
    if not os.path.exists("example_sequences.txt"):
        print("⚠️ 示例文件不存在，跳过文件测试")
        return True
    
    try:
        # 使用命令行工具测试
        import subprocess
        
        print("测试命令行批量处理...")
        result = subprocess.run([
            sys.executable, "codon_optimizer.py",
            "--input", "example_sequences.txt",
            "--output", "test_output.txt"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ 文件处理成功!")
            
            # 检查输出文件
            if os.path.exists("test_output.txt"):
                with open("test_output.txt", 'r') as f:
                    lines = f.readlines()
                print(f"输出文件行数: {len(lines)}")
                
                # 清理测试文件
                os.remove("test_output.txt")
                
            return True
        else:
            print(f"❌ 文件处理失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 文件测试失败: {e}")
        return False

def test_performance():
    """性能测试"""
    print("\n🧪 测试4: 性能测试")
    print("-" * 40)
    
    optimizer = EcoliCodonOptimizer()
    
    # 测试不同长度的序列
    test_cases = [
        ("短序列", "MKLLVVS"),
        ("中等序列", "MKAIFVLKGASITEVDAADAAQVKDALTKMRAAALDAQKATPPKLEDKSPDSPEMKDFRHGFDILVGQIDDALKLANEGKVKEAQAAAEQLKTTRNAYIQKYLGDS"),
        ("长序列", "GDVEKGKKIFIMKCSQCHTVEKGGKHKTGPNLHGLFGRKTGQAPGYSYTAANKNKGIIWGEDTLMEYLENPKKYIPGTKMIFVGIKKKEERADLIAYLKKATNEVHLTPEEKSAVTALWGKVNVDEVGGEALGRLLVVYPWTQRFFESFGDLSTPDAVMGNPKVKAHGKKVLGAFSDGLAHLDNLKGTFATLSELHCDKLHVDPENFRLLGNVLVCVLAHHFGKEFTPPVQAAYQKVVAGVANALAHKYH")
    ]
    
    print(f"{'序列类型':<10} {'长度':<6} {'时间(秒)':<10} {'速度(aa/s)':<12}")
    print("-" * 50)
    
    all_passed = True
    
    for name, sequence in test_cases:
        try:
            start_time = time.time()
            optimized_dna, info = optimizer.optimize_sequence(sequence)
            end_time = time.time()
            
            processing_time = end_time - start_time
            speed = len(sequence) / processing_time if processing_time > 0 else float('inf')
            
            print(f"{name:<10} {len(sequence):<6} {processing_time:<10.3f} {speed:<12.1f}")
            
            # 验证结果
            if len(optimized_dna) != len(sequence) * 3:
                print(f"  ⚠️ 长度不匹配: 期望{len(sequence)*3}, 实际{len(optimized_dna)}")
                all_passed = False
            
        except Exception as e:
            print(f"{name:<10} {len(sequence):<6} 失败: {e}")
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("🧬 大肠杆菌密码子优化器 - 功能测试")
    print("=" * 60)
    print("正在验证工具的各项功能...")
    
    tests = [
        ("单条序列优化", test_single_sequence),
        ("批量处理", test_batch_processing),
        ("文件处理", test_file_processing),
        ("性能测试", test_performance)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具运行正常")
        print("\n📖 使用建议:")
        print("1. 运行 'python easy_optimizer.py' 开始使用")
        print("2. 查看 'README.md' 了解详细说明")
        print("3. 使用 'example_sequences.txt' 进行批量测试")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        print("\n🔧 故障排除:")
        print("1. 确保安装了所有依赖包")
        print("2. 检查文件权限")
        print("3. 验证Python版本 (推荐3.7+)")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序异常: {e}")
        sys.exit(1)
