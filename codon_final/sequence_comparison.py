#!/usr/bin/env python3
"""
序列对比分析工具
对比原数据库序列与模型预测结果
"""

def compare_sequences():
    """对比序列差异"""
    
    # 原始蛋白质序列
    protein_seq = "MKRISTTITTTITITTGNGAG"
    
    # 原数据库中的DNA序列
    original_dna = "ATGAAACGCATTAGCACCACCATTACCACCACCATCACCATTACCACAGGTAACGGTGCGGGC"
    
    # 我们模型预测的DNA序列
    predicted_dna = "ATGAAACGTATTAGCACCACCATTACCACCACCATTACCATTACCACCGGCAACGGCGCGGGC"
    
    print("🧬 序列对比分析")
    print("=" * 70)
    print(f"蛋白质序列: {protein_seq}")
    print(f"序列长度: {len(protein_seq)} 氨基酸")
    print()
    
    print("📊 DNA序列对比:")
    print(f"原数据库: {original_dna}")
    print(f"模型预测: {predicted_dna}")
    print(f"序列长度: {len(original_dna)} vs {len(predicted_dna)} 核苷酸")
    print()
    
    # 逐位对比
    print("🔍 逐位对比分析:")
    print("位置  氨基酸  原数据库密码子  模型预测密码子  是否一致")
    print("-" * 55)
    
    matches = 0
    total_positions = len(protein_seq)
    
    for i, aa in enumerate(protein_seq):
        start_pos = i * 3
        end_pos = start_pos + 3
        
        original_codon = original_dna[start_pos:end_pos]
        predicted_codon = predicted_dna[start_pos:end_pos]
        
        is_match = original_codon == predicted_codon
        if is_match:
            matches += 1
        
        match_symbol = "✅" if is_match else "❌"
        
        print(f"{i+1:2d}    {aa}       {original_codon}           {predicted_codon}           {match_symbol}")
    
    # 统计结果
    accuracy = matches / total_positions * 100
    print()
    print("📈 对比统计:")
    print(f"总位置数: {total_positions}")
    print(f"匹配位置: {matches}")
    print(f"不匹配位置: {total_positions - matches}")
    print(f"一致性: {accuracy:.1f}%")
    
    # 分析不匹配的位置
    if matches < total_positions:
        print()
        print("🔍 不匹配位置详细分析:")
        
        # 导入密码子使用频率
        ecoli_codon_usage = {
            'TTT': 0.58, 'TTC': 0.42, 'TTA': 0.14, 'TTG': 0.13,
            'TCT': 0.17, 'TCC': 0.15, 'TCA': 0.14, 'TCG': 0.14,
            'TAT': 0.59, 'TAC': 0.41, 'TGT': 0.46, 'TGC': 0.54, 'TGG': 1.00,
            'CTT': 0.12, 'CTC': 0.10, 'CTA': 0.04, 'CTG': 0.47,
            'CCT': 0.18, 'CCC': 0.13, 'CCA': 0.20, 'CCG': 0.49,
            'CAT': 0.57, 'CAC': 0.43, 'CAA': 0.34, 'CAG': 0.66,
            'CGT': 0.36, 'CGC': 0.36, 'CGA': 0.07, 'CGG': 0.11,
            'ATT': 0.49, 'ATC': 0.39, 'ATA': 0.11, 'ATG': 1.00,
            'ACT': 0.19, 'ACC': 0.40, 'ACA': 0.17, 'ACG': 0.25,
            'AAT': 0.49, 'AAC': 0.51, 'AAA': 0.74, 'AAG': 0.26,
            'AGT': 0.16, 'AGC': 0.25, 'AGA': 0.07, 'AGG': 0.04,
            'GTT': 0.28, 'GTC': 0.20, 'GTA': 0.17, 'GTG': 0.35,
            'GCT': 0.18, 'GCC': 0.26, 'GCA': 0.23, 'GCG': 0.33,
            'GAT': 0.63, 'GAC': 0.37, 'GAA': 0.68, 'GAG': 0.32,
            'GGT': 0.35, 'GGC': 0.37, 'GGA': 0.13, 'GGG': 0.15,
            'TAA': 0.61, 'TAG': 0.09, 'TGA': 0.30
        }
        
        for i, aa in enumerate(protein_seq):
            start_pos = i * 3
            end_pos = start_pos + 3
            
            original_codon = original_dna[start_pos:end_pos]
            predicted_codon = predicted_dna[start_pos:end_pos]
            
            if original_codon != predicted_codon:
                orig_freq = ecoli_codon_usage.get(original_codon, 0.0)
                pred_freq = ecoli_codon_usage.get(predicted_codon, 0.0)
                
                print(f"位置 {i+1} ({aa}):")
                print(f"  原数据库: {original_codon} (使用频率: {orig_freq:.3f})")
                print(f"  模型预测: {predicted_codon} (使用频率: {pred_freq:.3f})")
                
                if pred_freq > orig_freq:
                    print(f"  ✅ 模型选择了更优的密码子 (+{pred_freq-orig_freq:.3f})")
                elif pred_freq < orig_freq:
                    print(f"  ⚠️ 原数据库使用了更优的密码子 (+{orig_freq-pred_freq:.3f})")
                else:
                    print(f"  ➡️ 两者使用频率相同")
                print()
    
    # GC含量对比
    def calculate_gc_content(dna_seq):
        gc_count = dna_seq.count('G') + dna_seq.count('C')
        return gc_count / len(dna_seq) * 100
    
    original_gc = calculate_gc_content(original_dna)
    predicted_gc = calculate_gc_content(predicted_dna)
    
    print("🧪 GC含量对比:")
    print(f"原数据库: {original_gc:.1f}%")
    print(f"模型预测: {predicted_gc:.1f}%")
    print(f"差异: {predicted_gc - original_gc:+.1f}%")
    
    # 总结
    print()
    print("📋 对比总结:")
    if accuracy == 100:
        print("🎉 完全一致! 模型预测与原数据库完全匹配")
    elif accuracy >= 90:
        print("✅ 高度一致! 模型预测与原数据库基本匹配")
    elif accuracy >= 70:
        print("⚠️ 部分一致，存在一些差异")
    else:
        print("❌ 差异较大，需要进一步分析")
    
    return accuracy, matches, total_positions

if __name__ == "__main__":
    compare_sequences()
