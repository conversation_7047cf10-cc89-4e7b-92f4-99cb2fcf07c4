#!/usr/bin/env python3
"""
数据预处理脚本
读取BL21data.xlsx文件，提取蛋白质序列和核酸序列，进行数据清洗和验证
"""

import pandas as pd
import numpy as np
from pathlib import Path
import re
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

# 标准遗传密码表
GENETIC_CODE = {
    'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
    'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
    'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
    'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
    'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
    'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
    'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
    'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
    'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
    'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
    'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
    'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
    'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
    'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
    'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
    'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
}

def translate_dna_to_protein(dna_sequence: str) -> str:
    """将DNA序列翻译为蛋白质序列"""
    protein = ""
    for i in range(0, len(dna_sequence), 3):
        codon = dna_sequence[i:i+3]
        if len(codon) == 3:
            aa = GENETIC_CODE.get(codon, 'X')
            if aa != '*':  # 跳过终止密码子
                protein += aa
    return protein

def validate_sequences(protein_seq: str, dna_seq: str) -> Tuple[bool, str]:
    """验证蛋白质序列和DNA序列的对应关系"""
    # 检查DNA序列长度是否是3的倍数
    if len(dna_seq) % 3 != 0:
        return False, f"DNA序列长度({len(dna_seq)})不是3的倍数"
    
    # 检查DNA序列是否只包含ATCG
    if not re.match(r'^[ATCG]+$', dna_seq):
        return False, "DNA序列包含非ATCG字符"
    
    # 检查蛋白质序列是否只包含标准氨基酸
    if not re.match(r'^[ACDEFGHIKLMNPQRSTVWY]+$', protein_seq):
        return False, "蛋白质序列包含非标准氨基酸"
    
    # 翻译DNA序列并与蛋白质序列比较
    translated_protein = translate_dna_to_protein(dna_seq)
    
    if len(protein_seq) != len(translated_protein):
        return False, f"长度不匹配: 蛋白质({len(protein_seq)}) vs 翻译({len(translated_protein)})"
    
    if protein_seq != translated_protein:
        return False, f"序列不匹配: {protein_seq[:50]}... vs {translated_protein[:50]}..."
    
    return True, "验证通过"

def load_and_preprocess_data(file_path: str) -> pd.DataFrame:
    """加载和预处理BL21数据"""
    print(f"正在读取数据文件: {file_path}")
    
    # 读取Excel文件
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取数据，共{len(df)}行")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None
    
    # 显示列名
    print(f"数据列: {list(df.columns)}")
    
    # 查找蛋白质序列和核酸序列列
    protein_col = None
    nucleotide_col = None
    
    for col in df.columns:
        col_lower = col.lower()
        if 'protein' in col_lower and 'sequence' in col_lower:
            protein_col = col
        elif 'nucleotide' in col_lower and 'sequence' in col_lower:
            nucleotide_col = col
    
    if protein_col is None or nucleotide_col is None:
        print("未找到蛋白质序列或核酸序列列")
        print("可用列:", list(df.columns))
        return None
    
    print(f"蛋白质序列列: {protein_col}")
    print(f"核酸序列列: {nucleotide_col}")
    
    # 提取有效数据
    valid_data = []
    invalid_count = 0
    
    for idx, row in df.iterrows():
        protein_seq = str(row[protein_col]).strip().upper()
        nucleotide_seq = str(row[nucleotide_col]).strip().upper()
        
        # 跳过空值
        if pd.isna(row[protein_col]) or pd.isna(row[nucleotide_col]):
            invalid_count += 1
            continue
            
        if protein_seq == 'NAN' or nucleotide_seq == 'NAN':
            invalid_count += 1
            continue
        
        # 验证序列
        is_valid, message = validate_sequences(protein_seq, nucleotide_seq)
        
        if is_valid:
            valid_data.append({
                'protein_sequence': protein_seq,
                'nucleotide_sequence': nucleotide_seq,
                'protein_length': len(protein_seq),
                'nucleotide_length': len(nucleotide_seq)
            })
        else:
            invalid_count += 1
            if invalid_count <= 5:  # 只打印前5个错误
                print(f"行{idx+1}验证失败: {message}")
    
    print(f"有效数据: {len(valid_data)}条")
    print(f"无效数据: {invalid_count}条")
    
    if len(valid_data) == 0:
        print("没有找到有效数据")
        return None
    
    # 创建清洗后的DataFrame
    clean_df = pd.DataFrame(valid_data)
    
    # 统计信息
    print("\n数据统计:")
    print(f"蛋白质序列长度范围: {clean_df['protein_length'].min()} - {clean_df['protein_length'].max()}")
    print(f"核酸序列长度范围: {clean_df['nucleotide_length'].min()} - {clean_df['nucleotide_length'].max()}")
    print(f"平均蛋白质长度: {clean_df['protein_length'].mean():.1f}")
    
    return clean_df

def analyze_codon_usage(df: pd.DataFrame) -> Dict:
    """分析密码子使用频率"""
    print("\n分析密码子使用频率...")
    
    codon_counts = {}
    aa_codon_counts = {}
    
    for _, row in df.iterrows():
        nucleotide_seq = row['nucleotide_sequence']
        protein_seq = row['protein_sequence']
        
        # 提取密码子
        for i in range(0, len(nucleotide_seq), 3):
            codon = nucleotide_seq[i:i+3]
            if len(codon) == 3:
                aa = GENETIC_CODE.get(codon, 'X')
                if aa != '*' and aa != 'X':
                    # 统计密码子频率
                    codon_counts[codon] = codon_counts.get(codon, 0) + 1
                    
                    # 统计每个氨基酸的密码子使用
                    if aa not in aa_codon_counts:
                        aa_codon_counts[aa] = {}
                    aa_codon_counts[aa][codon] = aa_codon_counts[aa].get(codon, 0) + 1
    
    # 计算每个氨基酸的密码子偏好性
    aa_preferences = {}
    for aa, codons in aa_codon_counts.items():
        total = sum(codons.values())
        preferences = {codon: count/total for codon, count in codons.items()}
        aa_preferences[aa] = preferences
    
    print(f"总共发现 {len(codon_counts)} 种密码子")
    print("前10个最常用密码子:")
    sorted_codons = sorted(codon_counts.items(), key=lambda x: x[1], reverse=True)
    for codon, count in sorted_codons[:10]:
        aa = GENETIC_CODE[codon]
        print(f"  {codon} ({aa}): {count}")
    
    return {
        'codon_counts': codon_counts,
        'aa_codon_counts': aa_codon_counts,
        'aa_preferences': aa_preferences
    }

def main():
    """主函数"""
    data_file = "20250623_Data/BL21data.xlsx"
    
    if not Path(data_file).exists():
        print(f"数据文件不存在: {data_file}")
        return
    
    # 加载和预处理数据
    df = load_and_preprocess_data(data_file)
    
    if df is None:
        print("数据预处理失败")
        return
    
    # 分析密码子使用
    codon_analysis = analyze_codon_usage(df)
    
    # 保存清洗后的数据
    output_file = "processed_BL21_data.csv"
    df.to_csv(output_file, index=False)
    print(f"\n清洗后的数据已保存到: {output_file}")
    
    # 保存一些样本数据用于检查
    sample_df = df.head(10)
    print("\n前10条数据样本:")
    for idx, row in sample_df.iterrows():
        print(f"样本 {idx+1}:")
        print(f"  蛋白质: {row['protein_sequence'][:50]}...")
        print(f"  核酸:   {row['nucleotide_sequence'][:50]}...")
        print(f"  长度:   蛋白质={row['protein_length']}, 核酸={row['nucleotide_length']}")
        print()

if __name__ == "__main__":
    main()
