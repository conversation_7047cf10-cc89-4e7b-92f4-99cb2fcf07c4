# 密码子预测模型Loss=NaN问题诊断与解决方案

> 深度学习训练中Loss=NaN的完整调试指南

## 🚨 问题现象

训练过程中出现 `Loss=nan`，表示损失函数计算出了无效的数值（Not a Number）。这会导致：
- 模型参数变为NaN
- 训练过程无法继续
- 梯度传播失效

## 🔍 问题诊断

### 1. 数值不稳定检查

```python
def diagnose_numerical_issues(model, sample_batch):
    """诊断模型中的数值问题"""
    print("=== 数值稳定性诊断 ===")
    
    # 检查输入数据
    protein_seq = sample_batch['protein_seq']
    print(f"输入序列长度: {len(protein_seq)}")
    print(f"输入序列示例: {protein_seq[:10]}...")
    
    # 逐步检查每个组件
    with torch.no_grad():
        # 1. ESM2特征提取
        esm2_output = model.esm2(protein_seq)
        features = esm2_output.last_hidden_state
        
        print(f"ESM2特征形状: {features.shape}")
        print(f"ESM2特征范围: [{features.min().item():.4f}, {features.max().item():.4f}]")
        print(f"ESM2特征包含NaN: {torch.isnan(features).any().item()}")
        print(f"ESM2特征包含Inf: {torch.isinf(features).any().item()}")
        
        # 2. 预测器输出
        raw_logits = model.codon_predictor(features)
        
        print(f"原始logits形状: {raw_logits.shape}")
        print(f"原始logits范围: [{raw_logits.min().item():.4f}, {raw_logits.max().item():.4f}]")
        print(f"原始logits包含NaN: {torch.isnan(raw_logits).any().item()}")
        print(f"原始logits包含Inf: {torch.isinf(raw_logits).any().item()}")
        
        # 3. 约束后的logits
        constrained_logits = model.apply_codon_constraints(raw_logits, protein_seq)
        
        print(f"约束后logits范围: [{constrained_logits.min().item():.4f}, {constrained_logits.max().item():.4f}]")
        print(f"约束后logits包含NaN: {torch.isnan(constrained_logits).any().item()}")
        print(f"约束后logits包含Inf: {torch.isinf(constrained_logits).any().item()}")
        
        # 4. Softmax输出
        probs = F.softmax(constrained_logits, dim=-1)
        
        print(f"概率分布范围: [{probs.min().item():.6f}, {probs.max().item():.6f}]")
        print(f"概率分布包含NaN: {torch.isnan(probs).any().item()}")
        print(f"概率分布包含Inf: {torch.isinf(probs).any().item()}")
        
        # 5. 检查约束是否正确应用
        check_constraint_validity(model, protein_seq, constrained_logits)

def check_constraint_validity(model, protein_seq, constrained_logits):
    """检查约束是否正确应用"""
    print("\n=== 约束有效性检查 ===")
    
    for pos in range(min(5, len(protein_seq))):  # 检查前5个位置
        aa = protein_seq[pos]
        valid_codons = model.aa_to_codons.get(aa, [])
        
        # 检查有效密码子的logits
        valid_logits = []
        for codon in valid_codons:
            codon_idx = model.codon_to_idx[codon]
            logit_val = constrained_logits[0, pos, codon_idx].item()
            valid_logits.append(logit_val)
        
        # 检查无效密码子的logits
        invalid_count = 0
        for codon_idx in range(64):
            if constrained_logits[0, pos, codon_idx].item() < -1e8:
                invalid_count += 1
        
        print(f"位置{pos} ({aa}): 有效密码子{len(valid_codons)}个, 被屏蔽{invalid_count}个")
        print(f"  有效logits范围: [{min(valid_logits):.4f}, {max(valid_logits):.4f}]")
```

## 🛠️ 解决方案

### 1. 修复约束函数（关键）

原始代码的问题：使用 `-inf` 导致数值不稳定

```python
# ❌ 问题代码
constrained_logits[b, pos, ~mask] = float('-inf')

# ✅ 修复后的代码
def apply_codon_constraints_fixed(self, logits, amino_acid_sequence):
    """修复后的约束函数，避免数值不稳定"""
    batch_size, seq_len, vocab_size = logits.shape
    constrained_logits = logits.clone()
    
    # 使用有限的大负数而不是-inf
    LARGE_NEGATIVE = -1e9
    
    for b in range(batch_size):
        for pos in range(seq_len):
            aa = amino_acid_sequence[b][pos]
            
            if aa in self.aa_to_codons:
                valid_codons = self.aa_to_codons[aa]
                
                # 创建mask
                mask = torch.zeros(vocab_size, dtype=torch.bool, device=logits.device)
                for codon in valid_codons:
                    if codon in self.codon_to_idx:
                        codon_idx = self.codon_to_idx[codon]
                        mask[codon_idx] = True
                
                # 屏蔽无效密码子，使用有限负数
                constrained_logits[b, pos, ~mask] = LARGE_NEGATIVE
                
                # 确保至少有一个有效的logits
                if mask.sum() == 0:
                    print(f"警告: 氨基酸 {aa} 没有找到有效密码子")
                    constrained_logits[b, pos, :] = logits[b, pos, :]
    
    return constrained_logits
```

### 2. 改进损失函数

```python
class RobustCodonLoss(nn.Module):
    """数值稳定的密码子预测损失函数"""
    
    def __init__(self, label_smoothing=0.1, ignore_index=-100):
        super().__init__()
        self.label_smoothing = label_smoothing
        self.ignore_index = ignore_index
        
    def forward(self, predicted_logits, true_codons, valid_mask=None):
        # 输入验证
        if torch.isnan(predicted_logits).any():
            print("错误: 预测logits包含NaN")
            return torch.tensor(float('nan'), device=predicted_logits.device)
            
        if torch.isinf(predicted_logits).any():
            print("警告: 预测logits包含Inf，进行裁剪")
            predicted_logits = torch.clamp(predicted_logits, min=-1e9, max=1e9)
        
        # 重新整形
        logits_flat = predicted_logits.view(-1, predicted_logits.size(-1))
        targets_flat = true_codons.view(-1)
        
        # 计算损失
        try:
            loss = F.cross_entropy(
                logits_flat,
                targets_flat,
                label_smoothing=self.label_smoothing,
                ignore_index=self.ignore_index,
                reduction='mean'
            )
            
            # 最终检查
            if torch.isnan(loss):
                print("错误: 计算出的损失为NaN")
                print(f"Logits统计: min={logits_flat.min()}, max={logits_flat.max()}")
                print(f"Targets统计: min={targets_flat.min()}, max={targets_flat.max()}")
                return torch.tensor(0.0, device=predicted_logits.device, requires_grad=True)
                
            return loss
            
        except Exception as e:
            print(f"损失计算异常: {e}")
            return torch.tensor(0.0, device=predicted_logits.device, requires_grad=True)
```

### 3. 稳健的训练循环

```python
def train_robust(model, dataloader, optimizer, criterion, epoch):
    """稳健的训练循环"""
    model.train()
    total_loss = 0
    nan_count = 0
    
    for batch_idx, batch in enumerate(dataloader):
        try:
            optimizer.zero_grad()
            
            # 前向传播
            codon_probs, logits = model(batch['protein_seq'])
            
            # 损失计算
            loss = criterion(logits, batch['true_codons'])
            
            # NaN检查
            if torch.isnan(loss):
                nan_count += 1
                print(f"批次 {batch_idx}: 检测到NaN损失")
                
                # 诊断当前批次
                diagnose_numerical_issues(model, batch)
                
                # 跳过这个批次
                continue
            
            # 反向传播
            loss.backward()
            
            # 梯度检查和裁剪
            grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            if torch.isnan(grad_norm):
                print(f"批次 {batch_idx}: 检测到NaN梯度")
                continue
                
            # 参数更新
            optimizer.step()
            
            total_loss += loss.item()
            
            # 定期输出
            if batch_idx % 100 == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}, Loss: {loss.item():.6f}, Grad Norm: {grad_norm:.4f}')
                
        except Exception as e:
            print(f"批次 {batch_idx} 训练异常: {e}")
            continue
    
    avg_loss = total_loss / (len(dataloader) - nan_count) if len(dataloader) > nan_count else 0
    print(f'Epoch {epoch}: 平均损失 = {avg_loss:.6f}, NaN批次数 = {nan_count}')
    
    return avg_loss
```

### 4. 优化的训练配置

```python
# 保守但稳定的训练配置
def get_stable_training_config():
    return {
        # 学习率设置
        'learning_rate': 1e-5,  # 较小的学习率
        'warmup_steps': 1000,
        'lr_scheduler': 'cosine_with_warmup',
        
        # 批次设置
        'batch_size': 4,  # 较小的批次大小
        'gradient_accumulation_steps': 4,
        
        # 正则化
        'weight_decay': 0.01,
        'label_smoothing': 0.1,
        'dropout': 0.1,
        
        # 梯度控制
        'gradient_clip_norm': 1.0,
        'gradient_clip_value': None,
        
        # 数值稳定性
        'use_amp': False,  # 暂时禁用混合精度
        'max_logit_value': 1e9,
        
        # 检查点
        'save_every_n_epochs': 5,
        'validate_every_n_steps': 500,
    }

# 优化器设置
def setup_stable_optimizer(model, config):
    # 分层学习率
    no_decay = ['bias', 'LayerNorm.weight']
    
    optimizer_grouped_parameters = [
        {
            'params': [p for n, p in model.named_parameters() 
                      if not any(nd in n for nd in no_decay) and 'esm2' not in n],
            'weight_decay': config['weight_decay'],
            'lr': config['learning_rate']
        },
        {
            'params': [p for n, p in model.named_parameters() 
                      if any(nd in n for nd in no_decay) and 'esm2' not in n],
            'weight_decay': 0.0,
            'lr': config['learning_rate']
        },
        {
            'params': [p for n, p in model.named_parameters() if 'esm2' in n],
            'weight_decay': config['weight_decay'],
            'lr': config['learning_rate'] * 0.1  # ESM2使用更小的学习率
        }
    ]
    
    optimizer = torch.optim.AdamW(
        optimizer_grouped_parameters,
        eps=1e-8,
        betas=(0.9, 0.999)
    )
    
    return optimizer
```

## 🧪 调试工具

### 1. 实时监控脚本

```python
class NaNWatcher:
    """NaN监控器"""
    
    def __init__(self, model):
        self.model = model
        self.hooks = []
        self.register_hooks()
    
    def register_hooks(self):
        """注册前向传播钩子"""
        def check_nan_hook(module, input, output):
            if isinstance(output, torch.Tensor):
                if torch.isnan(output).any():
                    print(f"NaN detected in {module.__class__.__name__}")
            elif isinstance(output, (list, tuple)):
                for i, tensor in enumerate(output):
                    if isinstance(tensor, torch.Tensor) and torch.isnan(tensor).any():
                        print(f"NaN detected in {module.__class__.__name__} output[{i}]")
        
        for name, module in self.model.named_modules():
            hook = module.register_forward_hook(check_nan_hook)
            self.hooks.append(hook)
    
    def remove_hooks(self):
        """移除所有钩子"""
        for hook in self.hooks:
            hook.remove()
        self.hooks = []

# 使用方法
nan_watcher = NaNWatcher(model)
# 训练代码...
nan_watcher.remove_hooks()
```

### 2. 模型健康检查

```python
def model_health_check(model, sample_input):
    """全面的模型健康检查"""
    print("=== 模型健康检查 ===")
    
    # 1. 参数检查
    total_params = 0
    nan_params = 0
    inf_params = 0
    
    for name, param in model.named_parameters():
        total_params += param.numel()
        if torch.isnan(param).any():
            nan_params += torch.isnan(param).sum().item()
            print(f"参数 {name} 包含 {torch.isnan(param).sum()} 个NaN")
        if torch.isinf(param).any():
            inf_params += torch.isinf(param).sum().item()
            print(f"参数 {name} 包含 {torch.isinf(param).sum()} 个Inf")
    
    print(f"总参数数: {total_params}")
    print(f"NaN参数数: {nan_params}")
    print(f"Inf参数数: {inf_params}")
    
    # 2. 前向传播检查
    try:
        with torch.no_grad():
            output = model(sample_input)
        print("✓ 前向传播正常")
    except Exception as e:
        print(f"✗ 前向传播失败: {e}")
    
    # 3. 梯度检查
    try:
        model.train()
        dummy_loss = model(sample_input)[0].sum()
        dummy_loss.backward()
        print("✓ 反向传播正常")
    except Exception as e:
        print(f"✗ 反向传播失败: {e}")

# 使用示例
sample_batch = next(iter(dataloader))
model_health_check(model, sample_batch['protein_seq'])
```

## 📋 问题排查清单

1. **立即检查**:
   - [ ] 将约束函数中的 `-inf` 改为 `-1e9`
   - [ ] 添加梯度裁剪 (`max_norm=1.0`)
   - [ ] 降低学习率到 `1e-5`

2. **数据验证**:
   - [ ] 检查输入数据是否包含异常值
   - [ ] 验证标签数据的范围和格式
   - [ ] 确认批次大小不会太大

3. **模型检查**:
   - [ ] 运行数值稳定性诊断
   - [ ] 检查模型参数初始化
   - [ ] 验证约束逻辑的正确性

4. **训练设置**:
   - [ ] 使用稳健的损失函数
   - [ ] 添加异常处理和跳过机制
   - [ ] 实施定期的健康检查

## 🚀 快速修复示例

```python
# 最小化修改，快速修复NaN问题
def quick_fix_apply_constraints(self, logits, amino_acid_sequence):
    """快速修复版本的约束函数"""
    batch_size, seq_len, vocab_size = logits.shape
    constrained_logits = logits.clone()
    
    for b in range(batch_size):
        for pos in range(seq_len):
            aa = amino_acid_sequence[b][pos]
            if aa in self.aa_to_codons:
                valid_codons = self.aa_to_codons[aa]
                
                # 使用有限负数代替-inf
                mask = torch.zeros(vocab_size, dtype=torch.bool, device=logits.device)
                for codon in valid_codons:
                    if codon in self.codon_to_idx:
                        mask[self.codon_to_idx[codon]] = True
                
                constrained_logits[b, pos, ~mask] = -1e9  # 关键修改
    
    return constrained_logits

# 替换原来的函数
model.apply_codon_constraints = quick_fix_apply_constraints.__get__(model, model.__class__)
```

按照这个指南逐步排查，应该能够解决Loss=NaN的问题。最关键的是修复约束函数中的数值不稳定问题。