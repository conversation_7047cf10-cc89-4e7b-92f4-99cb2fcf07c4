#!/usr/bin/env python3
"""
Quick Optimized Architecture Test
Test the optimized architecture with a small subset for validation
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import time
warnings.filterwarnings('ignore')

from fixed_data_processor import FixedCodonDataset, fixed_collate_fn

class QuickOptimizedModel(nn.Module):
    """Simplified optimized model for quick testing"""
    
    def __init__(self, esm_model_path="facebook/esm2_t33_650M_UR50D"):
        super().__init__()
        
        # Load ESM2 model
        from transformers import EsmModel, EsmTokenizer
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            self.tokenizer = EsmTokenizer.from_pretrained(local_path)
            self.esm2 = EsmModel.from_pretrained(local_path)
        except:
            self.tokenizer = EsmTokenizer.from_pretrained(esm_model_path)
            self.esm2 = EsmModel.from_pretrained(esm_model_path)
        
        # Freeze ESM2 parameters
        for param in self.esm2.parameters():
            param.requires_grad = False
            
        self.hidden_size = self.esm2.config.hidden_size
        
        # Build genetic code and codon vocabulary
        self.genetic_code = self._build_genetic_code()
        self.codon_to_idx = self._build_codon_vocab()
        
        # Simplified architecture
        self.feature_projection = nn.Linear(self.hidden_size, 256)
        self.feature_norm = nn.LayerNorm(256)
        self.dropout = nn.Dropout(0.2)
        
        # Multi-head attention
        self.attention = nn.MultiheadAttention(256, num_heads=8, dropout=0.1, batch_first=True)
        self.attention_norm = nn.LayerNorm(256)
        
        # Feed-forward network
        self.ff = nn.Sequential(
            nn.Linear(256, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.Dropout(0.1)
        )
        self.ff_norm = nn.LayerNorm(256)
        
        # Output layer
        self.output = nn.Linear(256, len(self.codon_to_idx))
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        """Initialize weights"""
        for module in [self.feature_projection, self.ff, self.output]:
            if isinstance(module, nn.Sequential):
                for layer in module:
                    if isinstance(layer, nn.Linear):
                        nn.init.xavier_uniform_(layer.weight, gain=0.1)
                        if layer.bias is not None:
                            nn.init.zeros_(layer.bias)
            elif isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def _build_genetic_code(self):
        return {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
    
    def _build_codon_vocab(self):
        codons = [codon for codon, aa in self.genetic_code.items() if aa != '*']
        return {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    def forward(self, protein_sequences):
        """Forward pass"""
        # ESM2 encoding
        encoded = self.tokenizer(
            protein_sequences,
            padding=True,
            truncation=True,
            max_length=512,  # Smaller for quick test
            return_tensors="pt"
        )
        
        input_ids = encoded['input_ids'].to(next(self.parameters()).device)
        attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
        
        # ESM2 feature extraction
        with torch.no_grad():
            esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
        
        # Feature projection
        features = self.feature_projection(esm_output.last_hidden_state)
        features = self.feature_norm(features)
        features = self.dropout(features)
        
        # Multi-head attention
        attn_output, _ = self.attention(features, features, features, 
                                       key_padding_mask=~attention_mask.bool())
        features = self.attention_norm(features + attn_output)
        
        # Feed-forward
        ff_output = self.ff(features)
        features = self.ff_norm(features + ff_output)
        
        # Output
        logits = self.output(features)
        
        return logits

def quick_test_optimized_model():
    """Quick test of the optimized model"""
    print("🚀 Quick Test: Optimized Architecture")
    print("Testing advanced model with subset of data")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Load small subset of data
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 256]  # Smaller sequences for quick test
    df = df.head(100)  # Only 100 samples for quick test
    print(f"Using {len(df)} samples for quick test")
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # Create model
    model = QuickOptimizedModel()
    model = model.to(device)
    
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Trainable parameters: {total_params:,}")
    
    # Create dataset
    dataset = FixedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    
    if len(dataset) == 0:
        print("❌ No valid data found")
        return 0.0
    
    print(f"Dataset size: {len(dataset)}")
    
    # Small train/val split
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # Data loaders
    batch_size = 2  # Very small batch for quick test
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=fixed_collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=fixed_collate_fn)
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
    
    print(f"\n🎯 Quick Training Configuration:")
    print(f"  Epochs: 5 (quick test)")
    print(f"  Batch size: {batch_size}")
    print(f"  Learning rate: 1e-4")
    print(f"  Train batches: {len(train_loader)}")
    print(f"  Val batches: {len(val_loader)}")
    
    def calculate_accuracy(logits, targets, ignore_index=-100):
        with torch.no_grad():
            predictions = torch.argmax(logits, dim=-1)
            mask = (targets != ignore_index)
            
            if mask.sum() == 0:
                return 0.0
            
            correct = (predictions == targets).float() * mask.float()
            accuracy = correct.sum() / mask.sum()
            
            return accuracy.item()
    
    print(f"\n🏃 Starting quick training...")
    
    best_val_acc = 0.0
    
    for epoch in range(5):  # Quick test with 5 epochs
        # Training
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc=f'Epoch {epoch+1}/5 [Train]', leave=False):
            try:
                if batch is None:
                    continue
                
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                logits = model(proteins)
                
                # Align dimensions
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                loss = criterion(
                    logits.reshape(-1, logits.size(-1)),
                    targets.reshape(-1)
                )
                
                if torch.isnan(loss) or torch.isinf(loss):
                    continue
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                train_loss += loss.item()
                train_acc += calculate_accuracy(logits, targets)
                train_batches += 1
                
            except Exception as e:
                print(f"Training error: {e}")
                continue
        
        # Validation
        model.eval()
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f'Epoch {epoch+1}/5 [Val]', leave=False):
                try:
                    if batch is None:
                        continue
                    
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    val_acc += calculate_accuracy(logits, targets)
                    val_batches += 1
                    
                except Exception as e:
                    continue
        
        # Calculate averages
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        
        print(f'Epoch {epoch+1}/5: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%')
        
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            print(f'  🏆 New best validation accuracy: {best_val_acc*100:.1f}%')
    
    print(f"\n🏁 Quick test complete!")
    print(f"Best validation accuracy: {best_val_acc*100:.1f}%")
    
    # Save results
    results = {
        'quick_test': True,
        'samples_used': len(df),
        'epochs': 5,
        'best_val_accuracy': float(best_val_acc),
        'model_type': 'optimized_transformer',
        'trainable_parameters': total_params
    }
    
    with open('quick_optimized_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"💾 Results saved to: quick_optimized_results.json")
    
    return best_val_acc

if __name__ == "__main__":
    try:
        best_accuracy = quick_test_optimized_model()
        
        print(f"\n🎯 QUICK TEST SUMMARY:")
        print(f"   Optimized Architecture: {best_accuracy*100:.1f}%")
        print(f"   Previous Best (Improved): 54.8%")
        print(f"   Previous Best (XGBoost): 48.4%")
        
        if best_accuracy > 0.548:
            print(f"\n🎉 SUCCESS: Optimized architecture shows improvement!")
            print(f"   Improvement: {(best_accuracy - 0.548)*100:.1f} percentage points")
        else:
            print(f"\n📊 BASELINE: Quick test provides validation")
            print(f"   Note: Full training needed for complete evaluation")
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")
        import traceback
        traceback.print_exc()
