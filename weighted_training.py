#!/usr/bin/env python3
"""
基于加权损失函数的密码子偏好性预测模型训练
解决密码子分布不均衡问题，提高模型准确度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import os
import warnings
warnings.filterwarnings('ignore')

from fixed_training import FixedCodonPreferenceModel
from improved_training import ImprovedCodonDataset, collate_fn

class WeightedCodonLoss(nn.Module):
    """基于密码子权重的损失函数"""
    
    def __init__(self, codon_weights, codon_to_idx, ignore_index=-100, focal_alpha=0.25, focal_gamma=2.0):
        super().__init__()
        self.ignore_index = ignore_index
        self.focal_alpha = focal_alpha
        self.focal_gamma = focal_gamma
        
        # 构建权重张量
        self.register_buffer('class_weights', self._build_weight_tensor(codon_weights, codon_to_idx))
        
    def _build_weight_tensor(self, codon_weights, codon_to_idx):
        """构建密码子权重张量"""
        num_classes = len(codon_to_idx)
        weights = torch.ones(num_classes)
        
        for codon, weight in codon_weights.items():
            if codon in codon_to_idx:
                idx = codon_to_idx[codon]
                weights[idx] = weight
        
        # 归一化权重，避免权重过大
        weights = weights / weights.mean()
        
        print(f"权重张量统计: min={weights.min():.3f}, max={weights.max():.3f}, mean={weights.mean():.3f}")
        return weights
    
    def forward(self, logits, targets):
        """
        计算加权损失
        logits: [batch_size, seq_len, num_classes]
        targets: [batch_size, seq_len]
        """
        # 数值稳定性检查
        if torch.isnan(logits).any() or torch.isinf(logits).any():
            print("警告: logits包含NaN或Inf")
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        # 重新整形
        logits_flat = logits.reshape(-1, logits.size(-1))  # [batch*seq, num_classes]
        targets_flat = targets.reshape(-1)  # [batch*seq]
        
        # 创建有效位置的mask
        valid_mask = (targets_flat != self.ignore_index)
        
        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        # 只计算有效位置的损失
        valid_logits = logits_flat[valid_mask]
        valid_targets = targets_flat[valid_mask]
        
        # 计算概率
        probs = F.softmax(valid_logits, dim=-1)
        
        # 获取目标类别的概率
        target_probs = probs.gather(1, valid_targets.unsqueeze(1)).squeeze(1)
        
        # Focal Loss权重
        focal_weight = self.focal_alpha * (1 - target_probs) ** self.focal_gamma
        
        # 类别权重
        class_weight = self.class_weights[valid_targets]
        
        # 组合权重
        combined_weight = focal_weight * class_weight
        
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(valid_logits, valid_targets, reduction='none')
        
        # 应用权重
        weighted_loss = (ce_loss * combined_weight).mean()
        
        # 最终检查
        if torch.isnan(weighted_loss) or torch.isinf(weighted_loss):
            print("警告: 加权损失为NaN或Inf")
            return torch.tensor(0.0, device=logits.device, requires_grad=True)
        
        return weighted_loss

def calculate_weighted_accuracy(logits, targets, codon_weights, codon_to_idx, ignore_index=-100):
    """计算加权准确度"""
    with torch.no_grad():
        predictions = torch.argmax(logits, dim=-1)
        mask = (targets != ignore_index)
        
        if mask.sum() == 0:
            return 0.0
        
        # 计算每个位置的权重
        weights = torch.ones_like(targets, dtype=torch.float)
        for codon, weight in codon_weights.items():
            if codon in codon_to_idx:
                idx = codon_to_idx[codon]
                weights[targets == idx] = weight
        
        # 计算加权准确度
        correct = (predictions == targets).float() * mask.float()
        weighted_correct = (correct * weights).sum()
        weighted_total = (mask.float() * weights).sum()
        
        if weighted_total > 0:
            return (weighted_correct / weighted_total).item()
        else:
            return 0.0

def train_weighted_model():
    """训练加权模型"""
    print("开始训练基于加权损失的密码子偏好性预测模型")
    
    # 加载权重信息
    with open('codon_weights.json', 'r') as f:
        weight_info = json.load(f)
    
    codon_weights = weight_info['codon_weights']
    print(f"加载了 {len(codon_weights)} 个密码子的权重信息")
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 256]  # 进一步限制长度以提高训练效率
    print(f"使用 {len(df)} 条数据进行训练")
    
    # 创建模型
    model = FixedCodonPreferenceModel()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 创建数据集
    dataset = ImprovedCodonDataset(
        df['protein_sequence'].tolist(),
        df['nucleotide_sequence'].tolist(),
        model.codon_to_idx
    )
    
    print(f"有效数据集大小: {len(dataset)}")
    
    # 划分数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 数据加载器
    batch_size = 4
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)
    
    # 加权损失函数
    criterion = WeightedCodonLoss(codon_weights, model.codon_to_idx)
    criterion = criterion.to(device)
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=5e-6, weight_decay=0.01)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=30)
    
    best_accuracy = 0.0
    patience = 8
    patience_counter = 0
    
    print(f"训练集: {len(train_dataset)}, 验证集: {len(val_dataset)}")
    print(f"批次大小: {batch_size}, 设备: {device}")
    
    for epoch in range(100):  # 增加最大训练轮数
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_weighted_acc = 0.0
        valid_batches = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/100 [Train]')
        for batch_idx, batch in enumerate(train_pbar):
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                # 前向传播
                logits = model(proteins)
                
                # 调整维度
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                # 计算损失
                loss = criterion(logits, targets)
                
                # 检查损失值
                if loss.item() > 100:  # 异常大的损失
                    print(f"跳过异常批次，损失值: {loss.item()}")
                    continue
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                if torch.isnan(grad_norm):
                    print("跳过NaN梯度批次")
                    continue
                
                optimizer.step()
                
                # 计算准确度
                from fixed_training import calculate_accuracy
                acc = calculate_accuracy(logits, targets)
                weighted_acc = calculate_weighted_accuracy(logits, targets, codon_weights, model.codon_to_idx)
                
                train_loss += loss.item()
                train_acc += acc
                train_weighted_acc += weighted_acc
                valid_batches += 1
                
                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.3f}',
                    'Acc': f'{acc:.3f}',
                    'WAcc': f'{weighted_acc:.3f}',
                    'GradNorm': f'{grad_norm:.3f}'
                })
                
            except Exception as e:
                print(f"训练批次 {batch_idx} 异常: {e}")
                continue
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_acc = 0.0
        val_weighted_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/100 [Val]')
            for batch in val_pbar:
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    loss = criterion(logits, targets)
                    acc = calculate_accuracy(logits, targets)
                    weighted_acc = calculate_weighted_accuracy(logits, targets, codon_weights, model.codon_to_idx)
                    
                    val_loss += loss.item()
                    val_acc += acc
                    val_weighted_acc += weighted_acc
                    val_batches += 1
                    
                    val_pbar.set_postfix({
                        'Loss': f'{loss.item():.3f}',
                        'Acc': f'{acc:.3f}',
                        'WAcc': f'{weighted_acc:.3f}'
                    })
                    
                except Exception as e:
                    print(f"验证批次异常: {e}")
                    continue
        
        # 计算平均值
        if valid_batches > 0:
            avg_train_loss = train_loss / valid_batches
            avg_train_acc = train_acc / valid_batches
            avg_train_weighted_acc = train_weighted_acc / valid_batches
        else:
            avg_train_loss = float('inf')
            avg_train_acc = 0.0
            avg_train_weighted_acc = 0.0
            
        if val_batches > 0:
            avg_val_loss = val_loss / val_batches
            avg_val_acc = val_acc / val_batches
            avg_val_weighted_acc = val_weighted_acc / val_batches
        else:
            avg_val_loss = float('inf')
            avg_val_acc = 0.0
            avg_val_weighted_acc = 0.0
        
        print(f'Epoch {epoch+1}/100:')
        print(f'  Train - Loss: {avg_train_loss:.4f}, Acc: {avg_train_acc:.4f}, WAcc: {avg_train_weighted_acc:.4f}')
        print(f'  Val   - Loss: {avg_val_loss:.4f}, Acc: {avg_val_acc:.4f}, WAcc: {avg_val_weighted_acc:.4f}')
        print(f'  Valid Batches: Train={valid_batches}, Val={val_batches}')
        
        # 保存检查点
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'val_loss': avg_val_loss,
            'val_acc': avg_val_acc,
            'val_weighted_acc': avg_val_weighted_acc,
            'codon_weights': codon_weights
        }, 'weighted_model_checkpoint.pth')
        
        # 保存最佳模型（基于加权准确度）
        if avg_val_weighted_acc > best_accuracy:
            best_accuracy = avg_val_weighted_acc
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': avg_val_loss,
                'val_acc': avg_val_acc,
                'val_weighted_acc': avg_val_weighted_acc,
                'codon_weights': codon_weights
            }, 'weighted_best_model.pth')
            print(f'  🎯 保存最佳模型 (加权准确度: {best_accuracy:.4f})')
        else:
            patience_counter += 1
            print(f'  ⏳ 加权准确度未改善 ({patience_counter}/{patience})')
        
        # 检查是否达到目标准确度
        if avg_val_weighted_acc >= 0.99:
            print(f'  🎉 达到目标加权准确度 0.99，停止训练')
            break
            
        # 早停检查
        if patience_counter >= patience:
            print(f'  ⏹️ 早停：加权准确度连续{patience}轮未改善')
            break
            
        scheduler.step()
        print(f'  📈 Learning Rate: {scheduler.get_last_lr()[0]:.8f}')
        print()
    
    print(f"训练完成！最佳加权准确度: {best_accuracy:.4f}")

if __name__ == "__main__":
    train_weighted_model()
