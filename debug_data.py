#!/usr/bin/env python3
"""
Debug data processing issues
"""

import pandas as pd

def debug_first_sequence():
    """Debug the first sequence to understand the issue"""
    df = pd.read_csv("processed_BL21_data.csv")
    
    # Get first sequence
    protein = df.iloc[0]['protein_sequence']
    nucleotide = df.iloc[0]['nucleotide_sequence']
    
    print(f"Protein: {protein}")
    print(f"Length: {len(protein)}")
    print(f"Nucleotide: {nucleotide}")
    print(f"Length: {len(nucleotide)}")
    print(f"Expected length: {len(protein) * 3}")
    
    # Check if lengths match
    if len(protein) * 3 == len(nucleotide):
        print("✅ Lengths match")
    else:
        print("❌ Lengths don't match")
        return
    
    # Genetic code
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    print("\nChecking amino acid-codon correspondence:")
    for i in range(len(protein)):
        codon_start = i * 3
        codon = nucleotide[codon_start:codon_start + 3]
        expected_aa = genetic_code.get(codon, '?')
        actual_aa = protein[i]
        
        match = "✅" if expected_aa == actual_aa else "❌"
        print(f"Position {i:2d}: {actual_aa} <- {codon} -> {expected_aa} {match}")
        
        if expected_aa != actual_aa:
            print(f"  MISMATCH at position {i}")
            break

if __name__ == "__main__":
    debug_first_sequence()
