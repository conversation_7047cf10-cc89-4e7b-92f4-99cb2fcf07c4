#!/usr/bin/env python3
"""
模型集成训练脚本
通过训练多个不同的模型并集成来提升准确度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
warnings.filterwarnings('ignore')

from fixed_training import FixedCodonPreferenceModel, calculate_accuracy
from improved_training import ImprovedCodonDataset, collate_fn

class EnsembleCodonModel(nn.Module):
    """集成密码子预测模型"""
    
    def __init__(self, num_models=5):
        super().__init__()
        self.num_models = num_models
        self.models = nn.ModuleList()
        
        # 创建多个不同架构的子模型
        for i in range(num_models):
            model = self._create_diverse_model(i)
            self.models.append(model)
    
    def _create_diverse_model(self, model_id):
        """创建不同架构的模型"""
        from transformers import EsmModel, EsmTokenizer
        
        # 基础ESM2模型
        try:
            local_path = "/home/<USER>/.cache/huggingface/hub/models--facebook--esm2_t33_650M_UR50D/snapshots/255893e6608ab942fb16da47f62667c303c571d6"
            tokenizer = EsmTokenizer.from_pretrained(local_path)
            esm2 = EsmModel.from_pretrained(local_path)
        except:
            tokenizer = EsmTokenizer.from_pretrained("facebook/esm2_t33_650M_UR50D")
            esm2 = EsmModel.from_pretrained("facebook/esm2_t33_650M_UR50D")
        
        # 冻结ESM2
        for param in esm2.parameters():
            param.requires_grad = False
        
        hidden_size = esm2.config.hidden_size
        
        # 构建密码子词汇表
        genetic_code = {
            'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
            'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
            'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
            'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
            'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
            'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
            'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
            'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
            'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
            'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
            'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
            'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
            'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
            'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
            'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
        }
        
        codons = [codon for codon, aa in genetic_code.items() if aa != '*']
        codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
        num_codons = len(codon_to_idx)
        
        # 根据model_id创建不同架构
        if model_id == 0:
            # 深层网络
            predictor = nn.Sequential(
                nn.LayerNorm(hidden_size),
                nn.Dropout(0.2),
                nn.Linear(hidden_size, 512),
                nn.LayerNorm(512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, 256),
                nn.LayerNorm(256),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(256, 128),
                nn.ReLU(),
                nn.Linear(128, num_codons)
            )
        elif model_id == 1:
            # 宽层网络
            predictor = nn.Sequential(
                nn.LayerNorm(hidden_size),
                nn.Dropout(0.1),
                nn.Linear(hidden_size, 1024),
                nn.LayerNorm(1024),
                nn.ReLU(),
                nn.Dropout(0.4),
                nn.Linear(1024, num_codons)
            )
        elif model_id == 2:
            # 残差网络
            class ResidualBlock(nn.Module):
                def __init__(self, dim):
                    super().__init__()
                    self.linear1 = nn.Linear(dim, dim)
                    self.linear2 = nn.Linear(dim, dim)
                    self.norm1 = nn.LayerNorm(dim)
                    self.norm2 = nn.LayerNorm(dim)
                    self.dropout = nn.Dropout(0.2)
                
                def forward(self, x):
                    residual = x
                    x = self.norm1(x)
                    x = F.relu(self.linear1(x))
                    x = self.dropout(x)
                    x = self.norm2(x)
                    x = self.linear2(x)
                    x = self.dropout(x)
                    return F.relu(x + residual)
            
            predictor = nn.Sequential(
                nn.LayerNorm(hidden_size),
                nn.Linear(hidden_size, 512),
                ResidualBlock(512),
                ResidualBlock(512),
                nn.Dropout(0.3),
                nn.Linear(512, num_codons)
            )
        elif model_id == 3:
            # 注意力网络
            class AttentionLayer(nn.Module):
                def __init__(self, dim):
                    super().__init__()
                    self.attention = nn.MultiheadAttention(dim, num_heads=8, dropout=0.1)
                    self.norm = nn.LayerNorm(dim)
                    self.dropout = nn.Dropout(0.2)
                
                def forward(self, x):
                    # x: (batch, seq, dim)
                    x = x.transpose(0, 1)  # (seq, batch, dim)
                    attn_out, _ = self.attention(x, x, x)
                    x = self.norm(x + self.dropout(attn_out))
                    return x.transpose(0, 1)  # (batch, seq, dim)
            
            predictor = nn.Sequential(
                nn.LayerNorm(hidden_size),
                nn.Linear(hidden_size, 512),
                AttentionLayer(512),
                nn.Dropout(0.3),
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Linear(256, num_codons)
            )
        else:
            # 简单网络
            predictor = nn.Sequential(
                nn.LayerNorm(hidden_size),
                nn.Dropout(0.3),
                nn.Linear(hidden_size, 256),
                nn.ReLU(),
                nn.Dropout(0.4),
                nn.Linear(256, num_codons)
            )
        
        # 创建完整模型
        class SingleModel(nn.Module):
            def __init__(self, esm2, tokenizer, predictor, codon_to_idx):
                super().__init__()
                self.esm2 = esm2
                self.tokenizer = tokenizer
                self.predictor = predictor
                self.codon_to_idx = codon_to_idx
            
            def forward(self, protein_sequences):
                encoded = self.tokenizer(
                    protein_sequences,
                    padding=True,
                    truncation=True,
                    max_length=1024,
                    return_tensors="pt"
                )
                
                input_ids = encoded['input_ids'].to(next(self.parameters()).device)
                attention_mask = encoded['attention_mask'].to(next(self.parameters()).device)
                
                with torch.no_grad():
                    esm_output = self.esm2(input_ids=input_ids, attention_mask=attention_mask)
                
                logits = self.predictor(esm_output.last_hidden_state)
                return torch.clamp(logits, min=-10, max=10)
        
        return SingleModel(esm2, tokenizer, predictor, codon_to_idx)
    
    def forward(self, protein_sequences):
        """集成前向传播"""
        outputs = []
        for model in self.models:
            output = model(protein_sequences)
            outputs.append(output)
        
        # 平均集成
        ensemble_output = torch.stack(outputs).mean(dim=0)
        return ensemble_output
    
    @property
    def codon_to_idx(self):
        return self.models[0].codon_to_idx

def train_ensemble_model():
    """训练集成模型"""
    print("🎯 开始集成模型训练")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载数据
    df = pd.read_csv("processed_BL21_data.csv")
    df = df[df['protein_length'] <= 512]
    
    proteins = df['protein_sequence'].tolist()
    nucleotides = df['nucleotide_sequence'].tolist()
    
    # 创建集成模型
    ensemble_model = EnsembleCodonModel(num_models=3)  # 3个子模型
    ensemble_model = ensemble_model.to(device)
    
    # 数据集
    dataset = ImprovedCodonDataset(proteins, nucleotides, ensemble_model.codon_to_idx)
    
    # 数据划分
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, collate_fn=collate_fn)
    
    # 优化器 - 为每个子模型使用不同的学习率
    optimizers = []
    for i, model in enumerate(ensemble_model.models):
        lr = 1e-5 * (0.8 ** i)  # 递减学习率
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=0.01)
        optimizers.append(optimizer)
    
    # 损失函数
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    
    best_val_acc = 0.0
    
    print("开始集成训练...")
    
    for epoch in range(100):
        # 训练阶段
        ensemble_model.train()
        train_loss = 0.0
        train_acc = 0.0
        train_batches = 0
        
        for batch in tqdm(train_loader, desc=f'Epoch {epoch+1}/100', leave=False):
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                # 清零梯度
                for optimizer in optimizers:
                    optimizer.zero_grad()
                
                # 前向传播
                logits = ensemble_model(proteins)
                
                # 对齐维度
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                # 计算损失
                loss = criterion(
                    logits.reshape(-1, logits.size(-1)),
                    targets.reshape(-1)
                )
                
                # 反向传播
                loss.backward()
                
                # 更新参数
                for optimizer in optimizers:
                    torch.nn.utils.clip_grad_norm_(ensemble_model.parameters(), max_norm=1.0)
                    optimizer.step()
                
                train_loss += loss.item()
                train_acc += calculate_accuracy(logits, targets)
                train_batches += 1
                
            except Exception as e:
                print(f"训练异常: {e}")
                continue
        
        # 验证阶段
        ensemble_model.eval()
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = ensemble_model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    val_acc += calculate_accuracy(logits, targets)
                    val_batches += 1
                    
                except Exception as e:
                    continue
        
        # 计算平均值
        avg_train_acc = train_acc / max(train_batches, 1)
        avg_val_acc = val_acc / max(val_batches, 1)
        
        print(f'Epoch {epoch+1}: Train={avg_train_acc*100:.1f}%, Val={avg_val_acc*100:.1f}%')
        
        # 保存最佳模型
        if avg_val_acc > best_val_acc:
            best_val_acc = avg_val_acc
            torch.save(ensemble_model.state_dict(), 'ensemble_best_model.pth')
            
            if avg_val_acc >= 0.8:
                print("🎉 集成模型达到80%目标！")
                break
    
    print(f"集成模型最佳验证准确度: {best_val_acc*100:.1f}%")
    return best_val_acc

if __name__ == "__main__":
    train_ensemble_model()
