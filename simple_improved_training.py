#!/usr/bin/env python3
"""
简化改进训练脚本 v2
基于抗过拟合成功基础，通过简单有效的改进提升准确度
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from tqdm import tqdm
import json
import warnings
import matplotlib.pyplot as plt
warnings.filterwarnings('ignore')

from fixed_training import FixedCodonPreferenceModel, calculate_accuracy
from improved_training import ImprovedCodonDataset, collate_fn

class SimpleBalancedLoss(nn.Module):
    """简化的平衡损失函数"""
    
    def __init__(self, class_weights=None, ignore_index=-100):
        super().__init__()
        self.ignore_index = ignore_index
        if class_weights is not None:
            self.register_buffer('class_weights', class_weights)
        else:
            self.class_weights = None
    
    def forward(self, logits, targets):
        """计算损失"""
        # 确保张量是连续的
        logits = logits.contiguous()
        targets = targets.contiguous()
        
        # 重塑张量
        batch_size, seq_len, num_classes = logits.shape
        logits_flat = logits.view(-1, num_classes)
        targets_flat = targets.view(-1)
        
        # 使用标准交叉熵损失
        if self.class_weights is not None:
            loss = F.cross_entropy(
                logits_flat, 
                targets_flat, 
                weight=self.class_weights,
                ignore_index=self.ignore_index,
                reduction='mean'
            )
        else:
            loss = F.cross_entropy(
                logits_flat, 
                targets_flat, 
                ignore_index=self.ignore_index,
                reduction='mean'
            )
        
        return loss

def load_all_data(data_file="processed_BL21_data.csv", max_length=512):
    """加载所有可用数据"""
    print(f"正在加载数据: {data_file}")
    
    df = pd.read_csv(data_file)
    print(f"原始数据: {len(df)} 条")
    
    # 使用更宽松的长度限制
    df_filtered = df[df['protein_length'] <= max_length]
    print(f"长度过滤后: {len(df_filtered)} 条 (≤{max_length})")
    
    return df_filtered['protein_sequence'].tolist(), df_filtered['nucleotide_sequence'].tolist()

def create_simple_weights(codon_counts):
    """创建简单的平衡权重"""
    total_count = sum(codon_counts.values())
    weights = {}
    
    for codon, count in codon_counts.items():
        if count > 0:
            # 简单的逆频率权重，限制范围
            weight = total_count / (len(codon_counts) * count)
            # 限制权重范围在 [0.5, 3.0]
            weight = max(0.5, min(3.0, weight))
            weights[codon] = weight
        else:
            weights[codon] = 1.0
    
    return weights

def train_simple_improved_model():
    """训练简化的改进模型"""
    print("开始训练简化的改进模型")
    
    # 加载数据
    proteins, nucleotides = load_all_data(max_length=512)
    print(f"使用 {len(proteins)} 条数据进行训练")
    
    # 创建模型
    model = FixedCodonPreferenceModel()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # 创建数据集
    dataset = ImprovedCodonDataset(proteins, nucleotides, model.codon_to_idx)
    print(f"有效数据集大小: {len(dataset)}")
    
    # 划分数据
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 数据加载器
    batch_size = 6  # 减小批次大小避免内存问题
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)
    
    # 创建简单的权重
    try:
        with open('improved_codon_weights.json', 'r') as f:
            weight_info = json.load(f)
        codon_counts = weight_info['codon_counts']
        simple_weights = create_simple_weights(codon_counts)
        
        # 转换为张量
        weight_tensor = torch.ones(len(model.codon_to_idx))
        for codon, weight in simple_weights.items():
            if codon in model.codon_to_idx:
                idx = model.codon_to_idx[codon]
                weight_tensor[idx] = weight
        
        weight_tensor = weight_tensor.to(device)
        print(f"权重统计: min={weight_tensor.min():.3f}, max={weight_tensor.max():.3f}")
        
    except FileNotFoundError:
        print("未找到权重文件，使用均匀权重")
        weight_tensor = None
    
    # 损失函数
    criterion = SimpleBalancedLoss(class_weights=weight_tensor)
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=2e-5, weight_decay=0.01)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=30)
    
    # 训练历史
    train_losses = []
    val_losses = []
    train_accuracies = []
    val_accuracies = []
    
    best_accuracy = 0.0
    patience = 8
    patience_counter = 0
    
    print(f"训练集大小: {len(train_dataset)}")
    print(f"验证集大小: {len(val_dataset)}")
    print(f"批次大小: {batch_size}")
    
    for epoch in range(30):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_acc = 0.0
        valid_batches = 0
        
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/30 [Train]')
        for batch_idx, batch in enumerate(train_pbar):
            try:
                proteins = batch['proteins']
                targets = batch['codons'].to(device)
                
                optimizer.zero_grad()
                
                # 前向传播
                logits = model(proteins)
                
                # 对齐维度
                if logits.shape[1] > targets.shape[1]:
                    start_idx = 1
                    end_idx = start_idx + targets.shape[1]
                    logits = logits[:, start_idx:end_idx, :]
                
                # 计算损失
                loss = criterion(logits, targets)
                
                # 检查损失是否有效
                if torch.isnan(loss) or torch.isinf(loss) or loss.item() > 100:
                    print(f"跳过异常批次: loss={loss.item()}")
                    continue
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                if torch.isnan(grad_norm):
                    print("跳过NaN梯度批次")
                    continue
                
                optimizer.step()
                
                # 计算准确度
                acc = calculate_accuracy(logits, targets)
                
                train_loss += loss.item()
                train_acc += acc
                valid_batches += 1
                
                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.3f}',
                    'Acc': f'{acc:.3f}',
                    'LR': f'{scheduler.get_last_lr()[0]:.2e}'
                })
                
            except Exception as e:
                print(f"训练批次 {batch_idx} 异常: {e}")
                continue
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_acc = 0.0
        val_batches = 0
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/30 [Val]')
            for batch in val_pbar:
                try:
                    proteins = batch['proteins']
                    targets = batch['codons'].to(device)
                    
                    logits = model(proteins)
                    
                    if logits.shape[1] > targets.shape[1]:
                        start_idx = 1
                        end_idx = start_idx + targets.shape[1]
                        logits = logits[:, start_idx:end_idx, :]
                    
                    loss = criterion(logits, targets)
                    acc = calculate_accuracy(logits, targets)
                    
                    val_loss += loss.item()
                    val_acc += acc
                    val_batches += 1
                    
                    val_pbar.set_postfix({
                        'Loss': f'{loss.item():.3f}',
                        'Acc': f'{acc:.3f}'
                    })
                    
                except Exception as e:
                    print(f"验证批次异常: {e}")
                    continue
        
        # 计算平均值
        if valid_batches > 0:
            avg_train_loss = train_loss / valid_batches
            avg_train_acc = train_acc / valid_batches
        else:
            avg_train_loss = float('inf')
            avg_train_acc = 0.0
            
        if val_batches > 0:
            avg_val_loss = val_loss / val_batches
            avg_val_acc = val_acc / val_batches
        else:
            avg_val_loss = float('inf')
            avg_val_acc = 0.0
        
        # 记录历史
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        train_accuracies.append(avg_train_acc)
        val_accuracies.append(avg_val_acc)
        
        print(f'Epoch {epoch+1}/30:')
        print(f'  Train - Loss: {avg_train_loss:.4f}, Acc: {avg_train_acc:.4f} ({avg_train_acc*100:.1f}%)')
        print(f'  Val   - Loss: {avg_val_loss:.4f}, Acc: {avg_val_acc:.4f} ({avg_val_acc*100:.1f}%)')
        print(f'  Valid Batches: Train={valid_batches}, Val={val_batches}')
        
        # 保存最佳模型
        if avg_val_acc > best_accuracy:
            best_accuracy = avg_val_acc
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': avg_val_loss,
                'val_accuracy': avg_val_acc,
            }, 'simple_best_model.pth')
            print(f'  🎉 保存最佳模型 (准确度: {best_accuracy:.4f} = {best_accuracy*100:.1f}%)')
        else:
            patience_counter += 1
            print(f'  准确度未改善 ({patience_counter}/{patience})')
        
        # 早停检查
        if patience_counter >= patience:
            print(f'  早停：准确度连续{patience}轮未改善')
            break
        
        scheduler.step()
        print()
    
    # 绘制训练曲线
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 3, 2)
    plt.plot([acc * 100 for acc in train_accuracies], label='Train Accuracy')
    plt.plot([acc * 100 for acc in val_accuracies], label='Val Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.title('Training and Validation Accuracy')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 3, 3)
    plt.plot([acc * 100 for acc in val_accuracies], 'b-', linewidth=2, label='Val Accuracy')
    plt.axhline(y=80, color='r', linestyle='--', linewidth=2, label='Target 80%')
    plt.axhline(y=best_accuracy*100, color='g', linestyle=':', linewidth=2, label=f'Best {best_accuracy*100:.1f}%')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.title('Progress Towards 80% Target')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('simple_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("训练完成！")
    print(f"最佳验证准确度: {best_accuracy:.4f} ({best_accuracy*100:.1f}%)")
    
    return best_accuracy

if __name__ == "__main__":
    best_acc = train_simple_improved_model()
    
    if best_acc >= 0.8:
        print("🎉 恭喜！模型达到了80%的目标准确度！")
    elif best_acc >= 0.6:
        print(f"模型准确度为 {best_acc*100:.1f}%，有很大改进！")
        print("建议进一步优化：")
        print("1. 增加训练轮数到50")
        print("2. 尝试更大的模型")
        print("3. 使用数据增强")
    else:
        print(f"模型准确度为 {best_acc*100:.1f}%，需要进一步改进。")
        print("建议：")
        print("1. 检查数据质量")
        print("2. 调整模型架构")
        print("3. 尝试不同的训练策略")
