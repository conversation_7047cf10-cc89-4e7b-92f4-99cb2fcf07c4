#!/usr/bin/env python3
"""
密码子偏好性预测器
用户友好的预测脚本，输入蛋白质序列输出对应的核酸序列
"""

import torch
import argparse
import sys
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

from codon_preference_model import CodonPreferenceModel

class CodonPredictor:
    """密码子偏好性预测器类"""
    
    def __init__(self, model_path="weighted_best_model.pth"):
        """
        初始化预测器
        
        Args:
            model_path: 训练好的模型路径
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 加载模型 - 使用修复版模型
        from fixed_training import FixedCodonPreferenceModel
        self.model = FixedCodonPreferenceModel()
        self.model.to(self.device)
        
        # 加载训练好的权重
        if os.path.exists(model_path):
            print(f"加载模型权重: {model_path}")
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 处理不同的保存格式
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
                print(f"模型训练轮数: {checkpoint.get('epoch', 'Unknown')}")
                print(f"验证准确度: {checkpoint.get('accuracy', 'Unknown'):.4f}")
            else:
                self.model.load_state_dict(checkpoint)
            
            self.model.eval()
            print("模型加载成功")
        else:
            print(f"警告: 模型文件 {model_path} 不存在，使用未训练的模型")
    
    def validate_protein_sequence(self, protein_seq):
        """验证蛋白质序列格式"""
        # 转换为大写
        protein_seq = protein_seq.upper().strip()
        
        # 检查是否只包含标准氨基酸
        valid_aa = set('ACDEFGHIKLMNPQRSTVWY')
        invalid_chars = set(protein_seq) - valid_aa
        
        if invalid_chars:
            raise ValueError(f"蛋白质序列包含无效字符: {invalid_chars}")
        
        # 检查长度限制
        if len(protein_seq) > 1024:
            raise ValueError(f"蛋白质序列过长 ({len(protein_seq)} > 1024)")
        
        if len(protein_seq) == 0:
            raise ValueError("蛋白质序列不能为空")
        
        return protein_seq
    
    def predict_single(self, protein_sequence):
        """
        预测单个蛋白质序列对应的核酸序列
        
        Args:
            protein_sequence: 蛋白质序列字符串
            
        Returns:
            dict: 包含预测结果的字典
        """
        # 验证输入
        protein_seq = self.validate_protein_sequence(protein_sequence)
        
        # 预测
        with torch.no_grad():
            dna_sequences, codon_probs = self.model.predict_codon_sequence([protein_seq])
        
        dna_seq = dna_sequences[0]
        
        # 验证预测结果
        predicted_codons = [dna_seq[i:i+3] for i in range(0, len(dna_seq), 3)]
        back_translated = ''.join([self.model.genetic_code.get(codon, 'X') for codon in predicted_codons])
        
        # 计算置信度（基于概率分布的熵）
        confidence_scores = []
        for i in range(len(protein_seq)):
            if i + 1 < codon_probs.shape[1]:
                probs = codon_probs[0, i + 1, :].cpu().numpy()
                # 计算归一化熵作为置信度
                entropy = -sum(p * torch.log(torch.tensor(p + 1e-8)) for p in probs if p > 0)
                confidence = 1.0 / (1.0 + entropy.item())
                confidence_scores.append(confidence)
        
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        
        return {
            'input_protein': protein_seq,
            'predicted_dna': dna_seq,
            'predicted_codons': predicted_codons,
            'back_translated': back_translated,
            'is_valid': protein_seq == back_translated,
            'confidence': avg_confidence,
            'length_protein': len(protein_seq),
            'length_dna': len(dna_seq),
            'codon_count': len(predicted_codons)
        }
    
    def predict_batch(self, protein_sequences):
        """
        批量预测多个蛋白质序列
        
        Args:
            protein_sequences: 蛋白质序列列表
            
        Returns:
            list: 预测结果列表
        """
        results = []
        
        for i, protein_seq in enumerate(protein_sequences):
            try:
                result = self.predict_single(protein_seq)
                result['sequence_id'] = i + 1
                results.append(result)
            except Exception as e:
                print(f"序列 {i+1} 预测失败: {e}")
                results.append({
                    'sequence_id': i + 1,
                    'input_protein': protein_seq,
                    'error': str(e),
                    'is_valid': False
                })
        
        return results
    
    def predict_from_file(self, input_file, output_file=None):
        """
        从文件读取蛋白质序列并预测
        
        Args:
            input_file: 输入文件路径（每行一个蛋白质序列）
            output_file: 输出文件路径（可选）
        """
        # 读取输入文件
        with open(input_file, 'r') as f:
            protein_sequences = [line.strip() for line in f if line.strip()]
        
        print(f"从 {input_file} 读取了 {len(protein_sequences)} 个蛋白质序列")
        
        # 批量预测
        results = self.predict_batch(protein_sequences)
        
        # 输出结果
        if output_file:
            with open(output_file, 'w') as f:
                f.write("序列ID\t蛋白质序列\t预测DNA序列\t验证结果\t置信度\n")
                for result in results:
                    if 'error' not in result:
                        f.write(f"{result['sequence_id']}\t{result['input_protein']}\t"
                               f"{result['predicted_dna']}\t{result['is_valid']}\t{result['confidence']:.4f}\n")
                    else:
                        f.write(f"{result['sequence_id']}\t{result['input_protein']}\t错误: {result['error']}\t"
                               f"False\t0.0000\n")
            print(f"结果已保存到 {output_file}")
        
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='密码子偏好性预测器')
    parser.add_argument('--model', '-m', default='best_codon_model.pth', 
                       help='模型文件路径 (默认: best_codon_model.pth)')
    parser.add_argument('--sequence', '-s', type=str, 
                       help='单个蛋白质序列')
    parser.add_argument('--input', '-i', type=str, 
                       help='输入文件路径（每行一个蛋白质序列）')
    parser.add_argument('--output', '-o', type=str, 
                       help='输出文件路径')
    parser.add_argument('--interactive', action='store_true', 
                       help='交互模式')
    
    args = parser.parse_args()
    
    # 创建预测器
    try:
        predictor = CodonPredictor(args.model)
    except Exception as e:
        print(f"模型加载失败: {e}")
        return 1
    
    # 单个序列预测
    if args.sequence:
        try:
            result = predictor.predict_single(args.sequence)
            print("\n=== 预测结果 ===")
            print(f"输入蛋白质序列: {result['input_protein']}")
            print(f"预测DNA序列:     {result['predicted_dna']}")
            print(f"回译验证:       {result['back_translated']}")
            print(f"验证结果:       {'✓ 正确' if result['is_valid'] else '✗ 错误'}")
            print(f"置信度:         {result['confidence']:.4f}")
            print(f"序列长度:       蛋白质={result['length_protein']}, DNA={result['length_dna']}")
            
            # 显示密码子详情
            print(f"\n密码子详情:")
            for i, (aa, codon) in enumerate(zip(result['input_protein'], result['predicted_codons'])):
                print(f"  {i+1:3d}: {aa} -> {codon}")
                
        except Exception as e:
            print(f"预测失败: {e}")
            return 1
    
    # 文件批量预测
    elif args.input:
        try:
            results = predictor.predict_from_file(args.input, args.output)
            
            # 统计结果
            total = len(results)
            valid = sum(1 for r in results if r.get('is_valid', False))
            avg_confidence = sum(r.get('confidence', 0) for r in results) / total if total > 0 else 0
            
            print(f"\n=== 批量预测统计 ===")
            print(f"总序列数:       {total}")
            print(f"验证通过:       {valid} ({valid/total*100:.1f}%)")
            print(f"平均置信度:     {avg_confidence:.4f}")
            
        except Exception as e:
            print(f"批量预测失败: {e}")
            return 1
    
    # 交互模式
    elif args.interactive:
        print("=== 密码子偏好性预测器 - 交互模式 ===")
        print("输入蛋白质序列进行预测，输入 'quit' 退出")
        
        while True:
            try:
                protein_seq = input("\n请输入蛋白质序列: ").strip()
                
                if protein_seq.lower() == 'quit':
                    break
                
                if not protein_seq:
                    continue
                
                result = predictor.predict_single(protein_seq)
                
                print(f"\n预测DNA序列: {result['predicted_dna']}")
                print(f"验证结果: {'✓ 正确' if result['is_valid'] else '✗ 错误'}")
                print(f"置信度: {result['confidence']:.4f}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"预测失败: {e}")
        
        print("退出交互模式")
    
    else:
        parser.print_help()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
