#!/usr/bin/env python3
"""
测试密码子偏好性预测器
"""

from codon_predictor import CodonPredictor
import pandas as pd

def test_predictor():
    """测试预测器功能"""
    print("=== 测试密码子偏好性预测器 ===")
    
    # 创建预测器（使用最新的检查点）
    try:
        predictor = CodonPredictor("latest_checkpoint.pth")
    except:
        print("使用未训练的模型进行测试...")
        predictor = CodonPredictor("nonexistent.pth")
    
    # 测试序列
    test_sequences = [
        "MFVFLVLLPLVSSQCVNLTTRTQLPPA",  # 短序列
        "MKRISTTITTTITITTGNGAG",        # 另一个短序列
        "ATCGATCGATCG",                  # 简单重复序列
        "ACDEFGHIKLMNPQRSTVWY"          # 包含所有氨基酸
    ]
    
    print(f"\n测试 {len(test_sequences)} 个蛋白质序列:")
    
    for i, protein_seq in enumerate(test_sequences, 1):
        print(f"\n--- 测试序列 {i} ---")
        print(f"蛋白质序列: {protein_seq}")
        
        try:
            result = predictor.predict_single(protein_seq)
            
            print(f"预测DNA序列: {result['predicted_dna']}")
            print(f"回译验证:   {result['back_translated']}")
            print(f"验证结果:   {'✓ 正确' if result['is_valid'] else '✗ 错误'}")
            print(f"置信度:     {result['confidence']:.4f}")
            print(f"长度比例:   {result['length_dna']}/{result['length_protein']} = {result['length_dna']/result['length_protein']:.1f}")
            
            # 显示前几个密码子
            print("前5个密码子:")
            for j, (aa, codon) in enumerate(zip(protein_seq[:5], result['predicted_codons'][:5])):
                print(f"  {aa} -> {codon}")
                
        except Exception as e:
            print(f"预测失败: {e}")
    
    # 测试批量预测
    print(f"\n--- 批量预测测试 ---")
    try:
        batch_results = predictor.predict_batch(test_sequences)
        
        valid_count = sum(1 for r in batch_results if r.get('is_valid', False))
        total_count = len(batch_results)
        avg_confidence = sum(r.get('confidence', 0) for r in batch_results) / total_count
        
        print(f"批量预测结果:")
        print(f"  总数: {total_count}")
        print(f"  验证通过: {valid_count} ({valid_count/total_count*100:.1f}%)")
        print(f"  平均置信度: {avg_confidence:.4f}")
        
    except Exception as e:
        print(f"批量预测失败: {e}")
    
    # 测试真实数据
    print(f"\n--- 真实数据测试 ---")
    try:
        # 从处理过的数据中取几个样本
        df = pd.read_csv("processed_BL21_data.csv")
        sample_data = df.head(3)
        
        for idx, row in sample_data.iterrows():
            protein_seq = row['protein_sequence']
            true_dna = row['nucleotide_sequence']
            
            print(f"\n真实数据样本 {idx+1}:")
            print(f"蛋白质: {protein_seq[:50]}...")
            print(f"真实DNA: {true_dna[:50]}...")
            
            try:
                result = predictor.predict_single(protein_seq)
                predicted_dna = result['predicted_dna']
                
                print(f"预测DNA: {predicted_dna[:50]}...")
                print(f"验证结果: {'✓ 正确' if result['is_valid'] else '✗ 错误'}")
                print(f"置信度: {result['confidence']:.4f}")
                
                # 计算与真实序列的相似度
                if len(predicted_dna) == len(true_dna):
                    matches = sum(1 for a, b in zip(predicted_dna, true_dna) if a == b)
                    similarity = matches / len(true_dna)
                    print(f"与真实序列相似度: {similarity:.4f} ({matches}/{len(true_dna)})")
                else:
                    print(f"长度不匹配: 预测={len(predicted_dna)}, 真实={len(true_dna)}")
                
            except Exception as e:
                print(f"预测失败: {e}")
                
    except Exception as e:
        print(f"真实数据测试失败: {e}")

def create_test_file():
    """创建测试文件"""
    test_sequences = [
        "MFVFLVLLPLVSSQCVNLTTRTQLPPA",
        "MKRISTTITTTITITTGNGAG",
        "ATCGATCGATCG",
        "ACDEFGHIKLMNPQRSTVWY",
        "MGSSHHHHHHSSGLVPRGSHMRGPNPTAASLEASAGPFTVRSFTVSRPSGYGAGTVYYPTNAGGTVGAIAIVPGYTARQSSIKWWGPRLASHGFVVITIDTNSTLDQPSSRSSQQMAALRQVASLNGTSSSPIYGKVDTARMGVMGWSMGGGGSLISAANNPSLKAAAPQAPWDSSTNFSSVTVPTLIFACENDSIAPVNSSALPIYDSMSRNAKQFLEINGGSHSCANSGNSNQALIGKKGVAWMKRFPTSREJ"
    ]
    
    with open("test_proteins.txt", "w") as f:
        for seq in test_sequences:
            f.write(seq + "\n")
    
    print("创建测试文件: test_proteins.txt")

if __name__ == "__main__":
    test_predictor()
    create_test_file()
    
    print("\n=== 使用说明 ===")
    print("1. 单个序列预测:")
    print("   python codon_predictor.py -s MFVFLVLLPLVSSQCVNLTTRTQLPPA")
    print()
    print("2. 文件批量预测:")
    print("   python codon_predictor.py -i test_proteins.txt -o results.txt")
    print()
    print("3. 交互模式:")
    print("   python codon_predictor.py --interactive")
    print()
    print("4. 指定模型文件:")
    print("   python codon_predictor.py -m best_codon_model.pth -s MFVFLVLLPLVSSQCVNLTTRTQLPPA")
