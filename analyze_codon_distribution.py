#!/usr/bin/env python3
"""
分析密码子分布不均衡问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter

def analyze_codon_imbalance():
    """分析密码子分布不均衡"""
    
    # 标准遗传密码表
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TAA': '*', 'TAG': '*',
        'TGT': 'C', 'TGC': 'C', 'TGA': '*', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    # 构建氨基酸到密码子的映射
    aa_to_codons = defaultdict(list)
    for codon, aa in genetic_code.items():
        if aa != '*':  # 排除终止密码子
            aa_to_codons[aa].append(codon)
    
    print("=== 氨基酸密码子数量分析 ===")
    print("氨基酸\t密码子数量\t密码子列表")
    print("-" * 60)
    
    aa_codon_counts = {}
    for aa in sorted(aa_to_codons.keys()):
        codons = aa_to_codons[aa]
        count = len(codons)
        aa_codon_counts[aa] = count
        print(f"{aa}\t{count}\t\t{', '.join(codons)}")
    
    # 统计密码子数量分布
    count_distribution = Counter(aa_codon_counts.values())
    print(f"\n=== 密码子数量分布统计 ===")
    for count, freq in sorted(count_distribution.items()):
        aas = [aa for aa, c in aa_codon_counts.items() if c == count]
        print(f"{count}个密码子: {freq}个氨基酸 ({', '.join(aas)})")
    
    # 分析训练数据中的密码子使用频率
    print(f"\n=== 训练数据中的密码子使用分析 ===")
    
    df = pd.read_csv("processed_BL21_data.csv")
    
    # 统计所有密码子的使用频率
    codon_counts = Counter()
    aa_codon_usage = defaultdict(Counter)
    
    for _, row in df.iterrows():
        dna_seq = row['nucleotide_sequence']
        protein_seq = row['protein_sequence']
        
        # 移除可能的终止密码子
        if len(dna_seq) >= 3 and dna_seq[-3:] in ['TAA', 'TAG', 'TGA']:
            dna_seq = dna_seq[:-3]
        
        # 提取密码子
        for i in range(0, len(dna_seq), 3):
            codon = dna_seq[i:i+3]
            if len(codon) == 3 and codon in genetic_code:
                aa = genetic_code[codon]
                if aa != '*':
                    codon_counts[codon] += 1
                    aa_codon_usage[aa][codon] += 1
    
    print(f"总密码子数量: {sum(codon_counts.values())}")
    print(f"不同密码子种类: {len(codon_counts)}")
    
    # 计算每个氨基酸的密码子使用偏好
    print(f"\n=== 各氨基酸密码子使用偏好 ===")
    aa_weights = {}
    codon_weights = {}
    
    for aa in sorted(aa_to_codons.keys()):
        codons = aa_to_codons[aa]
        total_usage = sum(aa_codon_usage[aa].values())
        
        if total_usage > 0:
            print(f"\n{aa} (总使用次数: {total_usage}):")
            
            # 计算权重
            aa_weight = 1.0 / len(codons)  # 基于密码子数量的权重
            aa_weights[aa] = aa_weight
            
            for codon in codons:
                usage = aa_codon_usage[aa][codon]
                frequency = usage / total_usage if total_usage > 0 else 0
                
                # 密码子权重：结合氨基酸权重和逆频率权重
                codon_weight = aa_weight * (1.0 / (frequency + 1e-8))
                codon_weights[codon] = codon_weight
                
                print(f"  {codon}: {usage:6d} ({frequency:6.3f}) 权重: {codon_weight:8.3f}")
    
    # 可视化分析
    plt.figure(figsize=(15, 10))
    
    # 子图1：氨基酸密码子数量分布
    plt.subplot(2, 3, 1)
    counts = list(aa_codon_counts.values())
    plt.hist(counts, bins=range(1, 8), alpha=0.7, edgecolor='black')
    plt.xlabel('密码子数量')
    plt.ylabel('氨基酸数量')
    plt.title('氨基酸密码子数量分布')
    plt.xticks(range(1, 7))
    
    # 子图2：密码子使用频率分布
    plt.subplot(2, 3, 2)
    frequencies = list(codon_counts.values())
    plt.hist(frequencies, bins=50, alpha=0.7, edgecolor='black')
    plt.xlabel('使用频率')
    plt.ylabel('密码子数量')
    plt.title('密码子使用频率分布')
    plt.yscale('log')
    
    # 子图3：氨基酸权重分布
    plt.subplot(2, 3, 3)
    weights = list(aa_weights.values())
    plt.hist(weights, bins=20, alpha=0.7, edgecolor='black')
    plt.xlabel('氨基酸权重')
    plt.ylabel('数量')
    plt.title('氨基酸权重分布')
    
    # 子图4：密码子权重分布
    plt.subplot(2, 3, 4)
    codon_weight_values = list(codon_weights.values())
    plt.hist(codon_weight_values, bins=50, alpha=0.7, edgecolor='black')
    plt.xlabel('密码子权重')
    plt.ylabel('数量')
    plt.title('密码子权重分布')
    plt.yscale('log')
    
    # 子图5：氨基酸使用频率 vs 密码子数量
    plt.subplot(2, 3, 5)
    aa_total_usage = [sum(aa_codon_usage[aa].values()) for aa in aa_codon_counts.keys()]
    aa_codon_nums = [aa_codon_counts[aa] for aa in aa_codon_counts.keys()]
    
    plt.scatter(aa_codon_nums, aa_total_usage, alpha=0.7)
    plt.xlabel('密码子数量')
    plt.ylabel('总使用次数')
    plt.title('氨基酸使用频率 vs 密码子数量')
    
    # 添加氨基酸标签
    for aa in aa_codon_counts.keys():
        x = aa_codon_counts[aa]
        y = sum(aa_codon_usage[aa].values())
        plt.annotate(aa, (x, y), xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    # 子图6：不平衡程度分析
    plt.subplot(2, 3, 6)
    imbalance_scores = []
    aa_names = []
    
    for aa in sorted(aa_to_codons.keys()):
        codons = aa_to_codons[aa]
        if len(codons) > 1:
            usages = [aa_codon_usage[aa][codon] for codon in codons]
            total = sum(usages)
            if total > 0:
                # 计算基尼系数作为不平衡度量
                sorted_usages = sorted(usages)
                n = len(sorted_usages)
                cumsum = np.cumsum(sorted_usages)
                gini = (n + 1 - 2 * sum((n + 1 - i) * usage for i, usage in enumerate(sorted_usages, 1))) / (n * total)
                imbalance_scores.append(gini)
                aa_names.append(aa)
    
    plt.barh(range(len(imbalance_scores)), imbalance_scores)
    plt.yticks(range(len(aa_names)), aa_names)
    plt.xlabel('不平衡程度 (基尼系数)')
    plt.title('各氨基酸密码子使用不平衡程度')
    
    plt.tight_layout()
    plt.savefig('codon_imbalance_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 保存权重信息
    weight_info = {
        'aa_weights': aa_weights,
        'codon_weights': codon_weights,
        'aa_codon_counts': aa_codon_counts,
        'codon_usage_counts': dict(codon_counts),
        'aa_codon_usage': {aa: dict(usage) for aa, usage in aa_codon_usage.items()}
    }
    
    import json
    with open('codon_weights.json', 'w') as f:
        json.dump(weight_info, f, indent=2)
    
    print(f"\n权重信息已保存到 codon_weights.json")

    return weight_info

if __name__ == "__main__":
    weight_info = analyze_codon_imbalance()

    print(f"\n=== 关键发现 ===")
    print(f"1. 氨基酸密码子数量范围: 1-6个")
    print(f"2. 单密码子氨基酸: Met(M), Trp(W) - 权重=1.0")
    print(f"3. 六密码子氨基酸: Leu(L), Ser(S), Arg(R) - 权重=0.167")
    print(f"4. 最不平衡的密码子: AGG(R)权重=8.646, CTA(L)权重=4.644")
    print(f"5. 这种不平衡导致模型难以学习正确的密码子偏好")
    print(f"6. 需要使用加权损失函数来平衡训练")

    # 显示一些关键统计
    print(f"\n=== 权重统计 ===")
    codon_weights = weight_info['codon_weights']
    print(f"密码子权重范围: {min(codon_weights.values()):.3f} - {max(codon_weights.values()):.3f}")
    print(f"权重比例: {max(codon_weights.values())/min(codon_weights.values()):.1f}:1")

    # 找出最需要关注的密码子
    sorted_weights = sorted(codon_weights.items(), key=lambda x: x[1], reverse=True)
    print(f"\n最高权重密码子 (需要特别关注):")
    for codon, weight in sorted_weights[:10]:
        aa = weight_info['aa_codon_counts']
        for a, codons in weight_info.get('aa_codon_usage', {}).items():
            if codon in codons:
                print(f"  {codon}({a}): 权重={weight:.3f}")
                break
