#!/usr/bin/env python3
"""
Working XGBoost Comparison
Complete traditional ML vs deep learning comparison
"""

import numpy as np
import pandas as pd
import json
import time
import warnings
warnings.filterwarnings('ignore')

# Import ML libraries
try:
    import xgboost as xgb
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score, classification_report
    from sklearn.ensemble import RandomForestClassifier
    SKLEARN_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ ML libraries not available: {e}")
    SKLEARN_AVAILABLE = False

def create_traditional_ml_features():
    """Create features for traditional ML from the processed data"""
    print("🔧 Creating Traditional ML Features")
    print("=" * 50)
    
    # Load processed data
    try:
        df = pd.read_csv("processed_BL21_data.csv")
        print(f"✅ Loaded {len(df)} samples from processed data")
    except FileNotFoundError:
        print("❌ processed_BL21_data.csv not found")
        return None, None, None
    
    # Filter for reasonable sequence lengths
    df = df[df['protein_length'] <= 512]
    print(f"✅ Using {len(df)} samples (length <= 512)")
    
    # Amino acid properties
    aa_properties = {
        'A': [1.8, 0, 0, 0, 0],   # Ala: hydrophobic, neutral
        'R': [-4.5, 1, 1, 0, 0],  # Arg: hydrophilic, positive
        'N': [-3.5, 1, 0, 0, 0],  # Asn: hydrophilic, neutral
        'D': [-3.5, 1, 0, 1, 0],  # Asp: hydrophilic, negative
        'C': [2.5, 0, 0, 0, 0],   # Cys: hydrophobic, neutral
        'Q': [-3.5, 1, 0, 0, 0],  # Gln: hydrophilic, neutral
        'E': [-3.5, 1, 0, 1, 0],  # Glu: hydrophilic, negative
        'G': [-0.4, 0, 0, 0, 0],  # Gly: neutral
        'H': [-3.2, 1, 1, 0, 1],  # His: hydrophilic, positive, aromatic
        'I': [4.5, 0, 0, 0, 0],   # Ile: hydrophobic, neutral
        'L': [3.8, 0, 0, 0, 0],   # Leu: hydrophobic, neutral
        'K': [-3.9, 1, 1, 0, 0],  # Lys: hydrophilic, positive
        'M': [1.9, 0, 0, 0, 0],   # Met: hydrophobic, neutral
        'F': [2.8, 0, 0, 0, 1],   # Phe: hydrophobic, aromatic
        'P': [-1.6, 0, 0, 0, 0],  # Pro: neutral
        'S': [-0.8, 1, 0, 0, 0],  # Ser: hydrophilic, neutral
        'T': [-0.7, 1, 0, 0, 0],  # Thr: hydrophilic, neutral
        'W': [-0.9, 0, 0, 0, 1],  # Trp: neutral, aromatic
        'Y': [-1.3, 1, 0, 0, 1],  # Tyr: hydrophilic, aromatic
        'V': [4.2, 0, 0, 0, 0],   # Val: hydrophobic, neutral
    }
    
    # Genetic code
    genetic_code = {
        'TTT': 'F', 'TTC': 'F', 'TTA': 'L', 'TTG': 'L',
        'TCT': 'S', 'TCC': 'S', 'TCA': 'S', 'TCG': 'S',
        'TAT': 'Y', 'TAC': 'Y', 'TGT': 'C', 'TGC': 'C', 'TGG': 'W',
        'CTT': 'L', 'CTC': 'L', 'CTA': 'L', 'CTG': 'L',
        'CCT': 'P', 'CCC': 'P', 'CCA': 'P', 'CCG': 'P',
        'CAT': 'H', 'CAC': 'H', 'CAA': 'Q', 'CAG': 'Q',
        'CGT': 'R', 'CGC': 'R', 'CGA': 'R', 'CGG': 'R',
        'ATT': 'I', 'ATC': 'I', 'ATA': 'I', 'ATG': 'M',
        'ACT': 'T', 'ACC': 'T', 'ACA': 'T', 'ACG': 'T',
        'AAT': 'N', 'AAC': 'N', 'AAA': 'K', 'AAG': 'K',
        'AGT': 'S', 'AGC': 'S', 'AGA': 'R', 'AGG': 'R',
        'GTT': 'V', 'GTC': 'V', 'GTA': 'V', 'GTG': 'V',
        'GCT': 'A', 'GCC': 'A', 'GCA': 'A', 'GCG': 'A',
        'GAT': 'D', 'GAC': 'D', 'GAA': 'E', 'GAG': 'E',
        'GGT': 'G', 'GGC': 'G', 'GGA': 'G', 'GGG': 'G'
    }
    
    # Create codon to index mapping
    codons = [codon for codon, aa in genetic_code.items() if aa != '*']
    codon_to_idx = {codon: idx for idx, codon in enumerate(sorted(codons))}
    
    print(f"✅ Created mapping for {len(codon_to_idx)} codons")
    
    # Extract features and labels
    features = []
    labels = []
    
    successful_samples = 0
    
    for idx, row in df.iterrows():
        try:
            protein_seq = row['protein_sequence']
            nucleotide_seq = row['nucleotide_sequence']
            
            # Validate sequences
            if len(nucleotide_seq) % 3 != 0:
                continue

            # Extract codons (excluding stop codon)
            codons_in_seq = [nucleotide_seq[i:i+3] for i in range(0, len(nucleotide_seq), 3)]

            # Remove stop codon if present
            if len(codons_in_seq) > len(protein_seq):
                codons_in_seq = codons_in_seq[:len(protein_seq)]

            # Final validation
            if len(codons_in_seq) != len(protein_seq):
                continue
            
            # Process each position
            for pos, (aa, codon) in enumerate(zip(protein_seq, codons_in_seq)):
                # Skip invalid codons or amino acids
                if codon not in codon_to_idx:
                    continue
                if aa not in aa_properties:
                    continue

                # Verify genetic code consistency
                expected_aa = genetic_code.get(codon, 'X')
                if expected_aa != aa:
                    continue

                # Current amino acid features
                aa_props = aa_properties[aa]

                # Position features
                rel_position = pos / len(protein_seq)
                seq_length = len(protein_seq)

                # Context features (previous and next amino acids)
                prev_aa = protein_seq[pos-1] if pos > 0 else 'G'
                next_aa = protein_seq[pos+1] if pos < len(protein_seq)-1 else 'G'

                prev_props = aa_properties.get(prev_aa, [0, 0, 0, 0, 0])
                next_props = aa_properties.get(next_aa, [0, 0, 0, 0, 0])

                # Combine all features
                feature_vector = (
                    aa_props +                    # 5 features: current AA properties
                    [rel_position, seq_length] +  # 2 features: position info
                    prev_props +                  # 5 features: previous AA properties
                    next_props                    # 5 features: next AA properties
                )

                features.append(feature_vector)
                labels.append(codon_to_idx[codon])
            
            successful_samples += 1
            
            if successful_samples % 500 == 0:
                print(f"   Processed {successful_samples} samples...")
                
        except Exception as e:
            continue
    
    print(f"✅ Successfully processed {successful_samples} samples")
    print(f"✅ Created {len(features)} feature vectors")
    
    if len(features) == 0:
        return None, None, None
    
    X = np.array(features)
    y = np.array(labels)
    
    print(f"✅ Feature matrix shape: {X.shape}")
    print(f"✅ Labels shape: {y.shape}")
    print(f"✅ Number of classes: {len(np.unique(y))}")
    
    return X, y, codon_to_idx

def run_xgboost_comparison():
    """Run comprehensive XGBoost comparison"""
    print("\n🚀 XGBoost vs Deep Learning Comparison")
    print("=" * 60)
    
    if not SKLEARN_AVAILABLE:
        print("❌ Scikit-learn not available, cannot run comparison")
        return
    
    # Create features
    X, y, codon_mapping = create_traditional_ml_features()
    
    if X is None:
        print("❌ Failed to create features")
        return
    
    print(f"\n📊 Dataset Statistics:")
    print(f"   Total samples: {len(X):,}")
    print(f"   Features per sample: {X.shape[1]}")
    print(f"   Unique codons: {len(np.unique(y))}")
    
    # Data split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"\n🔄 Data Split:")
    print(f"   Training: {len(X_train):,} samples")
    print(f"   Testing: {len(X_test):,} samples")
    
    # Feature scaling
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # XGBoost Model
    print(f"\n🚀 Training XGBoost Model...")
    
    xgb_params = {
        'objective': 'multi:softmax',
        'num_class': len(np.unique(y)),
        'max_depth': 8,
        'learning_rate': 0.1,
        'n_estimators': 300,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'random_state': 42,
        'n_jobs': -1,
        'eval_metric': 'mlogloss'
    }
    
    start_time = time.time()
    xgb_model = xgb.XGBClassifier(**xgb_params)
    xgb_model.fit(X_train_scaled, y_train, verbose=False)
    xgb_train_time = time.time() - start_time
    
    # XGBoost Predictions
    y_pred_xgb = xgb_model.predict(X_test_scaled)
    xgb_accuracy = accuracy_score(y_test, y_pred_xgb)
    
    print(f"✅ XGBoost Results:")
    print(f"   Training time: {xgb_train_time:.2f} seconds")
    print(f"   Test accuracy: {xgb_accuracy:.4f} ({xgb_accuracy*100:.1f}%)")
    
    # Random Forest Comparison
    print(f"\n🌲 Training Random Forest...")
    
    rf_params = {
        'n_estimators': 200,
        'max_depth': 12,
        'min_samples_split': 5,
        'min_samples_leaf': 2,
        'random_state': 42,
        'n_jobs': -1
    }
    
    start_time = time.time()
    rf_model = RandomForestClassifier(**rf_params)
    rf_model.fit(X_train_scaled, y_train)
    rf_train_time = time.time() - start_time
    
    y_pred_rf = rf_model.predict(X_test_scaled)
    rf_accuracy = accuracy_score(y_test, y_pred_rf)
    
    print(f"✅ Random Forest Results:")
    print(f"   Training time: {rf_train_time:.2f} seconds")
    print(f"   Test accuracy: {rf_accuracy:.4f} ({rf_accuracy*100:.1f}%)")
    
    # Cross-validation for XGBoost
    print(f"\n🔄 Cross-Validation (5-fold)...")
    cv_scores = cross_val_score(xgb_model, X_train_scaled, y_train, cv=5, scoring='accuracy')
    print(f"   XGBoost CV accuracy: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    # Feature importance
    print(f"\n📈 Top 10 Most Important Features:")
    feature_names = [
        'AA_hydrophobicity', 'AA_polarity', 'AA_positive', 'AA_negative', 'AA_aromatic',
        'rel_position', 'seq_length',
        'prev_hydrophobicity', 'prev_polarity', 'prev_positive', 'prev_negative', 'prev_aromatic',
        'next_hydrophobicity', 'next_polarity', 'next_positive', 'next_negative', 'next_aromatic'
    ]
    
    importances = xgb_model.feature_importances_
    feature_importance = list(zip(feature_names, importances))
    feature_importance.sort(key=lambda x: x[1], reverse=True)
    
    for i, (name, importance) in enumerate(feature_importance[:10]):
        print(f"   {i+1:2d}. {name:<20} {importance:.4f}")
    
    # Performance comparison
    print(f"\n🏆 Model Performance Comparison:")
    print(f"{'Model':<25} {'Accuracy':<12} {'Training Time':<15} {'Status'}")
    print("-" * 70)
    print(f"{'XGBoost':<25} {xgb_accuracy*100:<11.1f}% {xgb_train_time:<14.1f}s {'✅ Complete'}")
    print(f"{'Random Forest':<25} {rf_accuracy*100:<11.1f}% {rf_train_time:<14.1f}s {'✅ Complete'}")
    print(f"{'Deep Learning (Improved)':<25} {'54.8':<11}% {'Hours':<14} {'✅ Complete'}")
    print(f"{'Deep Learning (Original)':<25} {'54.4':<11}% {'Hours':<14} {'✅ Complete'}")
    
    # Analysis
    best_traditional = max(xgb_accuracy, rf_accuracy)
    best_traditional_name = "XGBoost" if xgb_accuracy > rf_accuracy else "Random Forest"
    
    print(f"\n🔍 Analysis:")
    print(f"   Best Traditional ML: {best_traditional_name} ({best_traditional*100:.1f}%)")
    print(f"   Best Deep Learning: Improved Pipeline (54.8%)")
    
    if best_traditional > 0.548:
        print(f"   🎯 BREAKTHROUGH: Traditional ML outperforms Deep Learning!")
        print(f"   📈 Improvement: {(best_traditional - 0.548)*100:.1f} percentage points")
        print(f"   💡 Recommendation: Use {best_traditional_name} as primary model")
    elif best_traditional > 0.5:
        print(f"   ✅ COMPETITIVE: Traditional ML shows strong performance")
        print(f"   💡 Recommendation: Ensemble {best_traditional_name} + Deep Learning")
    else:
        print(f"   📊 BASELINE: Traditional ML provides good baseline")
        print(f"   💡 Recommendation: Continue with Deep Learning focus")
    
    # Save results
    results = {
        'xgboost_accuracy': float(xgb_accuracy),
        'xgboost_train_time': float(xgb_train_time),
        'random_forest_accuracy': float(rf_accuracy),
        'random_forest_train_time': float(rf_train_time),
        'cv_mean': float(cv_scores.mean()),
        'cv_std': float(cv_scores.std()),
        'best_traditional_ml': float(best_traditional),
        'best_traditional_name': best_traditional_name,
        'deep_learning_best': 0.548,
        'feature_importance': [(name, float(imp)) for name, imp in feature_importance],
        'recommendation': 'ensemble' if best_traditional > 0.5 else 'deep_learning_focus'
    }
    
    with open('xgboost_comparison_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: xgboost_comparison_results.json")
    
    return results

if __name__ == "__main__":
    try:
        results = run_xgboost_comparison()
        
        if results:
            print(f"\n🎯 FINAL SUMMARY:")
            print(f"   XGBoost: {results['xgboost_accuracy']*100:.1f}%")
            print(f"   Random Forest: {results['random_forest_accuracy']*100:.1f}%")
            print(f"   Best Traditional ML: {results['best_traditional_ml']*100:.1f}%")
            print(f"   Best Deep Learning: {results['deep_learning_best']*100:.1f}%")
            
            if results['best_traditional_ml'] > results['deep_learning_best']:
                print(f"\n🏆 SUCCESS: Traditional ML achieves better performance!")
                print(f"   Improvement: {(results['best_traditional_ml'] - results['deep_learning_best'])*100:.1f} percentage points")
            else:
                print(f"\n📊 Deep Learning maintains advantage")
                print(f"   Gap: {(results['deep_learning_best'] - results['best_traditional_ml'])*100:.1f} percentage points")
        
    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        import traceback
        traceback.print_exc()
